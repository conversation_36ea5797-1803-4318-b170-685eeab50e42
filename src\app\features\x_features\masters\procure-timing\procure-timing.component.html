<mat-toolbar color="primary" class="navbar">
  <nav aria-label="Breadcrumb navigation" style="display: flex; align-items: center; flex-wrap: wrap;">
    <a routerLink="/" class="crumb">{{'Master' | translate}}</a>
    <mat-icon>chevron_right</mat-icon>
    <a class="crumb" (click)="goBack()">{{menuService.activeMenu()?.title}}</a>
    <mat-icon>chevron_right</mat-icon>
    <span aria-current="page" class="nav-active">
      @if(uniqueId() == 0) {
      {{'New' | translate}} {{menuService.activeMenu()?.title}}
      } @else {
        {{procuretiming()?.seasonId}}
      }
    </span>
    @if(mode() === "view") {
    <button (click)="onEdit()" title="edit" class="btn-edit btn-icon-unstyled">
      <mat-icon class="material-symbols-rounded">edit_square</mat-icon>
    </button>
    }
  </nav>
  <button mat-flat-button color="primary" (click)="goBack()" aria-label="Go back" class="back-btn">
    <mat-icon>arrow_back_ios</mat-icon>
    Back
  </button>
</mat-toolbar>

  <div class="card component">

    <form [formGroup]="form" (ngSubmit)="onSubmit()">
      <div class="form">

        <div class="field">
          <label for="seasonId" class="required-label">{{'Start Time' | translate}}</label>
          <mat-form-field appearance="outline">
            <input matInput [matTimepicker]="picker1" formControlName="startTime">
            <mat-timepicker-toggle matIconSuffix [for]="picker1" />
            <mat-timepicker #picker1 />
            <mat-error>
              @if(form.controls.startTime.errors?.['required']) {
              {{'Start Time' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

         <div class="field">
          <label for="seasonId" class="required-label">{{'End Time' | translate}}</label>
          <mat-form-field appearance="outline">
            <input matInput [matTimepicker]="picker2" formControlName="endTime">
            <mat-timepicker-toggle matIconSuffix [for]="picker2" />
            <mat-timepicker #picker2 />
            <mat-error>
              @if(form.controls.endTime.errors?.['required']) {
              {{'End TIme' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="noOfBags" class="required-label">{{'NoOfBags' | translate}}</label>
          <mat-form-field>
            <input id="noOfBags" formControlName="noOfBags" matInput (input)="onInput($event,'/^[0-9]*$/')"
              maxlength="5">
            <mat-error>
              @if(form.controls.noOfBags.errors?.['required']) {
              <!-- No of bags is required -->
              {{'NoOfBags' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <!-- <div class="field">
          <label for="StartTime" class="required-label">Start Time</label>
          <mat-form-field>
            <input matInput [matDatepicker]="picker1" formControlName="StartTime">
            <mat-datepicker-toggle matIconSuffix [for]="picker1"></mat-datepicker-toggle>
            <mat-datepicker #picker1></mat-datepicker>
            <mat-error>
              @if(form.controls.StartTime.errors?.['required']) {
                Start time is required
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="EndTime" class="required-label">End Time</label>
          <mat-form-field>
            <input matInput [matDatepicker]="picker2" formControlName="EndTime">
            <mat-datepicker-toggle matIconSuffix [for]="picker2"></mat-datepicker-toggle>
            <mat-datepicker #picker2></mat-datepicker>
            <mat-error>
              @if(form.controls.EndTime.errors?.['required']) {
                End time is required
              }
            </mat-error>
          </mat-form-field>
        </div> -->



      </div>

      <div class="actions">
       
        @if(editable() === true) {
        @if(mode() === 'add') {
        <button type="button" mat-flat-button (click)="formReset()" class="btn btn-danger">{{'Reset' |
          translate}}</button>
        }
        <button [disabled]="editable() === false" mat-flat-button class="btn btn-theme" type="submit">{{uniqueId() == 0
          ? ('Create' | translate) : ('Update' | translate)}}</button>
        }
      </div>


    </form>
  </div>
