export interface ProcurementRate {
    id: number,
    price: number,
    bonusPrice: number,
    fromDate: string,
    toDate: string,
    productName: string,
    varietyName: string,
    gradeCut: number,
    seasonName: string
}

export interface ProcurementRateById{
  id: number,
  price: number,
  bonusPrice: number,
  fromDate: string,
  toDate: string,
  productId: number,
  varietyId: number,
  gradeCut: number,
  seasonId: number
}

export type ProcurementRates=ProcurementRate[]
