<div class="card component">

  <div class="page-header1">
    <nav aria-label="breadcrumb">
      <ol class="breadcrumb">
        <li class="breadcrumb-item"><a (click)="navigateBack()">{{menuService.activeMenu()?.title}}</a>
        </li>
        @if(uniqueId() == 0) {
        <li aria-current="page" class="breadcrumb-item active">{{'New' | translate}} {{menuService.activeMenu()?.title}}
        </li>
        } @else {
        <li aria-current="page" class="breadcrumb-item active"> {{formData()?.cropName}}</li>
        }
      </ol>
    </nav>

    @if(uniqueId() == 0) {
    <h1>{{'New' | translate}} {{menuService.activeMenu()?.title}}</h1>
    } @else {
    <h1>
      {{formData()?.cropName}}


    </h1>
    }
  </div>



  <div class="grid-container">
    <div class="row-8">
      Trailer Details

      <div class="grid-container">
        <div class="row-6">
          <div class="grid-container gap">
            <div class="row-6"><label class="txt-aln">Sort</label></div>
            <div class="row-6"><label class="value-aln">THIRUCHIRAPPALLI</label></div>
          </div>
          <div class="grid-container gap">
            <div class="row-6"><label class="txt-aln">Moisture (%)</label></div>
            <div class="row-6"><label class="value-aln">Chennai</label></div>
          </div>
          <div class="grid-container gap">
            <div class="row-6"><label class="txt-aln">No of Bags</label></div>
            <div class="row-6"><label class="value-aln">THAYANOOR</label></div>
          </div>
          <div class="grid-container gap">
            <div class="row-6"><label class="txt-aln">Quantity</label></div>
            <div class="row-6"><label class="value-aln">SRIRANGAM GDN </label></div>
          </div>
        </div>

        <div class="row-6">
          <div class="grid-container gap">
            <div class="row-6"><label class="txt-aln">Region</label></div>
            <div class="row-6"><label class="value-aln">THIRUCHIRAPPALLI</label></div>
          </div>
          <div class="grid-container gap">
            <div class="row-6"><label class="txt-aln">Taulk</label></div>
            <div class="row-6"><label class="value-aln">Chennai</label></div>
          </div>
          <div class="grid-container gap">
            <div class="row-6"><label class="txt-aln">From</label></div>
            <div class="row-6"><label class="value-aln">THAYANOOR</label></div>
          </div>
          <div class="grid-container gap">
            <div class="row-6"><label class="txt-aln">To</label></div>
            <div class="row-6"><label class="value-aln">SRIRANGAM GDN </label></div>
          </div>
          <div class="grid-container gap">
            <div class="row-6"><label class="txt-aln">Truck Number</label></div>
            <div class="row-6"><label class="value-aln">TS01DS4562</label></div>
          </div>
          <div class="grid-container gap">
            <div class="row-6"><label class="txt-aln">Paddy Type</label></div>
            <div class="row-6"><label class="value-aln">PADDY-A</label></div>
          </div>
          <div class="grid-container gap">
            <div class="row-6"><label class="txt-aln">Gunny Type</label></div>
            <div class="row-6"><label class="value-aln">NEW BALE </label></div>
          </div>
          <div class="grid-container gap">
            <div class="row-6"><label class="txt-aln">Moisture (%)</label></div>
            <div class="row-6"><label class="value-aln">2487867687</label></div>
          </div>
          <div class="grid-container gap">
            <div class="row-6"><label class="txt-aln">Tare Weight / Per Bag</label></div>
            <div class="row-6"><label class="value-aln">2487867687</label></div>
          </div>
          <div class="grid-container gap">
            <div class="row-6"><label class="txt-aln">Gross Weight</label></div>
            <div class="row-6"><label class="value-aln">2487867687</label></div>
          </div>
          <div class="grid-container gap">
            <div class="row-6"><label class="txt-aln">Net Weight</label></div>
            <div class="row-6"><label class="value-aln">2487867687</label></div>
          </div>
          <div class="grid-container gap">
            <div class="row-6"><label class="txt-aln">Received Weight</label></div>
            <div class="row-6"><label class="value-aln">2487867687</label></div>
          </div>
          <div class="grid-container gap">
            <div class="row-6"><label class="txt-aln">Number of Bags</label></div>
            <div class="row-6"><label class="value-aln">2487867687</label></div>
          </div>
        </div>

      </div>

    </div>
    <div class="row-4">
      Acknowledged Value
      <div class="grid-container">
      

        <div class="row-12">
          <form [formGroup]="form" (ngSubmit)="onSubmit()">
            <!-- <div class="form">
            <div class="field"> -->
            <label for="GrossWeightKg" class="required-label">Number of Bags</label>
            <mat-form-field>
              <input id="GrossWeightKg" formControlName="GrossWeightKg" matInput
                (input)="onInput($event,'/^[0-9A-Za-z ]*$/')" maxlength="100" [readonly]="!acceptAcknowlege()">
              <mat-error>
                @if(form.controls.GrossWeightKg.errors?.['required']) {
                {{'Gross Weight in Kgs' | translate}} {{'IsRequired' | translate}}
                }
              </mat-error>
            </mat-form-field>



            <div class="actions" style="margin:40px">
              <button type="button" mat-flat-button (click)="navigateBack()" class="btn btn-secondary">{{'Back' |
                translate}}</button>
              <button type="button" mat-flat-button (click)="formReset()" class="btn btn-danger">{{'Reset' |
                translate}}</button>
              <button [disabled]="editable() === false" mat-flat-button class="btn btn-theme" type="submit">{{uniqueId()
                == 0 ?
                ('Submit' | translate) : ('Submit' | translate)}}</button>
            </div>

            <!-- </div>
            </div> -->
          </form>
        </div>


      </div>
    </div>
  </div>





  <!-- 
    <form [formGroup]="form" (ngSubmit)="onSubmit()">
      <div class="form">

        <div class="field">
          <label for="cropName" class="required-label">{{'Crop' | translate }}</label>
          <mat-form-field>
            <input id="cropName" formControlName="cropName" matInput (input)="onInput($event,'/^[0-9A-Za-z ]*$/')"
              maxlength="100">
            <mat-error>
              @if(form.controls.cropName.errors?.['required']) {
              {{'Crop' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="seasonId" class="required-label">{{'Season' | translate }}</label>
          <mat-form-field appearance="outline">
            <mat-select id="seasonId" formControlName="seasonId">
              <mat-form-field class="select-search hide-subscript" appearance="outline">
                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                <input #search autocomplete="off" matInput placeholder="Search" (input)="filter($event)">
              </mat-form-field>
              @for (role of filteredTaluks(); track role) {
              <mat-option [value]="role.key">{{role.value}}</mat-option>
              }
            </mat-select>
            <mat-error>
              @if(form.controls.seasonId.errors?.['required']) {
              {{'Season' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="fromDate" class="required-label">{{'FromDate' | translate }}</label>
          <mat-form-field>
            <input matInput id="fromDate" formControlName="fromDate" [matDatepicker]="picker1">
            <mat-datepicker-toggle matIconSuffix [for]="picker1"></mat-datepicker-toggle>
            <mat-datepicker #picker1></mat-datepicker>
            <mat-error>
              @if(form.controls.fromDate.errors?.['required']) {
              {{'FromDate' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="toDate" class="required-label">{{'ToDate' | translate }}</label>
          <mat-form-field>
            <input matInput id="toDate" formControlName="toDate" [matDatepicker]="picker2" [min]="minToDate()">
            <mat-datepicker-toggle matIconSuffix [for]="picker2"></mat-datepicker-toggle>
            <mat-datepicker #picker2></mat-datepicker>
            <mat-error>
              @if(form.controls.toDate.errors?.['required']) {
              {{'ToDate' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

      </div>

      <div class="actions">
        <button type="button" mat-flat-button (click)="navigateBack()" class="btn btn-secondary">{{'Back' |
          translate}}</button>
        @if(editable() === true) {
        @if(mode() === 'add') {
        <button type="button" mat-flat-button (click)="formReset()" class="btn btn-danger">{{'Reset' |
          translate}}</button>
        }
        <button [disabled]="editable() === false" mat-flat-button class="btn btn-theme" type="submit">{{uniqueId() == 0 ?
          ('Create' | translate) : ('Update' | translate)}}</button>
        }
      </div>
    </form> -->
</div>