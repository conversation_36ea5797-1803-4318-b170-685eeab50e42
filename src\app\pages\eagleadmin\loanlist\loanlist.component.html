<div class="container">
    <mat-card class="table-section">
        <mat-card-header>
            <mat-card-title>
                <mat-icon>people</mat-icon>
                Employee List
            </mat-card-title>
            <div class="header-actions">
                <button mat-icon-button (click)="refreshData()" [disabled]="isLoading" matTooltip="Refresh Data">
                    <mat-icon>refresh</mat-icon>
                </button>
            </div>
        </mat-card-header>

        <mat-card-content>
            <div *ngIf="isLoading" class="loading-container">
                <mat-spinner></mat-spinner>
                <p>Loading employees...</p>
            </div>
            <div *ngIf="!isLoading && dataSource.length > 0" class="table-container">
                <table mat-table [dataSource]="dataSource" class="mat-elevation-2 full-width-table" matSort>
                    <!-- S.No Column -->
                    <ng-container matColumnDef="position">
                        <th mat-header-cell *matHeaderCellDef mat-sort-header>S.No</th>
                        <td mat-cell *matCellDef="let element; let i = index">{{ i + 1 }}</td>
                    </ng-container>
                    <!-- Matrix ID Column -->
                    <ng-container matColumnDef="empId">
                        <th mat-header-cell *matHeaderCellDef mat-sort-header>Employee ID </th>
                        <td mat-cell *matCellDef="let element">{{ element.empId }}</td>
                    </ng-container>
                    <ng-container matColumnDef="employeeName">
                        <th mat-header-cell *matHeaderCellDef mat-sort-header>Employee Name</th>
                        <td mat-cell *matCellDef="let element">{{ element.employeeName }}</td>
                    </ng-container>
                    <ng-container matColumnDef="cadrePayLevel">
                        <th mat-header-cell *matHeaderCellDef mat-sort-header>CadrePayLevel</th>
                        <td mat-cell *matCellDef="let element">{{ element.cadrePayLevel }}</td>
                    </ng-container>
                      <ng-container matColumnDef="department">
                        <th mat-header-cell *matHeaderCellDef mat-sort-header>Department</th>
                        <td mat-cell *matCellDef="let element">{{ element.department }}</td>
                    </ng-container>
                   
                      <ng-container matColumnDef="loanType">
                        <th mat-header-cell *matHeaderCellDef mat-sort-header>loan Type</th>
                        <td mat-cell *matCellDef="let element">{{ element.loanType }}</td>
                    </ng-container>
                     <ng-container matColumnDef="loanAmount">
                        <th mat-header-cell *matHeaderCellDef mat-sort-header>loan Amount</th>
                        <td mat-cell *matCellDef="let element">{{ element.loanAmount }}</td>
                    </ng-container>
                    <ng-container matColumnDef="flowStatusName">
                        <th mat-header-cell *matHeaderCellDef mat-sort-header>Approved by</th>
                        <td mat-cell *matCellDef="let element">{{ element.flowStatusName }}</td>
                    </ng-container>
                   

                    <ng-container matColumnDef="Action">
                        <th mat-header-cell *matHeaderCellDef>Action</th>
                        <td mat-cell *matCellDef="let element">
                            <!-- <button mat-icon-button color="warn"
                                (click)="deleteMatrix(element.employeeRole, element.level)" matTooltip="Delete">
                                <mat-icon>delete</mat-icon>
                            </button> -->
                            <button mat-icon-button color="warn">
                               <!-- (click)="updateEmployee(element.employeeRole, element.level)" -->
                                <mat-icon>edit</mat-icon>
                            </button>
                        </td>
                    </ng-container>
                    <tr mat-header-row
                        *matHeaderRowDef="['position', 'employeeName','cadrePayLevel','department','loanType','loanAmount','flowStatusName']"></tr>
                    <tr mat-row
                        *matRowDef="let row; columns: ['position', 'employeeName','cadrePayLevel','department','loanType','loanAmount','flowStatusName'];">
                    </tr>
                </table>
            </div>



        </mat-card-content>
    </mat-card>
</div>