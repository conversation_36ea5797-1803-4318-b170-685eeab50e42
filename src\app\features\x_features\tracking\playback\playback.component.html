<div class="row text-left">
    <div class="card">


        <div class="titlecard">
            <div class="vehiclenum">
                <h3 style="margin-left: 20px !important;">
                    <!-- Vehicle Reg. No : <b>{{regNo}}</b> -->
                    TN123456
                </h3>
            </div>
            <div class="address">
                <h3>Chennai </h3>
            </div>
            <div class="backactions">
                <p class="text-right pr-2 cuurlen " style="margin-bottom:0px">{{currLength}}/{{dataLength}}</p>
                &nbsp;&nbsp;
                <button [disabled]="currLength === 1" type="button" matTooltip="{{'Backward'|translate}}"
                    matTooltipClass="tooltip-edit" matTooltipPosition="above" class="btnrefresh" (click)="backward()">
                    <!-- <i class="fa fa-angle-left"></i> -->
                    <mat-icon class="material-symbols-rounded">keyboard_arrow_left</mat-icon>
                </button>
                <button [disabled]="currLength === dataLength" type="button" matTooltip="{{'Forward'|translate}}"
                    matTooltipClass="tooltip-edit" matTooltipPosition="above" class="btnrefresh" (click)="forward()">
                    <!-- <i class="fa fa-angle-right"> </i> -->
                    <mat-icon class="material-symbols-rounded">keyboard_arrow_right</mat-icon>

                </button>
                <button type="button" matTooltip="{{'Refresh'|translate}}" matTooltipClass="tooltip-edit"
                    matTooltipPosition="above" class="btnrefresh" (click)="refresh()">
                    <!-- <i class="fa fa-refresh"> </i> -->
                    <mat-icon class="material-symbols-rounded">refresh</mat-icon>

                </button>
                <button type="button" matTooltip="{{'Cancel'|translate}}" matTooltipClass="tooltip-edit"
                    matTooltipPosition="above" class="btnrefresh" (click)="onCancel()">
                    <!-- <i class="fa fa-times"> </i> -->
                    <mat-icon class="material-symbols-rounded">close</mat-icon>

                </button>
            </div>
        </div>
        <div class="mapcard">
            <div class="col-12" id="mapId" #mapId style="display:block;position: relative; height: 30rem;">
                <h1>map</h1>
                <div style="height: 30rem;" class="map_icons_sec sidebtns">
                    <span class="" matTooltip="{{'Recenter'|translate}}" matTooltipClass="tooltip-edit"
                        matTooltipPosition="right" (click)="recenter()" style="margin: 10px;">
                        <a><img src="/images/tracking/resender.png" alt="resender"></a>
                    </span>
                    <span class="play" matTooltip="{{'Play'|translate}}" matTooltipClass="tooltip-edit"
                        matTooltipPosition="right" *ngIf="ispause" (click)="play()" style="margin: 10px;"><a> <img
                                src="/images/tracking/play.png" alt="play"></a>
                    </span>
                    <span class="play" matTooltip="{{'Pause'|translate}}" matTooltipClass="tooltip-edit"
                        matTooltipPosition="right" *ngIf="!ispause" (click)="pause()" style="margin: 10px;"><a> <img
                                src="/images/tracking/pause.png" alt="pause"></a>
                    </span>
                    <span class="" matTooltip="{{'Stop'|translate}}" matTooltipClass="tooltip-edit"
                        matTooltipPosition="right" (click)="stop()" style="margin: 10px;"><a><img
                                src="/images/tracking/stop.png" alt="stop"></a>
                    </span>
                    <span class="forward" matTooltip="{{'Fast Forward'|translate}}" matTooltipClass="tooltip-edit"
                        matTooltipPosition="right" (click)="fastforward()" style="margin: 10px;"><a>{{
                            fastforwardInterval }}x</a></span>
                </div>
            </div>
            <div class="map_chartsec" *ngIf="isPlay">
                <div class="speedometer plr-8 mb-10">
                    <div class="center g-detail text-center">
                        <ngx-gauge [value]="gaugeValue" label="Speed" append="Kmph" [thresholds]="thresholdConfig"
                            [thick]="10" [duration]="1000" [backgroundColor]="bgColor" [min]="0" [max]="100">
                        </ngx-gauge>
                    </div>
                    <div class="row col-12 ml-1 time_odometer">
                        <p>{{devicetime | date: 'dd-MM-yyyy hh:mm:ss a'}}</p>
                    </div>
                </div>
            </div>
        </div>


        <div class="detailcards">
            <div class=" col-md-2 m-0 p-1">
                <div class="details">
                    <div class="detailheader">Start Time</div>
                    <div class="detailBody">
                        <span>{{PlayBackDetails()?.startTs | date: 'dd-MM-yyyy'}}</span>
                        <span>{{PlayBackDetails()?.startTs | date: 'hh:mm a'}}</span>

                    </div>
                </div>
                <div class="details">
                    <div class="detailheader">End Time</div>
                    <div class="detailBody">
                        <span>{{PlayBackDetails()?.endTs | date: 'dd-MM-yyyy'}}</span>
                        <span>{{PlayBackDetails()?.endTs | date: 'hh:mm a'}}</span>
                    </div>
                </div>
            </div>
            <div class="col-md-2 m-0 p-1">
                <div class="details">
                    <div class="detailheader">Duration</div>
                    <div class="detailBody">
                        <span class="datetime">
                            <span class="timefont">{{PlayBackDetails()?.duration?.item1}}</span>&nbsp; day
                            &nbsp;&nbsp;
                            <span class="timefont">{{PlayBackDetails()?.duration?.item2}}</span>&nbsp; hrs
                            &nbsp;&nbsp;
                            <span class="timefont">{{PlayBackDetails()?.duration?.item3}}</span>&nbsp; mins
                        </span>
                    </div>
                </div>

                <div class="details">
                    <div class="detailheader">Total Run Time</div>
                    <div class="detailBody">
                        <span class="datetime">
                            <span class="timefont">{{PlayBackDetails()?.runTime?.item2}}</span>&nbsp; hrs
                            &nbsp;&nbsp;
                            <span class="timefont">{{PlayBackDetails()?.runTime?.item3}}</span>&nbsp; mins
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-md-3 m-0 p-1">
                <div class="details">
                    <div class="detailheader">Total Stopped Time</div>
                    <div class="detailBody">
                        <span class="datetime">
                            <span class="timefont">{{PlayBackDetails()?.idleTime?.item2}}</span>&nbsp; hrs
                            &nbsp;&nbsp;
                            <span class="timefont">{{PlayBackDetails()?.idleTime?.item2}}</span> &nbsp;mins
                        </span>
                    </div>
                </div>
                <div class="details">
                    <div class="detailheader">Total Unreached Time</div>
                    <div class="detailBody">
                        <span class="datetime">
                            <span class="timefont">{{PlayBackDetails()?.stoppedTime?.item1}}</span>&nbsp;day
                            &nbsp;&nbsp;
                            <span class="timefont">{{PlayBackDetails()?.stoppedTime?.item2}}</span>
                            &nbsp;hrs
                            &nbsp;&nbsp;
                            <span class="timefont">{{PlayBackDetails()?.stoppedTime?.item3}}</span>
                            &nbsp;mins
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-md-3 m-0 p-1">
                <div class="details">
                    <div class="detailheader alignment">
                        <span>Speed</span>
                        <span>Kmph</span>
                    </div>
                    <div class="detailBody ">
                        <div class="col-12 alignment">
                            <span>Avg Speed</span>
                            <span>{{PlayBackDetails()?.avgSpeed}}</span>
                        </div>
                        <div class="col-12 alignment">
                            <span>Max Speed</span>
                            <span></span>
                        </div>
                    </div>
                </div>
                <div class="details">
                    <div class="detailheader">Distance </div>
                    <div class="detailBody">
                        <span class="datetime">
                            <span class="timefont">{{PlayBackDetails()?.distance}}</span>&nbsp;Km
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-md-2 m-0 p-1" style="padding-right: calc(var(--bs-gutter-x)* .5);">
                <div class="details" style="height: 102%;">
                    <div class="detailheader " style="padding-top: 10px;">Legend</div>
                    <div class="detailBody">
                        <div class="col-12" style="padding-top: 5px;padding-bottom: 5px;">
                            <span
                                style="background-color: rgb(115, 255, 0); display: inline-block; width: 15px; height: 10px"></span>&nbsp;&nbsp;
                            <span>Less than 15 Km</span>
                        </div>
                        <div class="col-12" style="padding-top: 5px;padding-bottom: 5px;">
                            <span
                                style="background-color: rgb(255, 102, 0); display: inline-block; width: 15px; height: 10px"></span>&nbsp;&nbsp;
                            <span>Greater than 15 Km less than 30Km</span>
                        </div>
                        <div class="col-12" style="padding-top: 5px;padding-bottom: 5px;">
                            <span
                                style="background-color: rgb(255, 0, 0); display: inline-block; width: 15px; height: 10px"></span>&nbsp;&nbsp;
                            <span>Greater than 30 Km</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>