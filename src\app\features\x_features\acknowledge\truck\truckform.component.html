<div class="card component">

  <div class="page-header1">
    <nav aria-label="breadcrumb">
      <ol class="breadcrumb">
        <li class="breadcrumb-item"><a (click)="navigateBack()">{{menuService.activeMenu()?.title}}</a>
        </li>
        @if(uniqueId() == 0) {
        <li aria-current="page" class="breadcrumb-item active">{{'New' | translate}} {{menuService.activeMenu()?.title}}
        </li>
        } @else {
        <li aria-current="page" class="breadcrumb-item active"> {{formData()?.cropName}}</li>
        }
      </ol>
    </nav>

    @if(uniqueId() == 0) {
    <h1>{{'New' | translate}} {{menuService.activeMenu()?.title}}</h1>
    } @else {
    <h1>
      {{formData()?.cropName}}


    </h1>
    }
  </div>



  <div class="grid-container">
    <div class="row-8">
      Truck Details

      <div class="grid-container">
        <div class="row-6">
          <div class="grid-container gap">
            <div class="row-6"><label class="txt-aln">From</label></div>
            <div class="row-6"><label class="value-aln">DPC</label></div>
          </div>
          <div class="grid-container gap">
            <div class="row-6"><label class="txt-aln">To</label></div>
            <div class="row-6"><label class="value-aln">Godown</label></div>
          </div>
          <div class="grid-container gap">
            <div class="row-6"><label class="txt-aln">Date</label></div>
            <div class="row-6"><label class="value-aln">10-06-2025</label></div>
          </div>
          <div class="grid-container gap">
            <div class="row-6"><label class="txt-aln">Vehicle</label></div>
            <div class="row-6"><label class="value-aln">THGYT</label></div>
          </div>
          <div class="grid-container gap">
            <div class="row-6"><label class="txt-aln">Truck Memo No</label></div>
            <div class="row-6"><label class="value-aln">2487867687</label></div>
          </div>
        </div>


        <div class="row-6">
          <div class="grid-container gap">
            <div class="row-6"><label class="txt-aln">Sort</label></div>
            <div class="row-6"><label class="value-aln">2487867687</label></div>
          </div>
          <div class="grid-container gap">
            <div class="row-6"><label class="txt-aln">Variety</label></div>
            <div class="row-6"><label class="value-aln">2487867687</label></div>
          </div>
          <div class="grid-container gap">
            <div class="row-6"><label class="txt-aln">Gunny Type</label></div>
            <div class="row-6"><label class="value-aln">2487867687</label></div>
          </div>
          <div class="grid-container gap">
            <div class="row-6"><label class="txt-aln">Tare Weight(in Kgs.)</label></div>
            <div class="row-6"><label class="value-aln">2487867687</label></div>
          </div>
          <div class="grid-container gap">
            <div class="row-6"><label class="txt-aln">Moisture (%)</label></div>
            <div class="row-6"><label class="value-aln">2487867687</label></div>
          </div>
          <div class="grid-container gap">
            <div class="row-6"><label class="txt-aln">Total Bags</label></div>
            <div class="row-6"><label class="value-aln">2487867687</label></div>
          </div>
          <div class="grid-container gap">
            <div class="row-6"><label class="txt-aln">Gross Weight(in Kgs.)</label></div>
            <div class="row-6"><label class="value-aln">2487867687</label></div>
          </div>
          <div class="grid-container gap">
            <div class="row-6"><label class="txt-aln">Net Weight(in Kgs.)</label></div>
            <div class="row-6"><label class="value-aln">2487867687</label></div>
          </div>
        </div>

      </div>

    </div>
    <div class="row-4">
      Acknowledged Value
      <div class="grid-container">
        <div class="row-12"><label class="txt-aln"><mat-checkbox class="example-margin" [(ngModel)]="acceptAcknowlege" (change)="onCheckboxChange($event)"></mat-checkbox>Select if
            Discrepancy in Weight</label></div>

        <div class="row-12">
          <form [formGroup]="form" (ngSubmit)="onSubmit()">
            <!-- <div class="form">
            <div class="field"> -->
            <label for="GrossWeightKg" class="required-label">Gross Weight in Kgs</label>
            <mat-form-field>
              <input id="GrossWeightKg" formControlName="GrossWeightKg" matInput
                (input)="onInput($event,'/^[0-9A-Za-z ]*$/')" maxlength="100" [readonly]="!acceptAcknowlege()">
              <mat-error>
                @if(form.controls.GrossWeightKg.errors?.['required']) {
                {{'Gross Weight in Kgs' | translate}} {{'IsRequired' | translate}}
                }
              </mat-error>
            </mat-form-field>

            <label for="GrossWeightMt" class="required-label">Gross Weight in MTs</label>
            <mat-form-field>
              <input id="GrossWeightMt" formControlName="GrossWeightMt" matInput readonly>
            </mat-form-field>

            <label for="NetWeightKg" class="required-label">Net Weight in Kgs</label>
            <mat-form-field>
              <input id="NetWeightKg" formControlName="NetWeightKg" matInput readonly>
            </mat-form-field>

            <label for="NetWeightMt" class="required-label">Net Weight in MTs</label>
            <mat-form-field>
              <input id="NetWeightMt" formControlName="NetWeightMt" matInput readonly>
            </mat-form-field>

            <div class="actions" style="margin:40px">
              <button type="button" mat-flat-button (click)="navigateBack()" class="btn btn-secondary">{{'Back' |
                translate}}</button>
              <button type="button" mat-flat-button (click)="formReset()" class="btn btn-danger">{{'Reset' |
                translate}}</button>
              <button [disabled]="editable() === false" mat-flat-button class="btn btn-theme" type="submit">{{uniqueId()
                == 0 ?
                ('Submit' | translate) : ('Submit' | translate)}}</button>
            </div>

            <!-- </div>
            </div> -->
          </form>
        </div>


      </div>
    </div>
  </div>





  <!-- 
    <form [formGroup]="form" (ngSubmit)="onSubmit()">
      <div class="form">

        <div class="field">
          <label for="cropName" class="required-label">{{'Crop' | translate }}</label>
          <mat-form-field>
            <input id="cropName" formControlName="cropName" matInput (input)="onInput($event,'/^[0-9A-Za-z ]*$/')"
              maxlength="100">
            <mat-error>
              @if(form.controls.cropName.errors?.['required']) {
              {{'Crop' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="seasonId" class="required-label">{{'Season' | translate }}</label>
          <mat-form-field appearance="outline">
            <mat-select id="seasonId" formControlName="seasonId">
              <mat-form-field class="select-search hide-subscript" appearance="outline">
                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                <input #search autocomplete="off" matInput placeholder="Search" (input)="filter($event)">
              </mat-form-field>
              @for (role of filteredTaluks(); track role) {
              <mat-option [value]="role.key">{{role.value}}</mat-option>
              }
            </mat-select>
            <mat-error>
              @if(form.controls.seasonId.errors?.['required']) {
              {{'Season' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="fromDate" class="required-label">{{'FromDate' | translate }}</label>
          <mat-form-field>
            <input matInput id="fromDate" formControlName="fromDate" [matDatepicker]="picker1">
            <mat-datepicker-toggle matIconSuffix [for]="picker1"></mat-datepicker-toggle>
            <mat-datepicker #picker1></mat-datepicker>
            <mat-error>
              @if(form.controls.fromDate.errors?.['required']) {
              {{'FromDate' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="toDate" class="required-label">{{'ToDate' | translate }}</label>
          <mat-form-field>
            <input matInput id="toDate" formControlName="toDate" [matDatepicker]="picker2" [min]="minToDate()">
            <mat-datepicker-toggle matIconSuffix [for]="picker2"></mat-datepicker-toggle>
            <mat-datepicker #picker2></mat-datepicker>
            <mat-error>
              @if(form.controls.toDate.errors?.['required']) {
              {{'ToDate' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

      </div>

      <div class="actions">
        <button type="button" mat-flat-button (click)="navigateBack()" class="btn btn-secondary">{{'Back' |
          translate}}</button>
        @if(editable() === true) {
        @if(mode() === 'add') {
        <button type="button" mat-flat-button (click)="formReset()" class="btn btn-danger">{{'Reset' |
          translate}}</button>
        }
        <button [disabled]="editable() === false" mat-flat-button class="btn btn-theme" type="submit">{{uniqueId() == 0 ?
          ('Create' | translate) : ('Update' | translate)}}</button>
        }
      </div>
    </form> -->
</div>