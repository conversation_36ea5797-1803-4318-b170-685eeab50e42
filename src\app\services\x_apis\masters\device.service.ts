import { inject, Injectable } from '@angular/core';
import { DataService } from '../../data.service';
import { Response } from '../../../models/x_models/api-response';
import { DeviceById, Devices } from '../../../models/x_models/masters/device';

@Injectable({
  providedIn: 'root'
})
export class DeviceService {

  dataService = inject(DataService)

  create(data:DeviceById) {
      return this.dataService.post<Response>("/device", data)
  }

  get() {
      return this.dataService.get<Devices>("/device")
  }

  getById(id: number) {
      return this.dataService.get<DeviceById>(`/device/${id}`)
  }

  delete(id: number) {
      return this.dataService.delete<Response>(`/device/${id}`)
  }


}
