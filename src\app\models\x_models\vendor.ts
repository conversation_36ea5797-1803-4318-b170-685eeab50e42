export interface Vendor {
    id: number
    name: string
    regionalName: string
    authPersonName: string
    email: string
    mobileNo: string
    whatsAppNo: string
    vendorType: number
}



export interface Huller {
    id: number
    GSTNumber:string
    LegalBusinessName: string
    uin: string
    DateofRegistration: string
    StatusofGSTIN: string
    NatureofBusiness:string
    LandlineNumberofCompany: string
    ContactPersonName: string
    Address: string

    FSSAI: string
    MSME:string
    AgmarkLicense: string
    ContactEmail:string
    OwnerName: string
    MobileNo: string
    Region:  number
    Block:  number
    Village:  number
    Pincode: string

    BankAccountNumber: string
    IFSCCode: string
    BankName: string
    Branch: string
    Commodity:  number

    ContractID: string
    ContractStartDate: string
    ContractEndDate: string
    ContractDoc: string

    MillName: string
    MillOwnerName: string
    MillType:string
    MillCode:string
    Latitude:string
    Longitude: string
    AdhaarNo: string
    FactoryLicenseNo: string
    FactoryLicenseUpto: string
    HullingMonthlyCapacityinMTs: string
    IsGeneratorAvailable: string
    IsSeparateEBAvailable:string
    EBConsumptionNoBoiler: string
    TypeofEBServiceOffice: string
    BoilerLicenseNo: string
    MRMType: string
    AgreementUpto: string
    SecurityDepositValue:string
    BankGuaranteeValue:string
    BGNumber:string
    BGValidUpTo: string
    GenModel: string
    EBConsumptionNoMill: string
    TypeofEBServiceBoiler: string
    BoilerLicenseUpto: string
    DateOfAppointment: string
    Capacity: string
    TypeofEBServiceMill: string
    EBConsumptionNoOffice: string
}

export interface Vendors {
    id: number
    name: string
    regionalName: string
    authPersonName: string
    email: string
    mobileNo: string
    whatsAppNo: string
    vendorTypeName: string
}

export type VendorResponse = Vendors[]