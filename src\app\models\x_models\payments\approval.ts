export interface Approval {
    id: number
    cropName: string
    fromDate: string
    toDate: string
    seasonId: number | null
}
export interface Approvals {
    // id:number
    // purchaseTxnRefNo: string
    // purchaseTs: string
    // totalQty: number
    // mspAmount: number
    // incentiveAmount: number
    // rateCutAmount: number
    // paymentAmount:number
    // dpcName: string
    // farmerName: string
    // tokenNo: string
    // authTxnTypeName: string
    // moisturePercentage:number
    // noOfBags: number

    id: number
    purchaseTxnRefNo: string
    purchaseTs: string
    totalQty: number
    mspAmount: number
    incentiveAmount:number
    rateCutAmount: number
    paymentAmount: number
    tokenNo: string
    authTxnTypeName: string
    moisturePercentage: number
    noOfBags: number

    dpcNameCode: string
    farmerNameCode: string
    villageNameCode: string
    seasonName: string
    varietyName: string
    ddswCut:number
    issCut: number
    moistureCut:number
   
}

export type ApprovalResponse = Approvals[]