import { Component, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, FormArray, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import {
  Region,
  Department,
  Employee,
  Position,
  District,
  SubDistrict,
  Location,
  InterRegionTransferRequest,
  TransferType,
  TransferStatus,
  Priority,
  FamilyMember
} from '../../../enums/m_enums/transfer.model';
import { TransferDataService } from '../../../services/m_apis/transfer-data.service';
import { CommonModule } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDividerModule } from '@angular/material/divider';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { Router } from '@angular/router';

@Component({
  selector: 'app-inter-region',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    MatFormFieldModule,
    MatSelectModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatProgressBarModule,
    MatProgressSpinnerModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatDividerModule,
    MatSlideToggleModule,
    MatSnackBarModule
  ],
  templateUrl: './inter-region.component.html',
  styleUrls: ['./inter-region.component.scss']
})
export class InterRegionComponent implements OnInit {

  transferForm!: FormGroup;
  regions: Region[] = [];
  availableRegions: Region[] = [];
  availableDepartments: Department[] = [];
  availablePositions: Position[] = [];
  districts: District[] = [];
  subDistricts: SubDistrict[] = [];
  locations: Location[] = [];
  transferTypes: { value: string; label: string }[] = [];
  currentEmployee: Employee | null = null;
  isLoading = false;
  minDate = new Date();

  constructor(
    private fb: FormBuilder,
    private transferService: TransferDataService,
    private snackBar: MatSnackBar,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.initializeForm();
    this.loadData();
  }

  private initializeForm(): void {
    this.transferForm = this.fb.group({
      // Current Information (readonly)
      employeeName: ['', Validators.required],
      employeeCode: ['', Validators.required],
      currentRegion: ['', Validators.required],
      currentDepartment: ['', Validators.required],
      currentPosition: ['', Validators.required],
      currentGrade: ['', Validators.required],
      // Additional employee fields
      gender: [''],
      mobileNumber: [''],
      email: [''],
      location: [''],
      dateOfBirth: [''],
      designation: [''],
      fatherName: [''],
      empIdOrEcpfNo: [''],

      // Transfer Request Details
      proposedRegion: ['', Validators.required],
      proposedDepartment: [''],
      proposedPosition: [''],
      priority: ['MEDIUM', Validators.required],
      preferredJoiningDate: [''],

      // Transfer Details
      transferType: ['', Validators.required],
      district: ['', Validators.required],
      subDistrict: ['', Validators.required],
      location: ['', Validators.required],

      // Relocation Support
      relocationRequired: [false],
      estimatedRelocationDate: [''],
      currentAccommodationType: [''],
      preferredAccommodationType: [''],
      relocationRequirements: [''],

      // Family Information
      familyMembers: this.fb.array([]),

      // Reason and Justification
      reason: ['', [Validators.required, Validators.minLength(10)]],
      justification: ['']
    });
  }

  get familyMembers(): FormArray {
    return this.transferForm.get('familyMembers') as FormArray;
  }

  private loadData(): void {
    this.isLoading = true;

    // Load current employee data
    this.transferService.getCurrentEmployee().subscribe(employee => {
      this.currentEmployee = employee;
      this.populateCurrentEmployeeData(employee);
    });

    // Load regions
    this.transferService.getRegions().subscribe(regions => {
      this.regions = regions;
      // Filter out current region from available regions
      this.availableRegions = regions.filter(r => r.id !== this.currentEmployee?.region);
    });

    // Load transfer types
    this.transferService.getTransferTypes().subscribe(types => {
      this.transferTypes = types;
    });

    // Load districts
    this.transferService.getDistricts().subscribe(districts => {
      this.districts = districts;
      this.isLoading = false;
    });
  }

  private populateCurrentEmployeeData(employee: Employee): void {
    const currentRegion = this.regions.find(r => r.id === employee.region);

    this.transferForm.patchValue({
      employeeName: employee.name,
      employeeCode: employee.employeeCode,
      currentRegion: currentRegion?.name || employee.region,
      currentDepartment: employee.department,
      currentPosition: employee.position,
      currentGrade: employee.currentGrade,
      // Additional employee fields
      gender: employee.gender,
      mobileNumber: employee.mobileNumber,
      email: employee.email,
      location: employee.location,
      dateOfBirth: employee.dateOfBirth ? employee.dateOfBirth.toLocaleDateString() : '',
      designation: employee.designation,
      fatherName: employee.fatherName,
      empIdOrEcpfNo: employee.empIdOrEcpfNo
    });
  }

  onRegionChange(event: any): void {
    const regionId = event.value;
    if (regionId) {
      this.transferService.getDepartmentsByRegion(regionId).subscribe(departments => {
        this.availableDepartments = departments;
        this.transferForm.get('proposedDepartment')?.setValue('');
        this.transferForm.get('proposedPosition')?.setValue('');
        this.availablePositions = [];
      });
    } else {
      this.availableDepartments = [];
      this.availablePositions = [];
    }
  }

  onDepartmentChange(event: any): void {
    const departmentId = event.value;
    if (departmentId) {
      this.transferService.getPositionsByDepartment(departmentId).subscribe(positions => {
        this.availablePositions = positions;
        this.transferForm.get('proposedPosition')?.setValue('');
      });
    } else {
      this.availablePositions = [];
    }
  }

  onDistrictChange(event: any): void {
    const districtId = event.value;
    if (districtId) {
      this.transferService.getSubDistrictsByDistrict(districtId).subscribe(subDistricts => {
        this.subDistricts = subDistricts;
        this.transferForm.get('subDistrict')?.setValue('');
        this.transferForm.get('location')?.setValue('');
        this.locations = [];
      });
    } else {
      this.subDistricts = [];
      this.locations = [];
    }
  }

  onSubDistrictChange(event: any): void {
    const subDistrictId = event.value;
    if (subDistrictId) {
      this.transferService.getLocationsBySubDistrict(subDistrictId).subscribe(locations => {
        this.locations = locations;
        this.transferForm.get('location')?.setValue('');
      });
    } else {
      this.locations = [];
    }
  }

  onRelocationToggle(event: any): void {
    const isRequired = event.checked;
    if (isRequired) {
      this.transferForm.get('estimatedRelocationDate')?.setValidators([Validators.required]);
      this.transferForm.get('currentAccommodationType')?.setValidators([Validators.required]);
      this.transferForm.get('preferredAccommodationType')?.setValidators([Validators.required]);
    } else {
      this.transferForm.get('estimatedRelocationDate')?.clearValidators();
      this.transferForm.get('currentAccommodationType')?.clearValidators();
      this.transferForm.get('preferredAccommodationType')?.clearValidators();
      // Clear family members if relocation is not required
      this.familyMembers.clear();
    }

    this.transferForm.get('estimatedRelocationDate')?.updateValueAndValidity();
    this.transferForm.get('currentAccommodationType')?.updateValueAndValidity();
    this.transferForm.get('preferredAccommodationType')?.updateValueAndValidity();
  }

  addFamilyMember(): void {
    const familyMemberForm = this.fb.group({
      name: ['', Validators.required],
      relationship: ['', Validators.required],
      age: ['', [Validators.required, Validators.min(0), Validators.max(100)]],
      occupation: [''],
      schoolingRequired: [false]
    });

    this.familyMembers.push(familyMemberForm);
  }

  removeFamilyMember(index: number): void {
    this.familyMembers.removeAt(index);
  }

  onSubmit(): void {
    if (this.transferForm.valid && this.currentEmployee) {
      this.isLoading = true;

      const formData = this.transferForm.value;
      const proposedRegion = this.availableRegions.find(r => r.id === formData.proposedRegion);
      const proposedDept = this.availableDepartments.find(d => d.id === formData.proposedDepartment);
      const proposedPos = this.availablePositions.find(p => p.id === formData.proposedPosition);

      // Prepare family members data
      const familyDetails: FamilyMember[] = formData.familyMembers.map((member: any) => ({
        name: member.name,
        relationship: member.relationship,
        age: member.age,
        occupation: member.occupation,
        schoolingRequired: member.schoolingRequired
      }));

      const transferRequest: InterRegionTransferRequest = {
        id: '',
        requestNumber: '',
        employeeId: this.currentEmployee.id,
        employeeName: this.currentEmployee.name,
        transferType: TransferType.INTER_REGION,
        status: TransferStatus.DRAFT,
        priority: formData.priority as Priority,

        // Current Details
        currentRegionId: this.currentEmployee.region || '',
        currentRegionName: this.transferForm.get('currentRegion')?.value,
        currentDepartmentId: this.currentEmployee.department || '',
        currentDepartmentName: this.transferForm.get('currentDepartment')?.value,
        currentPosition: this.currentEmployee.position,
        currentGrade: this.currentEmployee.currentGrade || '',

        // Proposed Details
        proposedRegionId: formData.proposedRegion,
        proposedRegionName: proposedRegion?.name || '',
        proposedDepartmentId: formData.proposedDepartment || '',
        proposedDepartmentName: proposedDept?.name || '',
        proposedPosition: proposedPos?.title || '',
        proposedGrade: proposedPos?.grade || '',

        // Transfer Details
        reason: formData.reason,
        justification: formData.justification,
        preferredJoiningDate: formData.preferredJoiningDate,
        relocationRequired: formData.relocationRequired,
        familyDetails: familyDetails.length > 0 ? familyDetails : undefined,

        // Audit Fields
        submittedBy: this.currentEmployee.name,
        createdDate: new Date(),
        createdBy: this.currentEmployee.name
      };

      this.transferService.submitInterRegionTransfer(transferRequest).subscribe({
        next: (response) => {
          this.isLoading = false;
          if (response.success) {
            this.snackBar.open(
              response.message || 'Inter-region transfer request submitted successfully!',
              'Close',
              { duration: 5000, panelClass: ['success-snackbar'] }
            );
            this.router.navigate(['/transfer-dashboard']);
          } else {
            this.snackBar.open(
              response.message || 'Failed to submit transfer request',
              'Close',
              { duration: 5000, panelClass: ['error-snackbar'] }
            );
          }
        },
        error: (error) => {
          this.isLoading = false;
          console.error('Error submitting transfer request:', error);
          this.snackBar.open(
            'An error occurred while submitting the request',
            'Close',
            { duration: 5000, panelClass: ['error-snackbar'] }
          );
        }
      });
    } else {
      this.markFormGroupTouched();
      this.snackBar.open(
        'Please fill in all required fields correctly',
        'Close',
        { duration: 3000, panelClass: ['warning-snackbar'] }
      );
    }
  }

  resetForm(): void {
    this.transferForm.reset();
    this.availableDepartments = [];
    this.availablePositions = [];
    this.subDistricts = [];
    this.locations = [];
    this.familyMembers.clear();
    if (this.currentEmployee) {
      this.populateCurrentEmployeeData(this.currentEmployee);
    }
    this.transferForm.get('priority')?.setValue('MEDIUM');
    this.transferForm.get('relocationRequired')?.setValue(false);
  }

  getErrorMessage(fieldName: string): string {
    const field = this.transferForm.get(fieldName);
    if (field?.hasError('required')) {
      return `${this.getFieldDisplayName(fieldName)} is required`;
    }
    if (field?.hasError('minlength')) {
      const minLength = field.errors?.['minlength']?.requiredLength;
      return `${this.getFieldDisplayName(fieldName)} must be at least ${minLength} characters`;
    }
    return '';
  }

  private getFieldDisplayName(fieldName: string): string {
    const fieldNames: { [key: string]: string } = {
      'reason': 'Reason for transfer',
      'proposedRegion': 'Proposed region',
      'priority': 'Priority level'
    };
    return fieldNames[fieldName] || fieldName;
  }

  private markFormGroupTouched(): void {
    Object.keys(this.transferForm.controls).forEach(key => {
      const control = this.transferForm.get(key);
      control?.markAsTouched();

      if (control instanceof FormArray) {
        control.controls.forEach(arrayControl => {
          if (arrayControl instanceof FormGroup) {
            Object.keys(arrayControl.controls).forEach(nestedKey => {
              arrayControl.get(nestedKey)?.markAsTouched();
            });
          }
        });
      }
    });
  }
}
