import { inject, Injectable } from '@angular/core';
import { DataService } from '../../data.service';
import { Response } from '../../../models/x_models/api-response';
import { Dpcvillagemapping, dpcvillageMappingList } from '../../../models/x_models/masters/dpcvillagemapping';

@Injectable({
  providedIn: 'root'
})
export class DpcvillagemappingService {


  dataService = inject(DataService)

  create(data: any) {
    return this.dataService.post<Response>("/dpcvillagemapping", data)
  }

  get() {
    return this.dataService.get<dpcvillageMappingList>("/dpcvillagemapping")
  }

  getById(id: number) {
    return this.dataService.get<Dpcvillagemapping>(`/dpcvillagemapping/${id}`)
  }

  delete(id: number) {
    return this.dataService.delete<Response>(`/dpcvillagemapping/${id}`)
  }

}
