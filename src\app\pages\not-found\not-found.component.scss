.page-content {
    display: flex;
    flex-direction: column;
    /* align-content: center; */
    align-items: center;
    justify-content: center;
    height: -webkit-fill-available;

}

.page-wrapper {
    display: grid;
    height: -webkit-fill-available;
    // align-items: center;
    // justify-content: center;
    // height: 100vh;
}

.main-wrapper {
    width: 100%;
    padding: 20px;
    height: -webkit-fill-available;
}

.img-fluid {
    max-width: 50%;
}

@media (max-width: 600px) {
    .img-fluid {
        max-width: 100%;
    }
}

.type{
    font-weight: 700 !important;
    color: #686868 !important;
    font-size: 80px !important;
    margin-top: .5rem !important;
}
.title{
    font-weight: bolder !important;
    margin-bottom: .5rem !important;
}

.text-muted{color: #686868 !important;
    text-align: center !important;
    margin-bottom: 1rem !important;}


.abtn{
    color: white;
    background: #203664;
    padding: 10px;
    border-radius: 5px;
}