@use '@angular/material' as mat;

:root {


  @include mat.form-field-overrides((outlined-outline-width: 1.5px,
      outlined-focus-outline-width: 1.5px,
      outlined-container-shape: 4px,
      outlined-caret-color: var(--grey-100),
      //outlined-focus-outline-color: var(--mdc-outlined-text-field-foucs-outline-color),
      outlined-input-text-color: var(--grey-80),
      outlined-disabled-input-text-color: var(--grey-100),
      outlined-input-text-placeholder-color: var(--grey-80),
      outlined-error-caret-color: var(--grey-100),

      outlined-outline-color: var(--grey-20),
      outlined-disabled-outline-color: transparent,
      //outlined-hover-outline-color: var(--theme-40),
      disabled-input-text-placeholder-color: var(--grey-80),

      error-text-color: var(--grey-40),

      container-text-line-height: 40px,
      container-text-size: 14px,

      container-text-tracking: "0.00938em",
      container-height: 40px,
      container-vertical-padding: 0px,
    ));

}

.search-form-field {
  @include mat.form-field-overrides((outlined-container-shape: 25px,
      container-text-line-height: 2.8,
      container-text-size: 10px,
      container-height: 28px,
    ));

  .search-icon {
    width: 10.9px;
    color: var(--theme-80)
  }
}

.mat-mdc-form-field {
  --form-field-bg: #fff;
  --form-field-disabled-bg: var(--grey-10);
  --mdc-outlined-text-field-hover-outline-color: var(--grey-40);
  --mdc-outlined-text-field-focus-outline-color: var(--grey-40);
  max-width: 260px;
  width: 100%;

  .mdc-text-field--outlined {
    padding-inline: 12px;

    .mat-mdc-notch-piece {
      background-color: var(--form-field-bg)
    }

    &:not(.mdc-text-field--disabled) {
      &.mdc-text-field--focused .mat-mdc-notch-piece {
        border-color: var(--mdc-outlined-text-field-focus-outline-color);
      }

      &:not(.mdc-text-field--focused):hover .mat-mdc-notch-piece {
        border-color: var(--mdc-outlined-text-field-hover-outline-color);
      }
    }
  }

  // .mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mat-mdc-notch-piece {
  //     border-color: var(--theme-40);
  // }

  // .mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mat-mdc-notch-piece {
  //     border-color: var(--grey-40);
  // }

  .mdc-text-field--outlined.mdc-text-field--disabled .mat-mdc-notch-piece {
    background-color: var(--form-field-disabled-bg);

    &.mdc-notched-outline__leading {
      border-right: none
    }

    &.mdc-notched-outline__trailing {
      border-left: none
    }
  }

  //.mdc-text-field--outlined.mdc-text-field--disabled .mdc-text-field__input {
  //  --mdc-outlined-text-field-disabled-input-text-color
  //}

  .mat-mdc-form-field-hint {
    color: var(--grey-40);
    font-size: 12px;
    margin-top: 5px;
    margin-left: -2px;

    &::before {
      display: none;
    }
  }

  .mat-mdc-form-field-error {
    color: var(--red-80);
    font-size: 10px;

    text-align: right;
    padding-right: 5px;
  }

  .mat-mdc-form-field-subscript-wrapper {
    top: 3px;
    left: 3px;
    margin-top: -3px;
    // display: none;
  }





  .mat-mdc-form-field-hint-wrapper,
  .mat-mdc-form-field-error-wrapper {
    padding: 0px;
  }

  // vartical space between control and hint
  .mat-mdc-form-field-bottom-align::before {
    height: 14px;
  }
}

.mat-drawer-end .mat-mdc-form-field {
  max-width: 100%;
}


.ng-pristine.ng-untouched .mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid .mat-mdc-notch-piece {
  border-color: var(--grey-20);
}

.ng-pristine.ng-untouched .mat-mdc-form-field-error-wrapper {
  display: none;
}

.mdc-text-field--outlined.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--focused):hover .mdc-notched-outline .mat-mdc-notch-piece {
  border-color: var(--red-80);
}
.ng-pristine.ng-untouched .mdc-text-field--outlined.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--focused):hover .mdc-notched-outline .mat-mdc-notch-piece {
  border-color: var(--grey-40);
}

label {
  font-size: 14px;
  font-weight: 400;
  margin-bottom: 5px;
  display: block;
  line-height: 16px;
}

.mat-mdc-form-field-infix {
  z-index: 1;
}


input::placeholder {
  font-size: 14px;
}

.mat-mdc-form-field-icon-suffix {
  padding: 0 7px 0 4px !important;
}
.mat-mdc-form-field-icon-suffix {
  height: 40px;
  display: flex;
  align-items: center;
}

.hide-subscript .mat-mdc-form-field-subscript-wrapper {
  display: none;
}

.mat-mdc-form-field-text-prefix {
  z-index: 1;
}

.icon-search {
  width: 15px;
  height: 15px;
  padding-inline: 17px 13px;
  color: var(--grey-40);
  font-variation-settings: "FILL" 1, "wght" 200, "GRAD" 0, "opsz" 24;
}
