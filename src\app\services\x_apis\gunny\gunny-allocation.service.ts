import { inject, Injectable } from '@angular/core';
import { DataService } from '../../data.service';
import { GunnyAllocationResponse, GunnyAllocations } from '../../../models/x_models/gunny/gunny-allocation';


@Injectable({
    providedIn: 'root'
})
export class GunnyAllocationService {
    dataService = inject(DataService)

    create(data: GunnyAllocations) {
        return this.dataService.post<Response>("/procurement", data)
    }

    get() {
        return this.dataService.get<GunnyAllocationResponse>("/procurement")
    }

    getById(id: number) {
        return this.dataService.get<GunnyAllocations>(`/procurement/${id}`)
    }

    delete(id: number) {
        return this.dataService.delete<Response>(`/procurement/${id}`)
    }
}
