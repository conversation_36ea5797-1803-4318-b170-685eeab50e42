import { Component, CUSTOM_ELEMENTS_SCHEMA, ElementRef, inject, Input, input, signal, ViewChild } from '@angular/core';
// import { Component, ElementRef, inject, input, Optional, ViewChild} from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Vehicles } from '../../../../models/x_models/tracking/livetrack';
import { DatePipe } from '@angular/common';
import { NgxGaugeModule } from 'ngx-gauge';

declare var L: any;

@Component({
  selector: 'app-livetrack-map',
  imports: [
    TranslateModule, 
    DatePipe,
    NgxGaugeModule
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './livetrack-map.component.html',
  styleUrl: './livetrack-map.component.scss'
})
export class LivetrackMapComponent {
  // vehicleDetails = input.required<any>();
  // vehicleDetails = input<any[]>();
  // vehicleDetails = input.required<any>()
  vehicleData = input<Vehicles>();

  
  mapId = input.required<any>();
  mapIds= signal<number>(0);

  // for speedometer
  gaugeValue = 75; // Current value
  gaugeLabel = 'Speed';
  gaugeAppendText = 'km/h';
  thresholdConfig = {
    '0': { color: 'green' },
    '40': { color: 'orange' },
    '75': { color: 'red' },
  };
  bgColor = '#f0f0f0'; // Background color

  vehicleDetails = signal<Vehicles>({
    id: 1,
    mainPowerStatusType: 1,
    regNo: "TN123456",
    regionName: "SALEM",
    vehicleId: 1,
    vehicleStatusType: 3,
    vehicleStatusTypeName: "Unreachable",
    gpsDeviceTs: "2025-02-25T16:11:12"
  });

  translate = inject(TranslateService);
  mapview: any = L.tileLayer('http://{s}.google.com/vt/lyrs=m&x={x}&y={y}&z={z}', { maxZoom: 19, subdomains: ['mt0', 'mt1', 'mt2', 'mt3'] });
  terrainview: any = L.tileLayer('http://{s}.google.com/vt/lyrs=p&x={x}&y={y}&z={z}', { maxZoom: 19, subdomains: ['mt0', 'mt1', 'mt2', 'mt3'] });
  satelliteview: any = L.tileLayer('http://{s}.google.com/vt/lyrs=s&x={x}&y={y}&z={z}', { maxZoom: 19, subdomains: ['mt0', 'mt1', 'mt2', 'mt3'] });

  @ViewChild('mapId', { static: true }) mapcontainer!: ElementRef;
  map: any;

  ngOnInit() {
    this.mapIds.set(this.mapId())
console.log('mapId',this.mapId())
    this.loadMap();
  }

  loadMap() {
    const center = [11.0827, 77.8707];
    const baseMaps = {
      'Map': this.mapview,
      'Terrain': this.terrainview,
      'Satellite': this.satelliteview,
    };
    this.map = L.map(this.mapcontainer.nativeElement, {
      fullscreenControl: {
        pseudoFullscreen: false,
        position: 'topleft',
      },
    }, { minZoom: 2, tilt: true }).setView(center, 7, { heading: 100.0, tilt: 10.0 });
    L.control.layers(baseMaps).addTo(this.map);
    this.mapview.addTo(this.map);

    // this.markerClusterGroup = L.markerClusterGroup({
    //   chunkedLoading: true,
    //   showCoverageOnHover: false,
    //   maxClusterRadius: 50, // Adjust cluster radius as needed
    //   disableClusteringAtZoom: 17
    // });

    // this.map.addLayer(this.markerClusterGroup);

    // fixes a map loading issue
    setTimeout(() => {
      window.dispatchEvent(new Event('resize'));
    }, 10);
  }

  navigateBack() {

  }

  // Vehicle Details
  getVehicleStatusImage(status: number): string {
    switch (status) {
      case 1:
        return 'images/tracking/connected.png';
      case 2:
        return 'images/tracking/disconnected.png';
      default:
        return 'images/tracking/notconnected.png';
    }
  }

  getVehicleStatusAltText(status: number): string {
    switch (status) {
      case 1:
        return 'Connected';
      case 2:
        return 'Disconnected';
      default:
        return 'Not Connected';
    }
  }
}
