@if (showGrid()) {
<div class="component card">

    <div class="page-header">
        <h1>{{'VAOApproval' | translate}}</h1>
    </div>

<form [formGroup]="form">
    
        <div class="header">
            <div class="field" style="width: 25%;margin: 10px 0 10px;">
                <label for="villageId">{{'Village' | translate}}</label>
                <mat-form-field appearance="outline">
                    <mat-select id="villageId" formControlName="villageId" >
                        <mat-form-field class="select-search hide-subscript" appearance="outline">
                            <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                            <input #search autocomplete="off" matInput placeholder="Search"
                                (input)="filter($event)">
                        </mat-form-field>
                        <mat-option [value]="0">{{'All Village'}}</mat-option>

                        @for (village of filteredVillages(); track village) {
                        <mat-option [value]="village.key">{{village.value}}</mat-option>
                        }
                    </mat-select>
                </mat-form-field>
            </div>
    
    
            <div class="card-container" style="margin-bottom:20px">
                @for (card of vehicleCards(); track card) {
                <div class="card1" [class.shadow-primary]="card.status==ApproveStatus.Pending"
                    [class.shadow-success]="card.status==ApproveStatus.Approved"
                    [class.shadow-danger]="card.status==ApproveStatus.Rejected"
                    [class.shadow-warning]="card.status==ApproveStatus.PartiallyApproved"
                    [class.selected-pending]="card.status==ApproveStatus.Pending && card.status == selectedStatus()"
                    [class.selected-approved]="card.status==ApproveStatus.Approved && card.status == selectedStatus()"
                    [class.selected-rejected]="card.status==ApproveStatus.Rejected && card.status == selectedStatus()"
                    [class.selected-partial]="card.status==ApproveStatus.PartiallyApproved && card.status == selectedStatus()"
                    (click)="filterCards(card.status)">
                    <div class="card-content">
                        <div class="row-8">
                            <div class="text-content">
                                <mat-card-title style="font-size:18px;">{{ card.title }}</mat-card-title>
                                <p style="font-size:25px;font-weight:bold;text-align: center;">{{ card.total }}</p>
                            </div>
                        </div>
                        <!-- <div class="row-4">
                            <img class="card-image" [src]="card.imageUrl" [alt]="card.title">
                        </div> -->
                    </div>
                </div>
                }
            </div>
    
    
    
            <div class="filters-wrapper" style="margin-bottom:20px">
                <mat-form-field class="search hide-subscript" appearance="outline">
                    <mat-icon class="icon-search material-symbols-outlined" matPrefix>search</mat-icon>
                    <input id="search" formControlName="search" (input)="onSearch()" autocomplete="off" matInput
                        placeholder="{{ 'Search' | translate }}">
                </mat-form-field>
                <div class="filters-more">
                    <button (click)="onRefresh()" class="btn btn-filter" mat-icon-button>
                        <mat-icon class="icon-filter material-symbols-outlined" matPrefix>sync</mat-icon>
                    </button>
                </div>
            </div>
    
    
        </div>
    
</form>


    <div class="content">
        <div class="table-wrapper">

            <table mat-table [dataSource]="dataSource()" matSort class="mat-elevation-z8">

                <ng-container matColumnDef="farmerCode">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>{{'Farmer Name (Code)' | translate }} </th>
                    <td mat-cell *matCellDef="let row"> {{row.farmerNameCode}} </td>
                </ng-container>



                <ng-container matColumnDef="farmerName">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'FarmerName' | translate }}</th>
                    <td mat-cell *matCellDef="let row"> {{row.farmerName}} </td>
                </ng-container>



                <ng-container matColumnDef="mobileNo">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'MobileNo' | translate }}</th>
                    <td mat-cell *matCellDef="let row"> {{row.mobileNo}} </td>
                </ng-container>

                <ng-container matColumnDef="noOfLands">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'ApprovedLand' | translate }}</th>
                    <td mat-cell *matCellDef="let row"> {{row.noOfLands}} </td>
                </ng-container>

                <ng-container matColumnDef="village">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'Village Name' | translate }}</th>
                    <td mat-cell *matCellDef="let row"> {{row.villageName}} </td>
                </ng-container>


                <ng-container matColumnDef="date">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'Registered Date' | translate }}</th>
                    <td mat-cell *matCellDef="let row"> {{row.registeredTs | date: 'dd-MM-yyyy'}} </td>
                </ng-container>


                <ng-container matColumnDef="vaoApprovalStatusTypeName">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'Status' | translate }}</th>
                    <td mat-cell *matCellDef="let row"> {{row.vaoApprovalStatusTypeName}} </td>
                </ng-container>

                <ng-container matColumnDef="actions">
                    <th mat-header-cell *matHeaderCellDef class="action"> {{ 'Actions' | translate }} </th>
                    <td mat-cell *matCellDef="let row">
                        <div class="table-controls">
                            @if (access()?.canView) {
                            <button title="{{ 'Image' | translate }}" (click)="onView(row)"
                                class="btn-icon btn-icon-unstyled ">
                                <mat-icon class="material-symbols-rounded">visibility</mat-icon>
                            </button>
                            }

                        </div>
                    </td>
                </ng-container>

                <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
                <tr class="mat-row" *matNoDataRow>
                    <td class="mat-cell" [attr.colspan]="displayedColumns.length" style="text-align: center;padding:10px">
                      No data found
                    </td>
                  </tr>
            </table>

        </div>
        <!-- @if (dataSource()?.filteredData?.length==0) {
        <div style="text-align: center;">No Data</div>

        } -->
        <mat-paginator class="paginator" pageSize="10" [pageSizeOptions]="[5, 10, 50,100]"
            aria-label="Select page of users"></mat-paginator>

        <div class="mobile-wrapper">
            <div class="un-cards">
                @for (item of dataSource().data; track item) {
                <div class="un-card">
                    <div class="desc">
                        <div class="quote-no">{{item.farmerNameCode}}</div>
                        <div class="quote-no">{{item.mobileNo}}&nbsp;-&nbsp;{{item.noOfLands }}</div>
                    </div>
                    <div class="actions">
                        <button mat-icon-button class="btn btn-actions" [matMenuTriggerFor]="menu"
                            aria-label="Card actions">
                            <mat-icon>more_vert</mat-icon>
                        </button>
                        <mat-menu #menu="matMenu">

                            @if (access()?.canView) {
                            <button (click)="onView(item)" mat-menu-item>
                                <mat-icon class="material-symbols-rounded">visibility</mat-icon>
                                <span>{{ 'View' | translate }}</span>
                            </button>
                            }

                        </mat-menu>
                    </div>
                </div>
                }
            </div>
        </div>


    </div>
</div>
} @else{

<app-land-approval [farmerId]="farmerId()" [villageId]="form.value.villageId" [farmerName]="farmerName()" (closed)="onClose()"></app-land-approval>
}