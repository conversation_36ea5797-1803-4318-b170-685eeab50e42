import Swal from 'sweetalert2';

export async function confirmAndDelete(
  entity: any,
  uniqueId: any,
  entityName: any,
  service: { delete: (id: number) => Promise<any> },
  refreshFn: () => void
) {
  const title = 'Delete Confirmation';
  const txt = `Are you sure you want to delete ${entityName} - ${uniqueId} ? `;
  const Yes = 'Yes';
  const No = 'No';

  const swalRes = await Swal.fire({
    title,
    text: txt,
    icon: 'warning',
    showCancelButton: true,
    confirmButtonText: Yes,
    cancelButtonText: No,
    customClass: {
      confirmButton: 'btn-primary',
      cancelButton: 'btn-secondary',
    },
  });

  if (swalRes.isConfirmed) {
    const res = await service.delete(entity.id);
    if (res.isSuccess) {
      refreshFn();
    }
  }
}


