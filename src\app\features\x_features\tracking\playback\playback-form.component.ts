import { DatePipe } from '@angular/common';
import { Component, computed, inject, resource, signal } from '@angular/core';
import { <PERSON><PERSON>uilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButton } from '@angular/material/button';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatFormField, MatInput, MatError } from '@angular/material/input';
import { MatRadioModule } from '@angular/material/radio';
import { MatOption, MatSelect } from '@angular/material/select';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { DateInputDirective } from '../../../../directives/date-input.directive';
import { AlertService } from '../../../../services/alert.service';
import { InputformatService } from '../../../../services/x_apis/inputformat.service';
import { LookupService } from '../../../../services/x_apis/lookup.service';
import { PlaybackService } from '../../../../services/x_apis/tracking/playback.service';
import { Router } from '@angular/router';
import { LookUpResponse } from '../../../../models/x_models/lookup';
import { VehicleService } from '../../../../services/x_apis/masters/vehicle.service';
import { PlaybackComponent } from './playback.component';

@Component({
  selector: 'app-playback-form',
  imports: [
    FormsModule,
    MatFormField,
    MatInput,
    ReactiveFormsModule,
    MatError,
    MatOption,
    MatSelect,
    MatButton,
    MatIconModule,
    MatDatepickerModule,
    DateInputDirective,
    MatNativeDateModule,
    MatRadioModule,
    MatDatepickerModule,
    TranslateModule,
    MatFormFieldModule,
    PlaybackComponent
  ],
  providers: [DatePipe],
  templateUrl: './playback-form.component.html',
  styleUrl: './playback-form.component.scss'
})
export class PlaybackFormComponent {
  lookupService = inject(LookupService);
  playbackService = inject(PlaybackService);
  vehicleService = inject(VehicleService);
  alertService = inject(AlertService);
  inputFormatService = inject(InputformatService);
  translate = inject(TranslateService);
  fb = inject(FormBuilder);
  router = inject(Router);

  regions = resource({ loader: () => this.lookupService.getRegion() }).value;
  vehicle = signal<LookUpResponse[]>([]);
  showForm = signal(false)
  form = this.fb.group({
    range: this.fb.control<number>(1, Validators.required),
    Date: this.fb.control<string>('', null),
    fromDate: this.fb.control<string>('', null),
    toDate: this.fb.control<string>('', null),
    vehicleId: this.fb.control<number | null>(null, Validators.required),
    regionId: this.fb.control<number | null>(null, Validators.required),
    vehicleIdList:this.fb.control<string>('', null),
  });

  ngOnInit() {

    //route call
    this.getVehicle(0);
    //
    this.form.get("regionId")?.valueChanges.subscribe(res => {
      this.getVehicle(res);
    });
    this.form.get('range')?.valueChanges.subscribe(res => {
      this.onRangeChange(res);
    });
  }

  readonly search = signal('');

  readonly filteredvehicle = computed(() =>
    this.vehicle()?.filter(vehicle =>
      vehicle.value.toLowerCase().includes(this.search().toLowerCase())
    )
  );

  filterVehicles(value: any) {
    this.search.set(value.target.value);
  }

  async getVehicle(regionId: any) {
    const res = await this.vehicleService.lookup(regionId)
    this.vehicle.set(res)
  }

  onRangeChange(value: any) {
    if (value === 1) {
      console.log('By Date selected');
      // Add your logic here
    } else if (value === 2) {
      console.log('By Range selected');
      // Add your logic here
    }
  }

  async onSubmit() {
    debugger
    if (this.form.valid) {
      const formValue = { ...this.form.value };
      formValue.fromDate = this.formatDateToISOString(formValue.fromDate);
      formValue.toDate = this.formatDateToISOString(formValue.toDate);
      this.showForm.set(true)
    } else {
      this.form.markAllAsTouched();
    }
  }


  formReset() {
    // this.form.reset();
    this.form.reset({
      range: 1  // Set the default selected radio button (e.g., "By Date")
    });
  }

  formatDateToISOString(date: any): string | null {
    if (!date) return null;
    const parsedDate = new Date(date);
    return parsedDate.toISOString();
  }

  onClose() {
    this.showForm.set(false)
    this.form.reset({
      range: 1  // Set the default selected radio button (e.g., "By Date")
    });
  }
}
