import { NgT<PERSON>plateOutlet, DatePipe, CurrencyPipe } from '@angular/common';
import { Component, computed, effect, inject, linkedSignal, resource, signal, viewChild } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckbox } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInput, MatPrefix } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatOption, MatSelect } from '@angular/material/select';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { CloseFilterOnBlurDirective } from '../../../../directives/close-filter-on-blur.directive';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { confirmAndDelete } from '../../../../models/x_models/masters/shared';
import { StatusType } from '../../../../enums/x_enums/dotnet/status-type';
import { ProcurePricecutComponent } from './procure-pricecut.component';
import { ProcurePricecuts } from '../../../../models/x_models/masters/procure-pricecut';
import { ProcurePricecutService } from '../../../../services/x_apis/masters/procure-pricecut.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatToolbarModule } from '@angular/material/toolbar';

@Component({
    selector: 'app-procure-pricecuts',
    imports: [MatIcon,
        MatIcon,
        MatInput,
        MatPrefix,
        ReactiveFormsModule,
        MatCheckbox,
        MatPaginatorModule,
        MatTableModule,
        NgTemplateOutlet,
        MatButtonModule,
        MatMenuModule,
        DatePipe,
        MatSortModule,
        TranslateModule,
        MatToolbarModule,
        MatOption,
        MatSelect,
        CloseFilterOnBlurDirective, CurrencyPipe, MatFormFieldModule, ProcurePricecutComponent],
    templateUrl: './procure-pricecuts.component.html',
    styleUrl: './procure-pricecuts.component.scss'
})
export class ProcurePricecutsComponent {
    menuService = inject(MenuService);
    access = computed(() => this.menuService.activeMenu()?.controlAccess);
    translate = inject(TranslateService);

    paginator = viewChild<MatPaginator>(MatPaginator);
    sort = viewChild<MatSort>(MatSort);

    procurePricecutService = inject(ProcurePricecutService);
    procurePricecutResource = resource({ loader: () => this.procurePricecutService.get() });
    dataSource = linkedSignal(() => new MatTableDataSource(this.procurePricecutResource.value()));

    loading = signal(false);
    mode = signal<'view' | 'edit' | 'add'>('add');
    search = new FormControl("");
    showGrid = signal(false);
    status = StatusType;

    list = signal<ProcurePricecuts>({
        id: 0,
        procurementPriceCutTypeName: "",
        productName: "",
        seasonName: "",
        regionName: "",
        minRange: 0,
        maxRange: 0,
        priceCutAmount: 0
    });

    displayedColumns: string[] = [
        'procurementPriceCutName',
        'productName',
        'seasonName',
        'regionName',
        'minRange',
        'maxRange',
        'priceCutAmount',
        'actions'
    ];

    constructor() {
        effect(() => {
            if (this.paginator()) {
                this.dataSource().sort = this.sort()!;
                this.dataSource().paginator = this.paginator()!
            }
        });
    }

    ngOnInit(): void {
    }

    onAdd() {
        this.list.set({
            id: 0,
            procurementPriceCutTypeName: "",
            productName: "",
            seasonName: "",
            regionName: "",
            minRange: 0,
            maxRange: 0,
            priceCutAmount: 0
        });
        this.mode.set("add")
        this.showGrid.set(true)
    }

    onView(rowData: ProcurePricecuts) {
        this.list.set(rowData);
        this.mode.set('view');
        this.showGrid.set(true)
    }

    onEdit(rowData: ProcurePricecuts) {
        this.list.set(rowData);
        this.mode.set('edit');
        this.showGrid.set(true)
    }

    onClose() {
        this.showGrid.set(false)
        this.procurePricecutResource.reload()
        this.search.setValue("");
    }

    onRefresh() {
        this.procurePricecutResource.reload()
        this.search.setValue("");
    }

    async onDelete(rowData: ProcurePricecuts) {
        await confirmAndDelete(rowData, rowData.procurementPriceCutTypeName, 'Procurement Price Cut', this.procurePricecutService, () => this.procurePricecutResource.reload());
    }

    onSearch() {
        const filterValue = this.search.value?.trim().toLowerCase() || '';
        this.dataSource().filter = filterValue;
    }

}
