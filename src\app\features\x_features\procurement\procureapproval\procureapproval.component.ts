import { NgTemplateOutlet, DatePipe } from '@angular/common';
import { ChangeDetectorRef, Component, computed, CUSTOM_ELEMENTS_SCHEMA, effect, inject, linkedSignal, OnInit, QueryList, resource, signal, viewChild, ViewChildren } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, FormsModule, NgModel, ReactiveFormsModule, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckbox, MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInput, MatPrefix } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { StatusType } from '../../../../enums/x_enums/dotnet/status-type';
import { AppRoutes } from '../../../../enums/app-routes';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { SelectionModel } from '@angular/cdk/collections';
import { Approvals } from '../../../../models/x_models/payments/approval';
import { ProcureapprovalService } from '../../../../services/x_apis/procurement/procureapproval.service';
import Swal from 'sweetalert2';
import { AlertService } from '../../../../services/alert.service';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatTabsModule } from '@angular/material/tabs';
@Component({
  selector: 'app-procureapproval',
  imports: [MatIcon,
    MatInput,
    MatPrefix,
    ReactiveFormsModule,
    MatPaginatorModule,
    MatTableModule,
    NgTemplateOutlet,
    MatButtonModule,
    MatMenuModule,
    DatePipe,
    MatSortModule,
    MatFormFieldModule,
    MatCheckbox,
    FormsModule,
    ReactiveFormsModule,
    MatCheckboxModule,
    MatExpansionModule,
    MatTabsModule,
    TranslateModule],
  providers: [DatePipe],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './procureapproval.component.html',
  styleUrl: './procureapproval.component.scss'
})
export class ProcureapprovalComponent {
  menuService = inject(MenuService);
  access = computed(() => this.menuService.activeMenu()?.controlAccess);
  translate = inject(TranslateService);
  alertService = inject(AlertService);
  paginator = viewChild<MatPaginator>(MatPaginator);
  sort = viewChild<MatSort>(MatSort);
  approvalService = inject(ProcureapprovalService);
  approvalResource = resource({
    loader: async () => {
      const data = await this.approvalService.get();
      return data.map(item => ({ ...item, isSkipPayment: false, reason: '', showError: false }));
    },
  });
  dataSource = linkedSignal(() => new MatTableDataSource(this.approvalResource.value()));
  loading = signal(false);
  submitted = signal<boolean>(false);
  mode = signal<'view' | 'edit' | 'add'>('add');
  search = new FormControl("");
  showGrid = signal(false);
  status = StatusType;
  selection = new SelectionModel<any>(true, []);
  fb = inject(FormBuilder);

  tabId= signal<number>(1);

  @ViewChildren('reason') reasonControls!: QueryList<NgModel>; 

  tabs = [
    { label: 'Tab 1', content: 'Content 1' },
    { label: 'Tab 2', content: 'Content 2' }
  ];
  
  closeTab(index: number) {
    this.tabs.splice(index, 1);
  }

  panels = [
    { title: 'Panel 1', content: 'Content 1' },
    { title: 'Panel 2', content: 'Content 2' },
  ];
  openPanelIndex: number | null = null;

  togglePanel(index: number): void {
    this.openPanelIndex = this.openPanelIndex === index ? null : index;
  }

  isPanelOpen(index: number): boolean {
    return this.openPanelIndex === index;
  }

  list = signal<Approvals>({
    id: 0,
    purchaseTxnRefNo: '',
    purchaseTs: '',
    totalQty: 0,
    mspAmount: 0,
    incentiveAmount: 0,
    rateCutAmount: 0,
    paymentAmount: 0,
    tokenNo: '',
    authTxnTypeName: '',
    moisturePercentage: 0,
    noOfBags: 0,
    dpcNameCode: '',
    farmerNameCode: '',
    villageNameCode: '',
    seasonName: '',
    varietyName: '',
    ddswCut:0,
    issCut: 0,
    moistureCut:0
  });

  displayedColumns: string[] = [
    'villageNameCode',
    'dpcNameCode',
    'purchaseTxnRefNo',
    'seasonName',
    'farmerNameCode',
    'varietyName',
    'noOfBags',
    // 'purchaseTs',
    'incentiveAmount',
    'rateCutAmount',
    'moisturePercentage',
    'ddswCut',
    'issCut',
    'paymentAmount',
    'skippayment',
    // 'actions'
  ];
  lists = signal<Approvals[]>([]);
  protected readonly AppRoutes = AppRoutes;

  constructor(private datePipe: DatePipe, private cdr: ChangeDetectorRef) {
    effect(() => {
      if (this.paginator()) {
        this.dataSource().sort = this.sort()!;
        this.dataSource().paginator = this.paginator()!
      }
    });
  }

  ngOnInit(): void {
  }

  onAdd() {
    this.list.set({
      id: 0,
      purchaseTxnRefNo: '',
      purchaseTs: '',
      totalQty: 0,
      mspAmount: 0,
      incentiveAmount: 0,
      rateCutAmount: 0,
      paymentAmount: 0,
      tokenNo: '',
      authTxnTypeName: '',
      moisturePercentage: 0,
      noOfBags: 0,
      dpcNameCode: '',
      farmerNameCode: '',
      villageNameCode: '',
      seasonName: '',
      varietyName: '',
      ddswCut:0,
      issCut: 0,
      moistureCut:0
    });
    this.mode.set("add")
    this.showGrid.set(true)
  }

  onView(data: Approvals) {
    this.list.set(data);
    this.mode.set('view');
    this.showGrid.set(true)
  }

  onEdit(data: Approvals) {
    this.list.set(data);
    this.mode.set('edit');
    this.showGrid.set(true)
  }

  onClose() {
    this.showGrid.set(false)
    this.approvalResource.reload();
    this.search.setValue("");
  }

  onRefresh() {
    this.approvalResource.reload();
    this.search.setValue("");
  }


  onSearch() {
    const filterValue = this.search.value?.trim().toLowerCase() || '';
    this.dataSource().filter = filterValue;
  }

  isAllSelected() {
    const numSelected = this.selection.selected.filter(row => row.isSkipPayment).length;
    const numRows = this.dataSource().data.length;
    return numSelected === numRows;
  }

  masterToggle() {
    this.isAllSelected() ?
      this.selection.clear() :
      this.dataSource().data.forEach(row => {
        if (!this.selection.isSelected(row)) {
          this.selection.select(row);
        }
      });
  }

  onSkipPayment(event: any, row: any, index: number) {
    row.isSkipPayment = event.checked;
    if (!event.checked) {
      row.reason = '';
    }
    this.cdr.detectChanges();
  }


  onReasonChange(newValue: string): void {
    console.log('Reason changed:', newValue);
  }

  approveSkipPayment() {
    this.markAllAsTouched();
    const skipLength = this.dataSource().data.filter(item => item.isSkipPayment);
    if (skipLength.length == 0) {
      this.alertService.info('No Skip Payement Selected')
    } else {
      const skipPayment = this.dataSource().data.filter(item => item.isSkipPayment && item.reason === '');
      if (skipPayment.length > 0) {
        this.alertService.info('Please Fill Reason For Skipped Payment')
      } else {
        const txt = 'Are you sure you want to approve except skiped payments?'
        this.confirmationApprove(txt);
      }
    }
  }

  approveAllPayment() {
    this.approvalResource.reload();
    const txt = 'Are you sure you want to approve all payments?'
    this.confirmationApprove(txt);
  }

  async confirmationApprove(txt: string) {
    const title = 'Payment Approve Confirmation';
    const Yes = 'Yes';
    const No = 'No';

    const swalRes = await Swal.fire({
      title,
      text: txt,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: Yes,
      cancelButtonText: No,
      customClass: {
        confirmButton: 'btn-primary',
        cancelButton: 'btn-secondary',
      },
    });

    if (swalRes.isConfirmed) {
      this.alertService.success('Payment Approved Successfully')
      // let payload: UpdateUserStatusPayload = {
      //   id: user.id,
      //   statusType: StatusType.Inactive,
      // };
      // const res = await this.userService.update(payload);
      // if (res.isSuccess) {
      //   this.getUsers();
      // }
    } else{
      this.approvalResource.reload();
    }
  }

  markAllAsTouched(): void {
    this.reasonControls.forEach((control) => {
      control.control.markAsTouched();
    });
  }

  onTab(id :number){
    this.tabId.set(id);
  }
  
}
