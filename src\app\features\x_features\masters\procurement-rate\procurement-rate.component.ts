import { Component, computed, ElementRef, inject, input, OnInit, output, resource, signal, viewChild } from '@angular/core';
import { LookupService } from '../../../../services/x_apis/lookup.service';
import { ProcurementRateService } from '../../../../services/x_apis/masters/procurement-rate.service';
import { AlertService } from '../../../../services/alert.service';
import { InputformatService } from '../../../../services/x_apis/inputformat.service';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { ProcurementRate } from '../../../../models/x_models/masters/procurement-rate';
import { MatError, MatFormField, MatOption, MatSelect } from '@angular/material/select';
import { MatButton } from '@angular/material/button';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInput } from '@angular/material/input';
import { DateInputDirective } from '../../../../directives/date-input.directive';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { MatToolbarModule } from '@angular/material/toolbar';

@Component({
  selector: 'app-procurement-rate',
  imports: [
    FormsModule,
    MatFormField,
    MatInput,
    ReactiveFormsModule,
    MatError,
    MatOption,
    MatSelect,
    MatButton,
    MatIconModule,
    MatDatepickerModule,
    MatNativeDateModule,
    TranslateModule,
    MatToolbarModule,
    MatFormFieldModule],
  templateUrl: './procurement-rate.component.html',
  styleUrls: ['./procurement-rate.component.scss']
})

export class ProcurementRateComponent {
  menuService = inject(MenuService);
  lookupService = inject(LookupService);
  procurementRateService = inject(ProcurementRateService);
  alertService = inject(AlertService);
  inputFormatService = inject(InputformatService);

  translate = inject(TranslateService);
  regions = resource({ loader: () => this.lookupService.getRegion() }).value;
  seasons = resource({ loader: () => this.lookupService.getSeason() }).value;

  products = resource({ loader: () => this.lookupService.getProduct() }).value;
  Variety = resource({ loader: () => this.lookupService.getVariety() }).value;


  mode = input.required<string>();
  closed = output<boolean>();
  fb = inject(FormBuilder);
  editable = signal<boolean>(true);

  uniqueId = input.required<any>();
  procurementRate = signal<ProcurementRate | null>(null);
  minToDate = signal<Date>(new Date());
  search1 = viewChild.required<ElementRef<HTMLInputElement>>('search1')
  search2 = viewChild.required<ElementRef<HTMLInputElement>>('search2')
  search3 = viewChild.required<ElementRef<HTMLInputElement>>('search3')


  form = this.fb.group({
    id: this.fb.control<number>(0),
    price: this.fb.control<number | null>(null, Validators.required),
    productId: this.fb.control<number | null>(null, Validators.required),
    varietyId: this.fb.control<number | null>(null, Validators.required),
    seasonId: this.fb.control<number | null>(null, Validators.required),
    bonusPrice: this.fb.control<number | null>(null, Validators.required),
    fromDate: this.fb.control<string>("", Validators.required),
    toDate: this.fb.control<string>("", Validators.required),
    gradeCut: this.fb.control<number | null>(null, Validators.required)
  });

  ngOnInit() {
    this.translate.onLangChange.subscribe(() => {
      this.fetchRegions();
    });
    this.form.get("fromDate")?.valueChanges.subscribe((res) => {
      if (res) {
        const parsedFromDate = new Date(res);
        parsedFromDate.setDate(parsedFromDate.getDate() + 1);
        this.minToDate.set(parsedFromDate);
        this.form.patchValue({ toDate: '' });
      } else {
        this.minToDate.set(new Date());
      }
    });

    if (this.mode() === "edit" || this.mode() === "view") {
      this.getFormData()
    }
  }

 readonly productSearch = signal('');
 readonly varietySearch = signal('');
 readonly seasonSearch = signal('');


  readonly filteredVarieties = computed(() =>
    this.Variety()?.filter(x =>
      x.value.toLowerCase().includes(this.varietySearch().toLowerCase())
    )
  );

  readonly filteredSeasons = computed(() =>
    this.seasons()?.filter(x =>
      x.value.toLowerCase().includes(this.seasonSearch().toLowerCase())
    )
  );

  readonly filteredProducts = computed(() =>
    this.products()?.filter(x =>
      x.value.toLowerCase().includes(this.productSearch().toLowerCase())
    )
  );
  


  filter(value: any,type:string) {
     if (type === 'product') {
      this.productSearch.set(value.target.value);
    } else if (type === 'variety') {
      this.varietySearch.set(value.target.value);
    } else {
      this.seasonSearch.set(value.target.value);
    }
  }

    resetSearch(type: any) {

    if (type === 'product') {
      this.search1().nativeElement.value = '';
      this.productSearch.set('');
    } else if (type === 'variety') {
      this.varietySearch.set('');
      this.search2().nativeElement.value = '';
    } 
     else  {
      this.seasonSearch.set('');
      this.search3().nativeElement.value = '';
    } 

  }

  async fetchRegions() {
    this.regions.set(await this.lookupService.getRegion());
    this.products.set(await this.lookupService.getProduct());
  }

  async getFormData() {
    const res = await this.procurementRateService.getById(this.uniqueId());
    this.form.patchValue({ ...res });
    if (this.mode() === "view") {
      this.editable.set(false)
      this.form.disable()
    }
  }

  onEdit() {
    this.form.enable();
    this.editable.set(true)
  }

  async onSubmit() {
    if (this.form.valid) {
      const formValue = { ...this.form.value };
      const res = await this.procurementRateService.create(formValue as ProcurementRate)
      if (res.isSuccess) {
        this.closed.emit(true)
        if (formValue.id == 0) {
          this.alertService.success(`Procurement Price Cut Created successfully.`)
        }
        else {
          this.alertService.success(`Procurement Price Cut Updated successfully.`)
        }
      }
    } else {
      this.form.markAllAsTouched();
    }
  }

  goBack() {
    this.closed.emit(true)
  }

  formReset() {
    let indexId = this.form.value.id;
    this.form.reset();
    this.form.patchValue({
      id: indexId
    });
  }

  onInput(event: any, regexPattern: any): void {
    this.inputFormatService.isNumericOrHyphenOrSpace(event, regexPattern);
  }

}
