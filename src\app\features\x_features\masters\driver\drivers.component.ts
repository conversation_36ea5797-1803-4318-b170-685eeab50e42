import { NgTemplateOutlet, DatePipe } from '@angular/common';
import { Component, computed, effect, inject, linkedSignal, OnInit, resource, signal, viewChild } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckbox } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInput, MatPrefix } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { StatusType } from '../../../../enums/x_enums/dotnet/status-type';
import { AppRoutes } from '../../../../enums/app-routes';
import { confirmAndDelete } from '../../../../models/x_models/masters/shared';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { DriverService } from '../../../../services/x_apis/masters/driver.service';
import { DriverComponent } from './driver.component';
import { Drivers } from '../../../../models/x_models/masters/driver';
import { MatToolbarModule } from '@angular/material/toolbar';

@Component({
  selector: 'app-drivers',
  imports: [MatIcon,
    MatInput,
    MatPrefix,
    ReactiveFormsModule,
    MatPaginatorModule,
    MatTableModule,
    NgTemplateOutlet,
    MatButtonModule,
    MatMenuModule,
    DatePipe,
    MatSortModule,
    MatFormFieldModule,
    TranslateModule,
    MatToolbarModule,
    DriverComponent],
  providers: [DatePipe],
  templateUrl: './drivers.component.html',
  styleUrls: ['./drivers.component.scss']
})
export class DriversComponent implements OnInit {
  menuService = inject(MenuService);
  access = computed(() => this.menuService.activeMenu()?.controlAccess);
  translate = inject(TranslateService);

  paginator = viewChild<MatPaginator>(MatPaginator);
  sort = viewChild<MatSort>(MatSort);

  driverService = inject(DriverService)
  driverResource = resource({ loader: () => this.driverService.get() });
  dataSource = linkedSignal(() => new MatTableDataSource(this.driverResource.value()));

  loading = signal(false);
  mode = signal<'view' | 'edit' | 'add'>('add');
  search = new FormControl("");
  showGrid = signal(false);
  status = StatusType;

  driver = signal<Drivers>({
    id: 0,
    licenseNo: '',
    driverName: '',
    driverMobileNo: ''
  });

  displayedColumns: string[] = [
    'driverName',
    'driverMobileNo',
    'licenseNo',
    'actions',
  ];
  drivers = signal<Drivers[]>([]);
  protected readonly AppRoutes = AppRoutes;  // Need clarification

  constructor(private datePipe: DatePipe) {
    effect(() => {
      if (this.paginator()) {
        this.dataSource().sort = this.sort()!;
        this.dataSource().paginator = this.paginator()!
      }
    });
  }

  ngOnInit(): void {
  }

  onAdd() {
    this.driver.set({
      id: 0,
      licenseNo: '',
      driverName: '',
      driverMobileNo: ''
    });
    this.mode.set("add")
    this.showGrid.set(true)
  }

  onView(drivers: Drivers) {
    this.driver.set(drivers);
    this.mode.set('view');
    this.showGrid.set(true)
  }

  onEdit(drivers: Drivers) {
    this.driver.set(drivers);
    this.mode.set('edit');
    this.showGrid.set(true)
  }

  onClose() {
    this.showGrid.set(false)
    this.driverResource.reload();
    this.search.setValue("");
  }

  onRefresh() {
    this.driverResource.reload();
    this.search.setValue("");
  }

  async onDelete(driver: Drivers) {
    await confirmAndDelete(driver, driver.driverName, 'Driver', this.driverService, () => this.driverResource.reload());
  }

  onSearch() {
    const filterValue = this.search.value?.trim().toLowerCase() || '';
    this.dataSource().filter = filterValue;
  }

}
