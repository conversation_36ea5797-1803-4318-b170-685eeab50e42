import {Component, ElementRef, inject, input, Input, output, viewChild} from '@angular/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { OnlyNumbersDirective } from '../../../directives/only-numbers.directive';
import {FormBuilder, ReactiveFormsModule} from '@angular/forms';

@Component({
  selector: 'app-otp',
  imports: [MatFormFieldModule, MatInputModule, OnlyNumbersDirective, ReactiveFormsModule],
  templateUrl: './otp.component.html',
  styleUrl: './otp.component.scss'
})
export class OtpComponent {
  one = viewChild.required<ElementRef<HTMLInputElement>>('one')
  two = viewChild.required<ElementRef<HTMLInputElement>>('two')
  three = viewChild.required<ElementRef<HTMLInputElement>>('three')
  four = viewChild.required<ElementRef<HTMLInputElement>>('four')
  five = viewChild.required<ElementRef<HTMLInputElement>>('five')
  six = viewChild.required<ElementRef<HTMLInputElement>>('six')

  fb = inject(FormBuilder)
  form = this.fb.group({
    one: [""],
    two: [""],
    three: [""],
    four: [""],
    five: [""],
    six: [""]
  })

  changed = output<string>()

  onChange(event: any, current: any, next: any) {
    if (event.inputType === 'insertText' && next) {
      next.focus()
    }
    this.outputCode()
  }

  onBackSpace(prev: any, current: any) {
    if (!current.value && prev) {
      prev.focus()
    }
  }

  outputCode() {
    const one = this.one().nativeElement.value;
    const two = this.two().nativeElement.value;
    const three = this.three().nativeElement.value;
    const four = this.four().nativeElement.value;
    const five = this.five().nativeElement.value;
    const six = this.six().nativeElement.value;

    const code = one + two + three + four + five + six;

    this.changed.emit(code)
  }
}
