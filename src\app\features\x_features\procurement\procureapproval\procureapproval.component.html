<div class="component card">
  <div class="page-header">
    <h1>{{menuService.activeMenu()?.title}}</h1>
  </div>

  

  <mat-tab-group class="vertical-tabs">
    <mat-tab label="Procurement Payment Approval" (click)="onTab(1)">
      <ng-template matTabContent>
   
        <div class="header">
      
      
          <div class="grid-container" style="margin-bottom:20px;">
            <div class="row-4">
      
            </div>
            <div class="row-8">
      
              <div class="actions">
                <button mat-flat-button color="accent" style="text-align:center;padding:10px;border-radius:5px;"
                  (click)="approveAllPayment()">
                  <mat-icon>map</mat-icon>
                  Approve All Payment
                </button>
      
                <button mat-flat-button color="accent" style="text-align:center;padding:10px;border-radius:5px;"
                  (click)="approveSkipPayment()">
                  <mat-icon>map</mat-icon>
                  Approve Except Skiped Payment
                </button>
              </div>
      
            </div>
          </div>
      
          <div class="filters-wrapper">
            <mat-form-field class="search hide-subscript" appearance="outline">
              <mat-icon class="icon-search material-symbols-outlined" matPrefix>search</mat-icon>
              <input id="search" [formControl]="search" (input)="onSearch()" autocomplete="off" matInput
                placeholder="{{ 'Search' | translate }}">
            </mat-form-field>
            <div class="filters-more">
              <button (click)="onRefresh()" class="btn btn-filter" mat-icon-button>
                <mat-icon class="icon-filter material-symbols-outlined" matPrefix>sync</mat-icon>
              </button>
            </div>
          </div>
        </div>
        <div class="content">
          <div class="table-wrapper">
      
            <table mat-table [dataSource]="dataSource()" matSort class="mat-elevation-z8">
    
              <ng-container matColumnDef="villageNameCode">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>{{'Village' | translate}} </th>
                <td mat-cell *matCellDef="let row"> {{row.villageNameCode}} </td>
              </ng-container>
      
              <ng-container matColumnDef="dpcNameCode">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>{{'DPC' | translate}} </th>
                <td mat-cell *matCellDef="let row"> {{row.dpcNameCode}} </td>
              </ng-container>
      
              <ng-container matColumnDef="purchaseTxnRefNo">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'BillNo' | translate}}</th>
                <td mat-cell *matCellDef="let row"> {{row.purchaseTxnRefNo }}
                </td>
              </ng-container>
      
              <ng-container matColumnDef="seasonName">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'Season' | translate}} </th>
                <td mat-cell *matCellDef="let row"> {{row.seasonName }} </td>
              </ng-container>
      
              <ng-container matColumnDef="farmerNameCode">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'FarmerName' | translate}}</th>
                <td mat-cell *matCellDef="let row"> {{row.farmerNameCode }} </td>
              </ng-container>
      
              <ng-container matColumnDef="varietyName">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'PaddyType' | translate}}</th>
                <td mat-cell *matCellDef="let row"> {{row.varietyName }} </td>
              </ng-container>
      
              <ng-container matColumnDef="noOfBags">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'Bags' | translate}}</th>
                <td mat-cell *matCellDef="let row"> {{row.noOfBags }} </td>
              </ng-container>
      
              <!-- <ng-container matColumnDef="purchaseTs">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'PurchaseQuality' | translate}}</th>
                <td mat-cell *matCellDef="let row"> {{row.purchaseTs }} </td>
              </ng-container> -->
      
              <ng-container matColumnDef="incentiveAmount">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'Incentive' | translate}}</th>
                <td mat-cell *matCellDef="let row"> {{row.incentiveAmount }} </td>
              </ng-container>
      
              <!-- <ng-container matColumnDef="paymentAmount">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'Amount' | translate}}</th>
                <td mat-cell *matCellDef="let row"> {{row.paymentAmount }} </td>
              </ng-container> -->
      
              <ng-container matColumnDef="rateCutAmount">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'GradeCut' | translate}}</th>
                <td mat-cell *matCellDef="let row"> {{row.rateCutAmount }} </td>
              </ng-container>
      
              <ng-container matColumnDef="moisturePercentage">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'MoistureCut' | translate}}</th>
                <td mat-cell *matCellDef="let row"> {{row.moisturePercentage }} </td>
              </ng-container>
      
              <ng-container matColumnDef="ddswCut">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'DDSCut' | translate}}</th>
                <td mat-cell *matCellDef="let row"> {{row.ddswCut }} </td>
              </ng-container>
      
              <ng-container matColumnDef="issCut">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'ISSCut' | translate}}</th>
                <td mat-cell *matCellDef="let row"> {{row.issCut }} </td>
              </ng-container>
      
              <ng-container matColumnDef="paymentAmount">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'PaymentAmount' | translate}}</th>
                <td mat-cell *matCellDef="let row"> {{row.paymentAmount }} </td>
              </ng-container>
      
      
              <ng-container matColumnDef="processpayment">
                <th mat-header-cell *matHeaderCellDef>
                  <mat-checkbox (change)="$event ? masterToggle() : null" [checked]="selection.hasValue() && isAllSelected()"
                    [indeterminate]="selection.hasValue() && !isAllSelected()">
                  </mat-checkbox>
                </th>
                <td mat-cell *matCellDef="let row;let i = index">
                  <mat-checkbox (click)="$event.stopPropagation()" (change)="$event ? selection.toggle(row) : null"
                    [checked]="selection.isSelected(row)">
                  </mat-checkbox>
                </td>
              </ng-container>
      
              <ng-container matColumnDef="skippayment">
                <th mat-header-cell *matHeaderCellDef>
                  {{ 'SkipPayment' | translate }} {{ 'Optional' | translate }}
                </th>
                <td mat-cell *matCellDef="let row; let i = index">
                  <div style="display: flex; align-items: center; gap: 10px;">
                    <mat-checkbox [(ngModel)]="row.isSkipPayment" (change)="onSkipPayment($event, row, i)">
                    </mat-checkbox>
      
                    @if(row.isSkipPayment){
                    <mat-form-field style="min-width: 200px;margin-top: 15px;margin-bottom:5px;" appearance="fill">
                      <input matInput [(ngModel)]="row.reason" placeholder="{{ 'Enterreason' | translate }}" name="reason"
                        [readonly]="!row.isSkipPayment" required #reason="ngModel" (ngModelChange)="onReasonChange($event)" />
                        
                        
                      <mat-error class="error-msg">
                        @if((reason.invalid && reason.touched) || submitted()){
                        {{ 'Reason' | translate }} {{ 'IsRequired' | translate }}
                        }
                      </mat-error>
      
                      @if(row.showError) {
                        <mat-error>
                          Reason is required
                        </mat-error>
                      }
                    
                    </mat-form-field>
                    }
      
                  </div>
                </td>
              </ng-container>
      
              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
              <tr class="mat-row" *matNoDataRow>
                <td class="mat-cell" [attr.colspan]="displayedColumns.length" style="text-align: center;padding:10px">
                  No data found
                </td>
              </tr>
            </table>
      
          </div>
      
          <!-- @if (dataSource().filteredData.length==0) {
          <div style="text-align: center;">No Data</div>
          } -->
      
          <mat-paginator class="paginator" pageSize="10" [pageSizeOptions]="[5, 10, 50,100]"
            aria-label="Select page of users"></mat-paginator>
      
          <div class="mobile-wrapper">
            <div class="un-cards">
              @for (item of dataSource().data; track item) {
              <div class="un-card">
                <div class="desc">
                  <div class="quote-no">{{item.dpcNameCode}}</div>
                  <div style="line-height: 1em;"><span class="tracking-no">{{item.noOfBags}}</span> </div>
                  <div class="quote-no">{{item.paymentAmount}}</div>
                </div>
                <div class="actions">
                  <button mat-icon-button class="btn btn-actions" [matMenuTriggerFor]="menu" aria-label="Card actions">
                    <mat-icon>more_vert</mat-icon>
                  </button>
                  <mat-menu #menu="matMenu">
                    @if (access()?.canView) {
                    <button (click)="onView(item)" mat-menu-item>
                      <mat-icon class="material-symbols-rounded">visibility</mat-icon>
                      <span>{{ 'View' | translate }}</span>
                    </button>
                    }
                  </mat-menu>
                </div>
              </div>
              }
            </div>
          </div>
        </div>
      </ng-template>
    </mat-tab>
    <mat-tab label="Procurement Skipped Payment" (click)="onTab(2)">
    <ng-template matTabContent>
      <div class="header">
      
      
     
    
        <div class="filters-wrapper">
          <mat-form-field class="search hide-subscript" appearance="outline">
            <mat-icon class="icon-search material-symbols-outlined" matPrefix>search</mat-icon>
            <input id="search" [formControl]="search" (input)="onSearch()" autocomplete="off" matInput
              placeholder="{{ 'Search' | translate }}">
          </mat-form-field>
          <div class="filters-more">
            <button (click)="onRefresh()" class="btn btn-filter" mat-icon-button>
              <mat-icon class="icon-filter material-symbols-outlined" matPrefix>sync</mat-icon>
            </button>
          </div>
        </div>
      </div>
      <div class="content">
        <div class="table-wrapper">
    
          <table mat-table [dataSource]="dataSource()" matSort class="mat-elevation-z8">
    
            <ng-container matColumnDef="villageNameCode">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>{{'Village' | translate}} </th>
              <td mat-cell *matCellDef="let row"> {{row.villageNameCode}} </td>
            </ng-container>
    
            <ng-container matColumnDef="dpcNameCode">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>{{'DPC' | translate}} </th>
              <td mat-cell *matCellDef="let row"> {{row.dpcNameCode}} </td>
            </ng-container>
    
            <ng-container matColumnDef="purchaseTxnRefNo">
              <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'BillNo' | translate}}</th>
              <td mat-cell *matCellDef="let row"> {{row.purchaseTxnRefNo }}
              </td>
            </ng-container>
    
            <ng-container matColumnDef="seasonName">
              <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'Season' | translate}} </th>
              <td mat-cell *matCellDef="let row"> {{row.seasonName }} </td>
            </ng-container>
    
            <ng-container matColumnDef="farmerNameCode">
              <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'FarmerName' | translate}}</th>
              <td mat-cell *matCellDef="let row"> {{row.farmerNameCode }} </td>
            </ng-container>
    
            <ng-container matColumnDef="varietyName">
              <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'PaddyType' | translate}}</th>
              <td mat-cell *matCellDef="let row"> {{row.varietyName }} </td>
            </ng-container>
    
            <ng-container matColumnDef="noOfBags">
              <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'Bags' | translate}}</th>
              <td mat-cell *matCellDef="let row"> {{row.noOfBags }} </td>
            </ng-container>
    
            <!-- <ng-container matColumnDef="purchaseTs">
              <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'PurchaseQuality' | translate}}</th>
              <td mat-cell *matCellDef="let row"> {{row.purchaseTs }} </td>
            </ng-container> -->
    
            <ng-container matColumnDef="incentiveAmount">
              <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'Incentive' | translate}}</th>
              <td mat-cell *matCellDef="let row"> {{row.incentiveAmount }} </td>
            </ng-container>
    
            <!-- <ng-container matColumnDef="paymentAmount">
              <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'Amount' | translate}}</th>
              <td mat-cell *matCellDef="let row"> {{row.paymentAmount }} </td>
            </ng-container> -->
    
            <ng-container matColumnDef="rateCutAmount">
              <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'GradeCut' | translate}}</th>
              <td mat-cell *matCellDef="let row"> {{row.rateCutAmount }} </td>
            </ng-container>
    
            <ng-container matColumnDef="moisturePercentage">
              <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'MoistureCut' | translate}}</th>
              <td mat-cell *matCellDef="let row"> {{row.moisturePercentage }} </td>
            </ng-container>
    
            <ng-container matColumnDef="ddswCut">
              <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'DDSCut' | translate}}</th>
              <td mat-cell *matCellDef="let row"> {{row.ddswCut }} </td>
            </ng-container>
    
            <ng-container matColumnDef="issCut">
              <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'ISSCut' | translate}}</th>
              <td mat-cell *matCellDef="let row"> {{row.issCut }} </td>
            </ng-container>
    
            <ng-container matColumnDef="paymentAmount">
              <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'PaymentAmount' | translate}}</th>
              <td mat-cell *matCellDef="let row"> {{row.paymentAmount }} </td>
            </ng-container>
    
    
            <ng-container matColumnDef="processpayment">
              <th mat-header-cell *matHeaderCellDef>
                <mat-checkbox (change)="$event ? masterToggle() : null" [checked]="selection.hasValue() && isAllSelected()"
                  [indeterminate]="selection.hasValue() && !isAllSelected()">
                </mat-checkbox>
              </th>
              <td mat-cell *matCellDef="let row;let i = index">
                <mat-checkbox (click)="$event.stopPropagation()" (change)="$event ? selection.toggle(row) : null"
                  [checked]="selection.isSelected(row)">
                </mat-checkbox>
              </td>
            </ng-container>
    
            <ng-container matColumnDef="skippayment">
              <th mat-header-cell *matHeaderCellDef>
                {{ 'SkipPayment' | translate }} {{ 'Optional' | translate }}
              </th>
              <td mat-cell *matCellDef="let row; let i = index">
                <div style="display: flex; align-items: center; gap: 10px;">
                  <mat-checkbox [(ngModel)]="row.isSkipPayment" (change)="onSkipPayment($event, row, i)">
                  </mat-checkbox>
    
                  @if(row.isSkipPayment){
                  <mat-form-field style="min-width: 200px;margin-top: 15px;margin-bottom:5px;" appearance="fill">
                    <input matInput [(ngModel)]="row.reason" placeholder="{{ 'Enterreason' | translate }}" name="reason"
                      [readonly]="!row.isSkipPayment" required #reason="ngModel" (ngModelChange)="onReasonChange($event)" />
                      
                      
                    <mat-error class="error-msg">
                      @if((reason.invalid && reason.touched) || submitted()){
                      {{ 'Reason' | translate }} {{ 'IsRequired' | translate }}
                      }
                    </mat-error>
    
                    @if(row.showError) {
                      <mat-error>
                        Reason is required
                      </mat-error>
                    }
                  
                  </mat-form-field>
                  }
    
                </div>
              </td>
            </ng-container>
    
            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
    
          </table>
    
        </div>
    
        @if (dataSource().filteredData.length==0) {
        <div style="text-align: center;">No Data</div>
        }
    
        <mat-paginator class="paginator" pageSize="10" [pageSizeOptions]="[5, 10, 50,100]"
          aria-label="Select page of users"></mat-paginator>
    
        <div class="mobile-wrapper">
          <div class="un-cards">
            @for (item of dataSource().data; track item) {
            <div class="un-card">
              <div class="desc">
                <div class="quote-no">{{item.dpcNameCode}}</div>
                <div style="line-height: 1em;"><span class="tracking-no">{{item.noOfBags}}</span> </div>
                <div class="quote-no">{{item.paymentAmount}}</div>
              </div>
              <div class="actions">
                <button mat-icon-button class="btn btn-actions" [matMenuTriggerFor]="menu" aria-label="Card actions">
                  <mat-icon>more_vert</mat-icon>
                </button>
                <mat-menu #menu="matMenu">
                  @if (access()?.canView) {
                  <button (click)="onView(item)" mat-menu-item>
                    <mat-icon class="material-symbols-rounded">visibility</mat-icon>
                    <span>{{ 'View' | translate }}</span>
                  </button>
                  }
                </mat-menu>
              </div>
            </div>
            }
          </div>
        </div>
      </div>
      </ng-template>
    </mat-tab>
  </mat-tab-group>


 
</div>


