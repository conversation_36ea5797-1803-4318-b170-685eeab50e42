import { NgT<PERSON>plateOutlet, DatePipe, CurrencyPipe } from '@angular/common';
import { Component, computed, effect, inject, linkedSignal, resource, signal, viewChild } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckbox } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInput, MatPrefix } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatOption, MatSelect } from '@angular/material/select';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { CloseFilterOnBlurDirective } from '../../../../directives/close-filter-on-blur.directive';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { confirmAndDelete } from '../../../../models/x_models/masters/shared';
import { StatusType } from '../../../../enums/x_enums/dotnet/status-type';
import { StorageLocationService } from '../../../../services/x_apis/masters/storage-location.service';
import { StorageLocations } from '../../../../models/x_models/masters/storage-location';
import { StorageLocationComponent } from './storage-location.component';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatToolbarModule } from '@angular/material/toolbar';

@Component({
    selector: 'app-storage-locations',
    imports: [MatIcon,
        MatIcon,
        MatInput,
        MatPrefix,
        ReactiveFormsModule,
        MatCheckbox,
        MatPaginatorModule,
        MatTableModule,
        NgTemplateOutlet,
        MatButtonModule,
        MatMenuModule,
        DatePipe,
        MatSortModule,
        MatOption,
        MatToolbarModule,
        MatSelect,
         TranslateModule,
        CloseFilterOnBlurDirective, CurrencyPipe, MatFormFieldModule, StorageLocationComponent],
    templateUrl: './storage-locations.component.html',
    styleUrl: './storage-locations.component.scss'
})
export class StorageLocationsComponent {
    menuService = inject(MenuService);
    access = computed(() => this.menuService.activeMenu()?.controlAccess);
    translate = inject(TranslateService);

    paginator = viewChild<MatPaginator>(MatPaginator);
    sort = viewChild<MatSort>(MatSort);

    storageLocationService = inject(StorageLocationService);
    storageLocationResource = resource({ loader: () => this.storageLocationService.get() });
    dataSource = linkedSignal(() => new MatTableDataSource(this.storageLocationResource.value()));

    loading = signal(false);
    mode = signal<'view' | 'edit' | 'add'>('add');
    search = new FormControl("");
    showGrid = signal(false);
    status = StatusType;

    list = signal<StorageLocations>({
        id: 0,
        storageLocationName: "",
        storageLocationRegionalName: "",
        storageTypeName: "",
        regionName: "",
        agencyName: "",
        capacity: 0,
        carpetArea: 0,
        latitude: 0,
        longitude: 0
    });

    displayedColumns: string[] = [
        'regionName',
        'storageLocationName',
        'storageLocationRegionalName',
        'storageTypeName',
        'capacity',
        'carpetArea',
        'actions'
    ];

    constructor() {
        effect(() => {
            if (this.paginator()) {
                this.dataSource().sort = this.sort()!;
                this.dataSource().paginator = this.paginator()!
            }
        });
    }

    ngOnInit(): void {
    }

    onAdd() {
        this.list.set({
            id: 0,
            storageLocationName: "",
            storageLocationRegionalName: "",
            storageTypeName: "",
            regionName: "",
            agencyName: "",
            capacity: 0,
            carpetArea: 0,
            latitude: 0,
            longitude: 0
        });
        this.mode.set("add")
        this.showGrid.set(true)
    }

    onView(rowData: StorageLocations) {
        this.list.set(rowData);
        this.mode.set('view');
        this.showGrid.set(true)
    }

    onEdit(rowData: StorageLocations) {
        this.list.set(rowData);
        this.mode.set('edit');
        this.showGrid.set(true)
    }

    onClose() {
        this.showGrid.set(false)
        this.storageLocationResource.reload()
        this.search.setValue("");
    }

    onRefresh() {
        this.storageLocationResource.reload()
        this.search.setValue("");
    }

    async onDelete(rowData: StorageLocations) {
        await confirmAndDelete(rowData, rowData.storageLocationName, 'Storage Location', this.storageLocationService, () => this.storageLocationResource.reload());
    }

    onSearch() {
        const filterValue = this.search.value?.trim().toLowerCase() || '';
        this.dataSource().filter = filterValue;
      }

}
