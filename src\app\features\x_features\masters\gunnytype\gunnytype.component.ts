import { Component, inject, input, OnInit, output, signal } from '@angular/core';
import { AlertService } from '../../../../services/alert.service';
import { InputformatService } from '../../../../services/x_apis/inputformat.service';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButton } from '@angular/material/button';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatFormField, MatInput, MatError } from '@angular/material/input';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { GunnytypeService } from '../../../../services/x_apis/masters/gunnytype.service';
import { Gunny } from '../../../../models/x_models/masters/gunnytype';
import { MatToolbarModule } from '@angular/material/toolbar';


@Component({
  selector: 'app-gunnytype',
  imports: [
    FormsModule,
    MatFormField,
    MatInput,
    ReactiveFormsModule,
    MatError,
    MatButton,
    MatIconModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatFormFieldModule,
    TranslateModule,
    MatToolbarModule
  ],
  templateUrl: './gunnytype.component.html',
  styleUrls: ['./gunnytype.component.scss']
})

export class GunnytypeComponent implements OnInit {
  menuService = inject(MenuService);
  gunnytypeService = inject(GunnytypeService)
  alert = inject(AlertService);
  inputFormatService = inject(InputformatService);
  translate = inject(TranslateService);

  mode = input.required<string>();
  uniqueId = input.required<any>()
  closed = output<boolean>();
  fb = inject(FormBuilder);
  editable = signal<boolean>(true);
  gunny = signal<Gunny | null>(null);

  form = this.fb.group({
    id: [0],
    gunnyName: ['', Validators.required],
    gunnyCode: ['', Validators.required],
    gunnyShortName: ['', Validators.required],
  });


  ngOnInit() {

    if (this.mode() === "edit" || this.mode() === "view") {
      this.getGunny()
    }
  }

  async getGunny() {
    const res = await this.gunnytypeService.getById(this.uniqueId());
    this.gunny.set(res)
    this.form.patchValue(res)
    if (this.mode() === "view") {
      this.editable.set(false)
      this.form.disable()
    }
  }

  onEdit() {
    this.form.enable();
    this.editable.set(true)
  }

  async onSubmit() {

    if (this.form.valid) {

      const res = await this.gunnytypeService.create(this.form.value)
      if (res.isSuccess) {
        this.closed.emit(true)
        if (this.form.value.id == 0) {
          this.alert.success(`Gunny Type ${this.form.value.gunnyName} Created Successfully.`)
        }
        else {
          this.alert.success(`Gunny Type ${this.form.value.gunnyName} Updated Successfully.`)
        }
      }
    }
    else {
      this.form.markAllAsTouched();
    }
  }

  goBack() {
    this.closed.emit(true)
  }

  formReset() {
    let indexId = this.form.value.id;
    this.form.reset();
    this.form.patchValue({
      id: indexId
    });
  }

  onInput(event: any, regexPattern: any): void {
    this.inputFormatService.isNumericOrHyphenOrSpace(event, regexPattern);
  }
}