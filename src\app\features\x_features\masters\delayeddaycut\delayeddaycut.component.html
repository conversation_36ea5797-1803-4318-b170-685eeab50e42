<mat-toolbar color="primary" class="navbar">
  <nav aria-label="Breadcrumb navigation" style="display: flex; align-items: center; flex-wrap: wrap;">
    <a routerLink="/" class="crumb">{{'Master' | translate}}</a>
    <mat-icon>chevron_right</mat-icon>
    <a class="crumb" (click)="goBack()">{{menuService.activeMenu()?.title}}</a>
    <mat-icon>chevron_right</mat-icon>
    <span aria-current="page" class="nav-active">
      @if(uniqueId() == 0) {
      {{'New' | translate}} {{menuService.activeMenu()?.title}}
      } @else {
      {{delayedDaysTypeName()}}
      }
    </span>
    @if(mode() === "view") {
    <button (click)="onEdit()" title="edit" class="btn-edit btn-icon-unstyled">
      <mat-icon class="material-symbols-rounded">edit_square</mat-icon>
    </button>
    }
  </nav>
  <button mat-flat-button color="primary" (click)="goBack()" aria-label="Go back" class="back-btn">
    <mat-icon>arrow_back_ios</mat-icon>
    Back
  </button>
</mat-toolbar>

<div class="card component">
  <form [formGroup]="form" (ngSubmit)="onSubmit()">
    <div class="form">

      <div class="field">
        <label for="delayedDaysType" class="required-label">{{'DelayedDayCutType' | translate}}</label>
        <mat-form-field appearance="outline">
          <mat-select id="delayedDaysType" formControlName="delayedDaysType">
            <mat-form-field class="select-search hide-subscript" appearance="outline">
              <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
              <input #search autocomplete="off" matInput placeholder="Search" (input)="filter($event)">
            </mat-form-field>
            <!-- <mat-option value="null" disabled selected>Select Delayed Day Cut Type</mat-option> -->
            @for (role of filteredDelayedDays(); track role) {
            <mat-option [value]="role.key">{{role.value}}</mat-option>
            }
          </mat-select>
          <mat-error>
            @if(form.controls.delayedDaysType.errors?.['required']) {
            <!-- Delayed Day cut type is required -->
            {{'DelayedDayCutType' | translate}} {{'IsRequired' | translate}}
            }
          </mat-error>
        </mat-form-field>
      </div>

      <div class="field">
        <label for="seasonId" class="required-label">{{'Season' | translate}}</label>
        <mat-form-field appearance="outline">
          <mat-select id="seasonId" formControlName="seasonId">
            <mat-form-field class="select-search hide-subscript" appearance="outline">
              <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
              <input #search autocomplete="off" matInput placeholder="Search" (input)="filter($event)">
            </mat-form-field>
            <!-- <mat-option value="null" disabled selected>Select Season</mat-option> -->
            @for (role of filteredSeason(); track role) {
            <mat-option [value]="role.key">{{role.value}}</mat-option>
            }
          </mat-select>
          <mat-error>
            @if(form.controls.seasonId.errors?.['required']) {
            <!-- Season is required -->
            {{'Season' | translate}} {{'IsRequired' | translate}}
            }
          </mat-error>
        </mat-form-field>
      </div>

      <div class="field">
        <label for="regionId" class="required-label">{{'Region' | translate}}</label>
        <mat-form-field appearance="outline">
          <mat-select id="regionId" formControlName="regionId">
            <mat-form-field class="select-search hide-subscript" appearance="outline">
              <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
              <input #search autocomplete="off" matInput placeholder="Search" (input)="filter($event)">
            </mat-form-field>
            <!-- <mat-option value="null" disabled selected>Select Region</mat-option> -->
            @for (role of filteredRegions(); track role) {
            <mat-option [value]="role.key">{{role.value}}</mat-option>
            }
          </mat-select>
          <mat-error>
            @if(form.controls.regionId.errors?.['required']) {
            {{'Region' | translate}} {{'IsRequired' | translate}}
            }
          </mat-error>
        </mat-form-field>
      </div>


      <div class="field">
        <label for="priceCutAmount" class="required-label">{{'PriceCutAmount' | translate}}</label>
        <mat-form-field>
          <input id="priceCutAmount" formControlName="priceCut" matInput (input)="onInput($event,'/^[0-9.]*$/')"
            maxlength="10">
          <mat-error>
            @if(form.controls.priceCut.errors?.['required']) {
            {{'PriceCutAmount' | translate}} {{'IsRequired' | translate}}
            }
          </mat-error>
        </mat-form-field>
      </div>






    </div>

    <div class="actions">

      @if(editable() === true) {
      @if(mode() === 'add') {
      <button type="button" mat-flat-button (click)="formReset()" class="btn btn-danger">{{'Reset' |
        translate}}</button>
      }
      <button [disabled]="editable() === false" mat-flat-button class="btn btn-theme" type="submit">{{uniqueId() == 0
        ? ('Create' | translate) : ('Update' | translate)}}</button>
      }
    </div>


  </form>
</div>