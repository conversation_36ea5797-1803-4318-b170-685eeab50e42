.shimmer {
  --gradient-loader-background-color: #f9f9f9;
  animation-duration: 1.25s;
  animation-fill-mode: forwards;
  animation-iteration-count: infinite;
  animation-name: gradient-loader;
  animation-timing-function: linear;
  background: #d7d7d7;
  //background: linear-gradient(to right, var(--gradient-loader-background-color, #0000001a) 10%, #0000000d 18%, var(--gradient-loader-background-color, #0000001a) 33%);
  background: linear-gradient(to right, var(--gradient-loader-background-color, #0000001a) 10%, rgba(0, 0, 0, 0.05) 28%, var(--gradient-loader-background-color, #0000001a) 70%);
  background-size: 1000px 104px;
  border-radius: 4px;
  flex-shrink: 0;
  height: 20px;
  position: relative;
  width: 100%;
  max-width: 1000px;
}

@keyframes gradient-loader {
  0% {
    background-position: -500px 0;
}
100% {
    background-position: 500px 0;
}
}
