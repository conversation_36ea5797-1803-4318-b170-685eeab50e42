import { NgTemplateOutlet, DatePipe } from '@angular/common';
import { Component, computed, effect, inject, linkedSignal, OnInit, resource, signal, viewChild } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInput, MatPrefix } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { StatusType } from '../../../../enums/x_enums/dotnet/status-type';
import { AppRoutes } from '../../../../enums/app-routes';
import { confirmAndDelete } from '../../../../models/x_models/masters/shared';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { TruckformComponent } from './truckform.component';
import { TruckService } from '../../../../services/x_apis/acknowledge/truck.service';
import { TruckList } from '../../../../models/x_models/acknowledge/truck';

@Component({
  selector: 'app-trucklist',
  imports: [MatIcon,
    MatInput,
    MatPrefix,
    ReactiveFormsModule,
    MatPaginatorModule,
    MatTableModule,
    NgTemplateOutlet,
    MatButtonModule,
    MatMenuModule,
    DatePipe,
    MatSortModule,
    MatFormFieldModule,
    TranslateModule,
    TruckformComponent],
    providers: [DatePipe],
  templateUrl: './trucklist.component.html',
  styleUrls: ['./trucklist.component.scss']
})
export class TrucklistComponent implements OnInit {
  menuService = inject(MenuService);
  access = computed(() => this.menuService.activeMenu()?.controlAccess);
  translate = inject(TranslateService);

  paginator = viewChild<MatPaginator>(MatPaginator);
  sort = viewChild<MatSort>(MatSort);

  truckService = inject(TruckService)
  listResource = resource({ loader: () => this.truckService.get() });
  dataSource = linkedSignal(() => new MatTableDataSource(this.listResource.value()));

  loading = signal(false);
  mode = signal<'view' | 'edit' | 'add'>('add');
  search = new FormControl("");
  showGrid = signal(false);
  status = StatusType;

  list = signal<TruckList>({
    id: 0,
    truckMemoId: '',
    truckMemoDate: '',
    truckFrom: '',
    product: '',
    vehicleNo: '',
    totalQty: 0,
    noOfBags : 0,
    gunnyType : '',
    moisture : 0,
    storageTypeFrom: ''
  });

  displayedColumns: string[] = [
    'truckMemoId',
    'truckMemoDate',
    'truckFrom',
    'product',
    'vehicleNo',
    'totalQty',
    'noOfBags',
    'gunnyType',
    'moisture',
    'storageTypeFrom',
    'actions',
  ];

  protected readonly AppRoutes = AppRoutes;  

  constructor() {
    effect(() => {
      if (this.paginator()) {
        this.dataSource().sort = this.sort()!;
        this.dataSource().paginator = this.paginator()!
      }
     
    });
  }

  ngOnInit(): void {
  }

  onAdd() {
    this.list.set({
      id: 0,
      truckMemoId: '',
      truckMemoDate: '',
      truckFrom: '',
      product: '',
      vehicleNo: '',
      totalQty: 0,
      noOfBags : 0,
      gunnyType : '',
      moisture : 0,
      storageTypeFrom: ''
    });
    this.mode.set("add")
    this.showGrid.set(true)
  }

  onView(data: TruckList) {
    this.list.set(data);
    this.mode.set('view');
    this.showGrid.set(true)
  }

  onEdit(data: TruckList) {
    this.list.set(data);
    this.mode.set('edit');
    this.showGrid.set(true)
  }

  onClose() {
    this.showGrid.set(false)
    this.listResource.reload();
    this.search.setValue("");
  }

  onRefresh() {
    this.listResource.reload();
    this.search.setValue("");
  }

  async onDelete(data: TruckList) {
    await confirmAndDelete(data, data.truckMemoId, 'Truck Acknowledgement', this.truckService, () => this.listResource.reload());
  }

  onSearch() {
    const filterValue = this.search.value?.trim().toLowerCase() || '';
    this.dataSource().filter = filterValue;
  }

}
