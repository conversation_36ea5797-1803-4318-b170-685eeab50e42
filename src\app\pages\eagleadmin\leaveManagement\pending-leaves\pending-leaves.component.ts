import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatChipsModule } from '@angular/material/chips';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { ActivatedRoute, Router } from '@angular/router';
import { LeaveManagementService } from '../../../../services/m_apis/service/leave-management.service';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';

@Component({
  selector: 'app-pending-leaves',
  imports: [CommonModule,
 
       ReactiveFormsModule,
       FormsModule,
       MatCardModule,
       MatFormFieldModule,
       MatInputModule,
       MatSelectModule,
       MatButtonModule,
       MatIconModule,
       MatDatepickerModule,
       MatNativeDateModule,
       MatChipsModule,
       MatSnackBarModule,
       MatProgressSpinnerModule,
       MatTooltipModule,
       MatDatepickerModule,
       MatTableModule,
  ],
  templateUrl: './pending-leaves.component.html',
  styleUrl: './pending-leaves.component.scss'
})
export class PendingLeavesComponent implements OnInit {
  displayedColumns: string[] = ['leaveType', 'fromDate', 'toDate', 'reason', 'status'];
  pendingLeaves:any[] = [];
  employeeId: string | null = '';
  isLoading = false;
  dataSource = new MatTableDataSource<any>([]);
  totaldays: any;

  constructor(private leaveService:LeaveManagementService,
    private route:ActivatedRoute, private router:Router,private snackBar: MatSnackBar
  ) {
    // this.employeeId = this.route.snapshot.paramMap.get('employeeId');
    this.employeeId = 'EMP040';
  }
  ngOnInit(): void {
    this.getPendingLeaveByEmpId();
  }

  getTotalDays(): number {
    if (!this.dataSource.data || this.dataSource.data.length === 0) {
      return 0;
    }
    return this.dataSource.data.reduce((sum: number, leave: any) => sum + leave.totalDays, 0);
  }
  

  getPendingLeaveByEmpId(): void {
    this.isLoading = true;
    this.leaveService.getPendingLeavesByEmpId(this.employeeId).subscribe({
      next: (response) => {
        this.pendingLeaves = response.data;
        console.log('Pending Leaves:', this.pendingLeaves); // Log the data
        this.dataSource.data = this.pendingLeaves; // Ensure dataSource is updated
        this.getTotalDays();
      },
      error: (error) => {
        console.error('Error fetching pending leave:', error);
      },
      complete: () => {
        this.isLoading = false;
      }
    });
  }

  onHistoryColor(status: string): string {
    switch (status) {
      case 'PENDING':
        return 'orange';
      case 'APPROVED':
        return 'green';
      case 'REJECTED':
        return 'red';
      default:
        return 'gray';
    }
  }

  applyFilter(event: Event): void {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    // Custom filter predicate
    this.dataSource.filterPredicate = (data: any, filter: string) => {
      return data.leaveTypeName.toLowerCase().includes(filter) ||
             data.fromDate.toLowerCase().includes(filter) ||
             data.toDate.toLowerCase().includes(filter) ||
             data.reason.toLowerCase().includes(filter) ||
             data.status.toLowerCase().includes(filter);
    };
  }

  getStatusChipClass(status: string): string {
    switch (status.toUpperCase()) {
      case 'PENDING':
        return 'status-pending';
      case 'APPROVED':
        return 'status-approved';
      case 'REJECTED':
        return 'status-rejected';
      default:
        return 'status-default';
    }
  }

 

  // calculateTotalDays(): void {
  //   this.totaldays = this.pendingLeaves.reduce((sum, leave) => {
  //     const start = new Date(leave.fromDate);
  //     const end = new Date(leave.toDate);
  //     let days = (end.getTime() - start.getTime()) / (1000 * 3600 * 24) + 1;

  //     // Adjust for half-day selections
  //     if (start.getTime() === end.getTime()) {
  //       if ((leave.fromSession === 'FIRST_HALF' && leave.toSession === 'SECOND_HALF') ||
  //           (leave.fromSession === 'SECOND_HALF' && leave.toSession === 'FIRST_HALF')) {
  //         days = 1; // Full day
  //       } else {
  //         days = 0.5; // Half day
  //       }
  //     } else {
  //       if (leave.fromSession === 'SECOND_HALF') days -= 0.5;
  //       if (leave.toSession === 'FIRST_HALF') days -= 0.5;
  //     }

  //     return sum + days;
  //   }, 0);
  // }

}
