// export interface LiveTrack {
//     id: number
//     vehicleId:number,
//     regNo: string,
//     regionName: string,
//     gpsDeviceTs: string,
//     mainPowerStatusType: number,
//     vehicleStatusType: number
// }
export interface Vehicles {
    id: number
    vehicleId:number,
    regNo: string,
    regionName: string,
    gpsDeviceTs: string,
    mainPowerStatusType: number,
    vehicleStatusTypeName : string
    vehicleStatusType?: number
}
export interface VehicleCard {
    title: string;
    total: number;
    imageUrl: string;
    status?:number
  }

export type VehicleResponse = Vehicles[]