<div class="leave-balance-container">
    <div class="header">
      <h1 class="page-title">
        <mat-icon>event_available</mat-icon>
        Leave Balance
      </h1>
      <!-- <p class="subtitle">Track your leave allocation and usage</p> -->
    </div>
  
    <div class="leave-cards-grid">
      <mat-card 
        *ngFor="let leave of leaveData" 
        class="leave-card"
        [style.border-left]="'4px solid ' + leave.color">
        
        <!-- Top Section: Leave Type and Granted -->
        <div class="card-header">
          <div class="leave-type">
            <mat-icon [style.color]="leave.color">{{ leave.icon }}</mat-icon>
            <span class="type-text">{{ leave.type }}</span>
          </div>
          <div class="granted-section">
            <span class="granted-label">Granted</span>
            <span class="granted-value">{{ leave.granted }}</span>
          </div>
        </div>
  
        <!-- Center Section: Balance -->
        <div class="balance-section">
          <div class="balance-circle" [style.border-color]="leave.color">
            <span class="balance-number">{{ leave.balance }}</span>
            <span class="balance-label">Available</span>
          </div>
        </div>
  
        <!-- Progress Bar -->
        <div class="progress-section">
          <mat-progress-bar 
            mode="determinate" 
            [value]="getProgressPercentage(leave.consumed, leave.granted)"
            [color]="'primary'">
          </mat-progress-bar>
          <div class="progress-labels">
            <span>Used: {{ leave.consumed }}</span>
            <span>{{ getProgressPercentage(leave.consumed, leave.granted) | number:'1.0-0' }}%</span>
          </div>
        </div>
  
        <!-- Bottom Section: Consumed -->
        <div class="consumed-section">
          <div class="consumed-info">
            <mat-icon>remove_circle_outline</mat-icon>
            <span>Consumed: <strong>{{ leave.consumed }}</strong> days</span>
          </div>
        </div>
      </mat-card>
    </div>
  
    <!-- Summary Section -->
    <mat-card class="summary-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>assessment</mat-icon>
          Leave Summary
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="summary-grid">
          <div class="summary-item">
            <span class="summary-label">Total Granted</span>
            <span class="summary-value">{{ getTotalGranted() }}</span>
          </div>
          <div class="summary-item">
            <span class="summary-label">Total Consumed</span>
            <span class="summary-value consumed">{{ getTotalConsumed() }}</span>
          </div>
          <div class="summary-item">
            <span class="summary-label">Total Available</span>
            <span class="summary-value available">{{ getTotalAvailable() }}</span>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>