<div class="container">
  <mat-card class="form-card">
    <mat-card-header class="card-header">
      <mat-card-title>
        <mat-icon class="header-icon">flight_takeoff</mat-icon>
        <h1>Inter-Region Transfer</h1>
      </mat-card-title>
      <mat-card-subtitle>
        Request transfer to a different region with relocation support options
      </mat-card-subtitle>
    </mat-card-header>

    <mat-progress-bar
      *ngIf="isLoading"
      mode="indeterminate"
      class="progress-bar">
    </mat-progress-bar>

    <mat-card-content class="card-content">
      <form [formGroup]="transferForm" (ngSubmit)="onSubmit()" class="form-container transfer-form">

        <!-- Current Information Section -->
        <div class="section">
          <h3 class="section-title">
            <mat-icon>info</mat-icon>
            Current Information
          </h3>
          <mat-divider></mat-divider>

          <div class="form-grid-3-columns">
            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Employee Name</mat-label>
              <input matInput formControlName="employeeName" readonly>
              <mat-icon matSuffix>person</mat-icon>
            </mat-form-field>

            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Gender</mat-label>
              <input matInput formControlName="gender" readonly>
              <mat-icon matSuffix>wc</mat-icon>
            </mat-form-field>

            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Mobile Number</mat-label>
              <input matInput formControlName="mobileNumber" readonly>
              <mat-icon matSuffix>phone</mat-icon>
            </mat-form-field>

            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Email ID</mat-label>
              <input matInput formControlName="email" readonly>
              <mat-icon matSuffix>email</mat-icon>
            </mat-form-field>

            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Location</mat-label>
              <input matInput formControlName="location" readonly>
              <mat-icon matSuffix>place</mat-icon>
            </mat-form-field>

            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Date of Birth</mat-label>
              <input matInput formControlName="dateOfBirth" readonly>
              <mat-icon matSuffix>cake</mat-icon>
            </mat-form-field>

            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Designation</mat-label>
              <input matInput formControlName="designation" readonly>
              <mat-icon matSuffix>work</mat-icon>
            </mat-form-field>

            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Father Name</mat-label>
              <input matInput formControlName="fatherName" readonly>
              <mat-icon matSuffix>family_restroom</mat-icon>
            </mat-form-field>

            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Emp.ID/ECPF.No</mat-label>
              <input matInput formControlName="empIdOrEcpfNo" readonly>
              <mat-icon matSuffix>badge</mat-icon>
            </mat-form-field>

            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Employee Code</mat-label>
              <input matInput formControlName="employeeCode" readonly>
              <mat-icon matSuffix>confirmation_number</mat-icon>
            </mat-form-field>

            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Current Region</mat-label>
              <input matInput formControlName="currentRegion" readonly>
              <mat-icon matSuffix>location_on</mat-icon>
            </mat-form-field>

            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Current Department</mat-label>
              <input matInput formControlName="currentDepartment" readonly>
              <mat-icon matSuffix>business</mat-icon>
            </mat-form-field>

            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Current Position</mat-label>
              <input matInput formControlName="currentPosition" readonly>
              <mat-icon matSuffix>work_outline</mat-icon>
            </mat-form-field>

            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Current Grade</mat-label>
              <input matInput formControlName="currentGrade" readonly>
              <mat-icon matSuffix>grade</mat-icon>
            </mat-form-field>
          </div>
        </div>

        <!-- Transfer Request Details Section -->
        <div class="section">
          <h3 class="section-title">
            <mat-icon>assignment</mat-icon>
            Transfer Request Details
          </h3>
          <mat-divider></mat-divider>

          <div class="form-grid-3-columns">
            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Proposed Region <span class="required-asterisk">*</span></mat-label>
              <mat-select formControlName="proposedRegion" (selectionChange)="onRegionChange($event)">
                <mat-option *ngFor="let region of availableRegions" [value]="region.id">
                  {{ region.name }} ({{ region.code }})
                </mat-option>
              </mat-select>
              <mat-icon matSuffix>location_on</mat-icon>
              <mat-error *ngIf="transferForm.get('proposedRegion')?.invalid && transferForm.get('proposedRegion')?.touched">
                Please select a region
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Proposed Department</mat-label>
              <mat-select formControlName="proposedDepartment" (selectionChange)="onDepartmentChange($event)">
                <mat-option *ngFor="let dept of availableDepartments" [value]="dept.id">
                  {{ dept.name }}
                </mat-option>
              </mat-select>
              <mat-icon matSuffix>business</mat-icon>
            </mat-form-field>

            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Proposed Position</mat-label>
              <mat-select formControlName="proposedPosition">
                <mat-option *ngFor="let pos of availablePositions" [value]="pos.id">
                  {{ pos.title }} ({{ pos.grade }})
                </mat-option>
              </mat-select>
              <mat-icon matSuffix>work</mat-icon>
            </mat-form-field>

            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Priority Level <span class="required-asterisk">*</span></mat-label>
              <mat-select formControlName="priority">
                <mat-option value="LOW">
                  <span class="priority-option low">Low</span>
                </mat-option>
                <mat-option value="MEDIUM">
                  <span class="priority-option medium">Medium</span>
                </mat-option>
                <mat-option value="HIGH">
                  <span class="priority-option high">High</span>
                </mat-option>
                <mat-option value="URGENT">
                  <span class="priority-option urgent">Urgent</span>
                </mat-option>
              </mat-select>
              <mat-icon matSuffix>priority_high</mat-icon>
              <mat-error *ngIf="transferForm.get('priority')?.invalid && transferForm.get('priority')?.touched">
                Please select priority level
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Preferred Joining Date</mat-label>
              <input matInput [matDatepicker]="picker" formControlName="preferredJoiningDate" [min]="minDate">
              <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
              <mat-datepicker #picker></mat-datepicker>
            </mat-form-field>
          </div>
        </div>

        <!-- Relocation Support Section -->
        <div class="section">
          <h3 class="section-title">
            <mat-icon>moving</mat-icon>
            Relocation Support
          </h3>
          <mat-divider></mat-divider>

          <div class="relocation-options">
            <mat-slide-toggle
              formControlName="relocationRequired"
              (change)="onRelocationToggle($event)"
              class="relocation-toggle">
              Request Relocation Support
            </mat-slide-toggle>

            <div *ngIf="transferForm.get('relocationRequired')?.value" class="relocation-details">
              <div class="form-grid">
                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>Estimated Relocation Date</mat-label>
                  <input matInput [matDatepicker]="relocationPicker" formControlName="estimatedRelocationDate" [min]="minDate">
                  <mat-datepicker-toggle matSuffix [for]="relocationPicker"></mat-datepicker-toggle>
                  <mat-datepicker #relocationPicker></mat-datepicker>
                </mat-form-field>

                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>Current Accommodation Type</mat-label>
                  <mat-select formControlName="currentAccommodationType">
                    <mat-option value="OWNED">Owned House</mat-option>
                    <mat-option value="RENTED">Rented House</mat-option>
                    <mat-option value="COMPANY">Company Accommodation</mat-option>
                    <mat-option value="FAMILY">Family House</mat-option>
                  </mat-select>
                </mat-form-field>

                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>Preferred Accommodation Type</mat-label>
                  <mat-select formControlName="preferredAccommodationType">
                    <mat-option value="COMPANY">Company Accommodation</mat-option>
                    <mat-option value="RENTED">Rented House</mat-option>
                    <mat-option value="OWNED">Purchase House</mat-option>
                  </mat-select>
                </mat-form-field>

                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Special Relocation Requirements</mat-label>
                  <textarea
                    matInput
                    formControlName="relocationRequirements"
                    rows="3"
                    placeholder="Any specific requirements for relocation (e.g., school for children, medical facilities, etc.)">
                  </textarea>
                </mat-form-field>
              </div>
            </div>
          </div>
        </div>

        <!-- Family Information Section -->
        <div class="section" *ngIf="transferForm.get('relocationRequired')?.value">
          <h3 class="section-title">
            <mat-icon>family_restroom</mat-icon>
            Family Information
          </h3>
          <mat-divider></mat-divider>

          <div class="family-section">
            <div class="family-header">
              <button
                mat-stroked-button
                color="primary"
                type="button"
                (click)="addFamilyMember()"
                class="add-family-btn">
                <mat-icon>add</mat-icon>
                Add Family Member
              </button>
            </div>

            <div *ngFor="let member of familyMembers.controls; let i = index" class="family-member" [formGroupName]="i">
              <div class="family-member-header">
                <h4>Family Member {{ i + 1 }}</h4>
                <button
                  mat-icon-button
                  color="warn"
                  type="button"
                  (click)="removeFamilyMember(i)"
                  *ngIf="familyMembers.length > 0">
                  <mat-icon>delete</mat-icon>
                </button>
              </div>

              <div class="form-grid">
                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>Name</mat-label>
                  <input matInput formControlName="name">
                </mat-form-field>

                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>Relationship</mat-label>
                  <mat-select formControlName="relationship">
                    <mat-option value="SPOUSE">Spouse</mat-option>
                    <mat-option value="CHILD">Child</mat-option>
                    <mat-option value="PARENT">Parent</mat-option>
                    <mat-option value="SIBLING">Sibling</mat-option>
                    <mat-option value="OTHER">Other</mat-option>
                  </mat-select>
                </mat-form-field>

                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>Age</mat-label>
                  <input matInput type="number" formControlName="age" min="0" max="100">
                </mat-form-field>

                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>Occupation</mat-label>
                  <input matInput formControlName="occupation">
                </mat-form-field>

                <div class="form-field">
                  <mat-slide-toggle formControlName="schoolingRequired">
                    Schooling Required
                  </mat-slide-toggle>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Reason and Justification Section -->
        <div class="section">
          <h3 class="section-title">
            <mat-icon>description</mat-icon>
            Reason and Justification
          </h3>
          <mat-divider></mat-divider>

          <div class="form-grid">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Reason for Transfer <span class="required-asterisk">*</span></mat-label>
              <textarea
                matInput
                formControlName="reason"
                rows="3">
              </textarea>
              <mat-icon matSuffix>description</mat-icon>
              <mat-hint>Minimum 10 characters required</mat-hint>
              <mat-error *ngIf="transferForm.get('reason')?.invalid && transferForm.get('reason')?.touched">
                {{getErrorMessage('reason')}}
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Additional Justification</mat-label>
              <textarea
                matInput
                formControlName="justification"
                rows="3"
                placeholder=" ">
              </textarea>
              <mat-icon matSuffix>note_add</mat-icon>
            </mat-form-field>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="form-actions">
          <button
            mat-raised-button
            color="primary"
            type="submit"
            [disabled]="transferForm.invalid || isLoading"
            class="submit-btn">
            <mat-icon *ngIf="!isLoading">send</mat-icon>
            <mat-spinner *ngIf="isLoading" diameter="20"></mat-spinner>
            {{ isLoading ? 'Submitting...' : 'Submit Transfer Request' }}
          </button>

          <button
            mat-stroked-button
            color="accent"
            type="button"
            (click)="saveDraft()"
            [disabled]="isLoading"
            class="draft-btn">
            <mat-icon>save</mat-icon>
            Save as Draft
          </button>

          <button
            mat-button
            color="warn"
            type="button"
            (click)="resetForm()"
            [disabled]="isLoading"
            class="reset-btn">
            <mat-icon>refresh</mat-icon>
            Reset Form
          </button>
        </div>
      </form>
    </mat-card-content>
  </mat-card>
</div>
