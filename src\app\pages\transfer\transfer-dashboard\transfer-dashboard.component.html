<div class="container">
  <!-- Header Section -->
  <div class="header-section">
    <div class="page-title">
      <mat-icon class="title-icon">dashboard</mat-icon>
      <h1>Transfer Dashboard</h1>
    </div>
    <p class="page-subtitle">Manage your transfer requests and track their progress</p>
  </div>

  <!-- Quick Actions Section -->
  <mat-card class="quick-actions-card">
    <mat-card-header>
      <mat-card-title>
        <mat-icon>add_circle</mat-icon>
        Quick Actions
      </mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <div class="action-buttons">
        <button
          mat-raised-button
          color="primary"
          routerLink="/within-region"
          class="action-btn">
          <mat-icon>swap_horiz</mat-icon>
          Transfer Within Region
        </button>

        <button
          mat-raised-button
          color="primary"
          routerLink="/inter-region"
          class="action-btn">
          <mat-icon>flight_takeoff</mat-icon>
          Inter-Region Transfer
        </button>

        <button
          mat-raised-button
          color="primary"
          routerLink="/mutual-transfer"
          class="action-btn">
          <mat-icon>swap_horizontal_circle</mat-icon>
          Mutual Transfer
        </button>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Statistics Section -->
  <div class="stats-section">
    <mat-card class="stat-card pending">
      <mat-card-content>
        <div class="stat-content">
          <div class="stat-icon">
            <mat-icon>pending</mat-icon>
          </div>
          <div class="stat-info">
            <h3>{{ statistics.pending }}</h3>
            <p>Pending Requests</p>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <mat-card class="stat-card approved">
      <mat-card-content>
        <div class="stat-content">
          <div class="stat-icon">
            <mat-icon>check_circle</mat-icon>
          </div>
          <div class="stat-info">
            <h3>{{ statistics.approved }}</h3>
            <p>Approved Requests</p>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <mat-card class="stat-card rejected">
      <mat-card-content>
        <div class="stat-content">
          <div class="stat-icon">
            <mat-icon>cancel</mat-icon>
          </div>
          <div class="stat-info">
            <h3>{{ statistics.rejected }}</h3>
            <p>Rejected Requests</p>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <mat-card class="stat-card completed">
      <mat-card-content>
        <div class="stat-content">
          <div class="stat-icon">
            <mat-icon>done_all</mat-icon>
          </div>
          <div class="stat-info">
            <h3>{{ statistics.completed }}</h3>
            <p>Completed Transfers</p>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Filters and Search Section -->
  <mat-card class="filters-card">
    <mat-card-header>
      <mat-card-title>
        <mat-icon>filter_list</mat-icon>
        Filter & Search
      </mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <div class="filters-grid">
        <mat-form-field appearance="outline" class="filter-field">
          <mat-label>Search Requests</mat-label>
          <input matInput
                 [(ngModel)]="searchTerm"
                 (input)="onSearch()"
                 placeholder="Search by request number, employee name...">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>

        <mat-form-field appearance="outline" class="filter-field">
          <mat-label>Filter by Status</mat-label>
          <mat-select [(ngModel)]="selectedStatus" (selectionChange)="onStatusFilter()">
            <mat-option value="">All Status</mat-option>
            <mat-option value="DRAFT">Draft</mat-option>
            <mat-option value="SUBMITTED">Submitted</mat-option>
            <mat-option value="UNDER_REVIEW">Under Review</mat-option>
            <mat-option value="APPROVED_BY_SUPERVISOR">Approved by Supervisor</mat-option>
            <mat-option value="APPROVED_BY_HR">Approved by HR</mat-option>
            <mat-option value="COMPLETED">Completed</mat-option>
            <mat-option value="REJECTED">Rejected</mat-option>
            <mat-option value="CANCELLED">Cancelled</mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="outline" class="filter-field">
          <mat-label>Filter by Type</mat-label>
          <mat-select [(ngModel)]="selectedType" (selectionChange)="onTypeFilter()">
            <mat-option value="">All Types</mat-option>
            <mat-option value="WITHIN_REGION">Within Region</mat-option>
            <mat-option value="INTER_REGION">Inter Region</mat-option>
            <mat-option value="MUTUAL">Mutual Transfer</mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="outline" class="filter-field">
          <mat-label>Filter by Priority</mat-label>
          <mat-select [(ngModel)]="selectedPriority" (selectionChange)="onPriorityFilter()">
            <mat-option value="">All Priorities</mat-option>
            <mat-option value="LOW">Low</mat-option>
            <mat-option value="MEDIUM">Medium</mat-option>
            <mat-option value="HIGH">High</mat-option>
            <mat-option value="URGENT">Urgent</mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Transfer Requests List -->
  <mat-card class="requests-card">
    <mat-card-header>
      <mat-card-title>
        <mat-icon>list</mat-icon>
        Your Transfer Requests
      </mat-card-title>
      <mat-card-subtitle>
        {{ filteredRequests.length }} of {{ transferRequests.length }} requests
      </mat-card-subtitle>
    </mat-card-header>
    <mat-card-content>
      <div *ngIf="isLoading" class="loading-container">
        <mat-spinner diameter="40"></mat-spinner>
        <p>Loading transfer requests...</p>
      </div>

      <div *ngIf="!isLoading && filteredRequests.length === 0" class="no-requests">
        <mat-icon>inbox</mat-icon>
        <h3>No Transfer Requests Found</h3>
        <p>You haven't submitted any transfer requests yet, or no requests match your current filters.</p>
        <button mat-raised-button color="primary" routerLink="/within-region">
          <mat-icon>add</mat-icon>
          Submit Your First Request
        </button>
      </div>

      <div *ngIf="!isLoading && filteredRequests.length > 0" class="requests-list">
        <div *ngFor="let request of filteredRequests" class="request-item">
          <mat-card class="request-card" [ngClass]="getStatusClass(request.status)">
            <mat-card-header>
              <div mat-card-avatar class="request-avatar">
                <mat-icon>{{ getTransferTypeIcon(request.transferType) }}</mat-icon>
              </div>
              <mat-card-title>{{ request.requestNumber }}</mat-card-title>
              <mat-card-subtitle>{{ getTransferTypeLabel(request.transferType) }}</mat-card-subtitle>
              <div class="request-actions">
                <button mat-icon-button [matMenuTriggerFor]="requestMenu">
                  <mat-icon>more_vert</mat-icon>
                </button>
                <mat-menu #requestMenu="matMenu">
                  <button mat-menu-item (click)="viewRequest(request)">
                    <mat-icon>visibility</mat-icon>
                    View Details
                  </button>
                  <button mat-menu-item (click)="editRequest(request)" *ngIf="canEdit(request)">
                    <mat-icon>edit</mat-icon>
                    Edit Request
                  </button>
                  <button mat-menu-item (click)="cancelRequest(request)" *ngIf="canCancel(request)">
                    <mat-icon>cancel</mat-icon>
                    Cancel Request
                  </button>
                  <button mat-menu-item (click)="downloadRequest(request)">
                    <mat-icon>download</mat-icon>
                    Download PDF
                  </button>
                </mat-menu>
              </div>
            </mat-card-header>

            <mat-card-content>
              <div class="request-details">
                <div class="detail-row">
                  <span class="label">Status:</span>
                  <span class="status-badge" [ngClass]="getStatusClass(request.status)">
                    {{ getStatusLabel(request.status) }}
                  </span>
                </div>

                <div class="detail-row">
                  <span class="label">Priority:</span>
                  <span class="priority-badge" [ngClass]="getPriorityClass(request.priority)">
                    {{ request.priority }}
                  </span>
                </div>

                <div class="detail-row">
                  <span class="label">From:</span>
                  <span>{{ request.currentRegionName }} - {{ request.currentDepartmentName }}</span>
                </div>

                <div class="detail-row">
                  <span class="label">To:</span>
                  <span>{{ request.proposedRegionName }} - {{ request.proposedDepartmentName }}</span>
                </div>

                <div class="detail-row" *ngIf="request.submittedDate">
                  <span class="label">Submitted:</span>
                  <span>{{ request.submittedDate | date:'medium' }}</span>
                </div>

                <div class="detail-row" *ngIf="request.preferredJoiningDate">
                  <span class="label">Preferred Joining:</span>
                  <span>{{ request.preferredJoiningDate | date:'mediumDate' }}</span>
                </div>
              </div>

              <!-- Progress Indicator -->
              <div class="progress-section" *ngIf="request.approvalWorkflow && request.approvalWorkflow.length > 0">
                <h4>Approval Progress</h4>
                <div class="progress-steps">
                  <div *ngFor="let step of request.approvalWorkflow; let i = index"
                       class="progress-step"
                       [ngClass]="getStepClass(step.status)">
                    <div class="step-icon">
                      <mat-icon *ngIf="step.status === 'APPROVED'">check</mat-icon>
                      <mat-icon *ngIf="step.status === 'REJECTED'">close</mat-icon>
                      <mat-icon *ngIf="step.status === 'PENDING'">schedule</mat-icon>
                    </div>
                    <div class="step-content">
                      <div class="step-title">{{ step.approverRole }}</div>
                      <div class="step-status">{{ step.status }}</div>
                      <div class="step-date" *ngIf="step.actionDate">
                        {{ step.actionDate | date:'short' }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </mat-card-content>

            <mat-card-actions>
              <button mat-button color="primary" (click)="viewRequest(request)">
                <mat-icon>visibility</mat-icon>
                View Details
              </button>
              <button mat-button color="accent" (click)="trackProgress(request)" *ngIf="hasApprovalWorkflow(request)">
                <mat-icon>timeline</mat-icon>
                Track Progress
              </button>
            </mat-card-actions>
          </mat-card>
        </div>
      </div>
    </mat-card-content>
  </mat-card>
</div>
