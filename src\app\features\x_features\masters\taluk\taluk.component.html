<mat-toolbar color="primary" class="navbar">
    <nav aria-label="Breadcrumb navigation" style="display: flex; align-items: center; flex-wrap: wrap;">
      <a routerLink="/" class="crumb">{{'Master' | translate}}</a>
      <mat-icon>chevron_right</mat-icon>
      <a class="crumb" (click)="goBack()">{{menuService.activeMenu()?.title}}</a>
      <mat-icon>chevron_right</mat-icon>
      <span aria-current="page" class="nav-active">
        @if(talukId() == 0) {
        {{'New' | translate}} {{menuService.activeMenu()?.title}}
        } @else {
            {{taluk()?.talukName}}
        }
      </span>
      @if(mode() === "view") {
      <button (click)="onEdit()" title="edit" class="btn-edit btn-icon-unstyled">
        <mat-icon class="material-symbols-rounded">edit_square</mat-icon>
      </button>
      }
    </nav>
    <button mat-flat-button color="primary" (click)="goBack()" aria-label="Go back" class="back-btn">
      <mat-icon>arrow_back_ios</mat-icon>
      Back
    </button>
  </mat-toolbar>
  
    <div class="card component">

        <form [formGroup]="form" (ngSubmit)="onSubmit()">
            <div class="form">

                <div class="field">
                    <label for="talukName" class="required-label">{{'Taluk' | translate}}</label>
                    <mat-form-field>
                        <input id="talukName" formControlName="talukName" matInput maxlength="100"
                            (input)="onInput($event,'/^[0-9A-Za-z ]*$/')">
                        <mat-error>
                            @if(form.controls.talukName.errors?.['required']) {
                            <!-- Taluk is required -->
                            {{'Taluk' | translate}} {{'IsRequired' | translate}}
                            }
                        </mat-error>
                    </mat-form-field>
                </div>

                <div class="field">
                    <label for="talukRegionalName" class="required-label">{{'Taluk' | translate}} {{'InTamil' |
                        translate}}</label>
                    <mat-form-field>
                        <input id="talukRegionalName" formControlName="talukRegionalName" matInput
                            (input)="onInput($event,'/^[a-zA-Z0-9 ]*$/')" maxlength="100">
                        <mat-error>
                            @if(form.controls.talukRegionalName.errors?.['required']) {
                            <!-- Taluk in tamil is required -->
                            {{'Taluk' | translate}} {{'InTamil' | translate}} {{'IsRequired' | translate}}
                            }
                        </mat-error>
                    </mat-form-field>
                </div>

                <div class="field">
                    <label for="talukLgdCode" class="required-label">{{'Taluk' | translate}} {{'LGDCode' |
                        translate}}</label>
                    <mat-form-field>
                        <input id="talukLgdCode" formControlName="talukLgdCode" matInput
                            (input)="onInput($event,'/^[0-9]*$/')" maxlength="10">
                        <mat-error>
                            @if(form.controls.talukLgdCode.errors?.['required']) {
                            <!-- Taluk LGD code is required -->
                            {{'Taluk' | translate}} {{'LGDCode' | translate}} {{'IsRequired' | translate}}
                            }
                        </mat-error>
                    </mat-form-field>
                </div>


                <div class="field">
                    <label for="regionId" class="required-label">{{'Region' | translate}}</label>
                    <mat-form-field appearance="outline">
                        <mat-select id="regionId" formControlName="regionId">
                             <mat-form-field class="select-search hide-subscript" appearance="outline" (openedChange)="resetSearch('region')">
                                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                                <input #search1 autocomplete="off" matInput placeholder="Search" (input)="filter($event,'region')">
                            </mat-form-field>
                            @for (role of filteredRegions(); track role) {
                            <mat-option [value]="role.key">{{role.value}}</mat-option>
                            }
                        </mat-select>
                        <mat-error>
                            @if(form.controls.regionId.errors?.['required']) {
                            <!-- Region is required -->
                            {{'Region' | translate}} {{'IsRequired' | translate}}
                            }
                        </mat-error>
                    </mat-form-field>
                </div>

                <div class="field">
                    <label for="unitId" class="required-label">{{'Unit' | translate}}</label>
                    <mat-form-field appearance="outline">
                        <mat-select id="unitId" formControlName="unitId" (openedChange)="resetSearch('unit')">
                           <mat-form-field class="select-search hide-subscript" appearance="outline">
                                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                                <input #search2 autocomplete="off" matInput placeholder="Search" (input)="filter($event,'unit')">
                            </mat-form-field>
                            @for (role of filteredUnites(); track role) {
                            <mat-option [value]="role.key">{{role.value}}</mat-option>
                            }
                        </mat-select>
                        <mat-error>
                            @if(form.controls.unitId.errors?.['required']) {
                            <!-- Unit is required -->
                            {{'Unit' | translate}} {{'IsRequired' | translate}}
                            }
                        </mat-error>
                    </mat-form-field>
                </div>

            </div>

            <div class="actions">
               
                @if(editable() === true) {
                @if(mode() === 'add') {
                <button type="button" mat-flat-button (click)="formReset()" class="btn btn-danger">{{'Reset' |
                    translate}}</button>
                }
                <button [disabled]="editable() === false" mat-flat-button class="btn btn-theme"
                    type="submit">{{talukId()
                    == 0 ?
                    ('Create' | translate) : ('Update' | translate)}}</button>
                }
            </div>
        </form>
    </div>
