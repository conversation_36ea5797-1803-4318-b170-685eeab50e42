import { Ng<PERSON><PERSON>plateOutlet, DatePipe, CurrencyPipe } from '@angular/common';
import { Component, computed, inject, signal, viewChild } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckbox } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInput, MatPrefix } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatOption, MatSelect } from '@angular/material/select';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { CloseFilterOnBlurDirective } from '../../../../directives/close-filter-on-blur.directive';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { confirmAndDelete } from '../../../../models/x_models/masters/shared';
import { Block, Blocks } from '../../../../models/x_models/masters/block';
import { BlockService } from '../../../../services/x_apis/masters/block.service';
import { BlockComponent } from './block.component';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatToolbarModule } from '@angular/material/toolbar';

@Component({
    selector: 'app-blocks',
    imports: [MatIcon,
        MatIcon,
        MatInput,
        MatPrefix,
        ReactiveFormsModule,
        MatCheckbox,
        MatPaginatorModule,
        MatTableModule,
        NgTemplateOutlet,
        MatButtonModule,
        MatMenuModule,
        DatePipe,
        MatSortModule,
        MatOption,
        MatSelect,
        TranslateModule,
        MatToolbarModule,
        CloseFilterOnBlurDirective, CurrencyPipe, MatFormFieldModule, BlockComponent],
    templateUrl: './blocks.component.html',
    styleUrl: './blocks.component.scss'
})
export class BlocksComponent {
    dataSource = signal(new MatTableDataSource<Blocks>([]));
    paginator = viewChild<MatPaginator>(MatPaginator);
    sort = viewChild<MatSort>(MatSort);
    translate = inject(TranslateService);

    menuService = inject(MenuService);
    blockService = inject(BlockService);
    access = computed(() => this.menuService.activeMenu()?.controlAccess);

    mode = signal<'view' | 'edit' | 'add'>('add');
    search = new FormControl("");
    showTable = signal(false)

    lists = signal<Blocks[]>([]);
    list = signal<Blocks>({
        id: 0,
        blockName: "",
        blockRegionalName: "",
        blockLgdCode: "",
        regionName: '',
        unitName: '',
        talukName: ''
    });

    displayedColumns: string[] = [
        'blockName',
        'blockRegionalName',
        'blockLgdCode',
        'regionName',
        'unitName',
        'talukName',
        'actions'
    ];

    ngOnInit(): void {
        this.getLists()
    }

    async getLists() {
        const res = await this.blockService.get();
        this.lists.set(res);
        this.dataSource.set(new MatTableDataSource(this.lists()))
        setTimeout(() => {
            this.dataSource().sort = this.sort()!;
            this.dataSource().paginator = this.paginator()!
        }, 100)
    }

    onAdd() {
        this.list.set({
            id: 0,
            blockName: "",
            blockRegionalName: "",
            blockLgdCode: "",
            talukName: '',
            regionName: '',
            unitName: ''
        });
        this.mode.set("add")
        this.showTable.set(true)
    }

    onView(rowData: Blocks) {
        this.list.set(rowData);
        this.mode.set('view');
        this.showTable.set(true)
    }

    onEdit(rowData: Blocks) {
        this.list.set(rowData);
        this.mode.set('edit');
        this.showTable.set(true)
    }

    onClose() {
        this.showTable.set(false)
        this.getLists();
        this.search.setValue("");
    }

    onRefresh() {
        this.getLists();
        this.search.setValue("");
    }

    async onDelete(rowData: Blocks) {
        await confirmAndDelete(rowData, rowData.blockName, 'Block', this.blockService, () => this.getLists());
    }

    onSearch() {
        const filterValue = this.search.value?.trim().toLowerCase() || '';
        this.dataSource().filter = filterValue;
    }
}
