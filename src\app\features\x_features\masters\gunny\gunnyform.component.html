<mat-toolbar color="primary" class="navbar">
  <nav aria-label="Breadcrumb navigation" style="display: flex; align-items: center; flex-wrap: wrap;">
    <a routerLink="/" class="crumb">{{'Master' | translate}}</a>
    <mat-icon>chevron_right</mat-icon>
    <a class="crumb" (click)="goBack()">{{menuService.activeMenu()?.title}}</a>
    <mat-icon>chevron_right</mat-icon>
    <span aria-current="page" class="nav-active">
      @if(uniqueId() == 0) {
      {{'New' | translate}} {{menuService.activeMenu()?.title}}
      } @else {
        {{formData()?.gunnyTypeDetailId}}
      }
    </span>
    @if(mode() === "view") {
    <button (click)="onEdit()" title="edit" class="btn-edit btn-icon-unstyled">
      <mat-icon class="material-symbols-rounded">edit_square</mat-icon>
    </button>
    }
  </nav>
  <button mat-flat-button color="primary" (click)="goBack()" aria-label="Go back" class="back-btn">
    <mat-icon>arrow_back_ios</mat-icon>
    Back
  </button>
</mat-toolbar>

<div class="card component">
  <form [formGroup]="form">

    <div class="grid-container form">
      <div class="row-3">
        <label for="storageLocationId" class="required-label">{{'GodownLocation' | translate }}</label>
        <mat-form-field appearance="outline">
          <mat-select id="storageLocationId" formControlName="storageLocationId"
            (openedChange)="resetSearch('location')">
            <mat-form-field class="select-search hide-subscript" appearance="outline">
              <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
              <input #search2 autocomplete="off" matInput placeholder="Search" (input)="filter($event,'location')">
            </mat-form-field>
            @for (role of filteredlocation(); track role) {
            <mat-option [value]="role.key">{{role.value}}</mat-option>
            }
          </mat-select>
          <mat-error>
            @if(form.controls.storageLocationId.errors?.['required']) {
            {{'GodownLocation' | translate}} {{'IsRequired' | translate}}
            }
          </mat-error>
        </mat-form-field>
      </div>

      <div class="row-3">
        <label for="gunnyTypeDetailId" class="required-label">{{'GunnyType' | translate }}</label>
        <mat-form-field appearance="outline">
          <mat-select id="gunnyTypeDetailId" formControlName="gunnyTypeDetailId" (openedChange)="resetSearch('type')">
            <mat-form-field class="select-search hide-subscript" appearance="outline">
              <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
              <input #search1 autocomplete="off" matInput placeholder="Search" (input)="filter($event,'type')">
            </mat-form-field>
            @for (role of filteredType(); track role) {
            <mat-option [value]="role.key">{{role.value}}</mat-option>
            }
          </mat-select>
          <mat-error>
            @if(form.controls.gunnyTypeDetailId.errors?.['required']) {
            {{'GunnyType' | translate}} {{'IsRequired' | translate}}
            }
          </mat-error>
        </mat-form-field>
      </div>

      <div class="row-6">
        <label for="gunnyQrCode" class="required-label">{{'QRCode' | translate }}</label>
        <mat-form-field style="max-width: 100%;
    min-height: 70px;">
          <input id="gunnyQrCode" formControlName="gunnyQrCode" matInput maxlength="10" (keydown.enter)="onQrEnter()"
            style="color: #06064a;
    font-size: 30px;" #input1 (keydown.enter)="moveFocus(input1)">
          <mat-error>
            @if(form.controls.gunnyQrCode.errors?.['required']) {
            {{'QRCode' | translate}} {{'IsRequired' | translate}}
            }
          </mat-error>
        </mat-form-field>
      </div>

    </div>

    <div class="actions">

      <button mat-flat-button class="btn btn-theme" type="submit" (click)="onQrEnter()"><mat-icon
          color="primary">save</mat-icon>{{'Save' | translate}}</button>
    </div>
  </form>


  @if(showGrid()){


  <div class="page-header" style="color: darkslategray;">
    <h3>Scanned Gunny Details</h3>
  </div>

  @if(dataSource().filteredData.length > 0) {
  <div class="grid-container summary-grid">
    <div class="row-3" style="margin-bottom: 20px;">
      <div class="summary-item">
        <span class="subject-name">Total</span>
        <div class="badge">{{dataSource().filteredData.length}}</div>
      </div>
    </div>
    @for (test of qrAbstract(); track test) {
    <div class="row-3" style="margin-bottom: 20px;">
      <div class="summary-item">
        <span class="subject-name">{{test.gunny}}</span>
        <div class="badge">{{test.count}}</div>
      </div>
    </div>
    }
  </div>
  }


  <div class="header">
    <div class="filters-wrapper">

      <mat-form-field class="search hide-subscript" appearance="outline">
        <mat-icon class="icon-search material-symbols-outlined" matPrefix>search</mat-icon>
        <input id="search" (keyup)="applyFilter($event)" autocomplete="off" matInput
          placeholder="{{ 'Search' | translate }}">
      </mat-form-field>
      <div class="filters-more">
        <button (click)="onRefresh()" class="btn btn-filter" mat-icon-button>
          <mat-icon class="icon-filter material-symbols-outlined" matPrefix>sync</mat-icon>
        </button>
      </div>
    </div>
  </div>
  <div class="content">
    <div class="table-wrapper">
      <table mat-table [dataSource]="dataSource()" matSort class="mat-elevation-z8">

        <ng-container matColumnDef="gunny">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{'GunnyType' | translate}} </th>
          <td mat-cell *matCellDef="let row"> {{row.gunny}} </td>
        </ng-container>

        <ng-container matColumnDef="gunnyQrCode">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'QRCode' | translate}}</th>
          <td mat-cell *matCellDef="let row"> {{row.gunnyQrCode }} </td>
        </ng-container>

        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef class="action"> {{ 'Actions' | translate }} </th>
          <td mat-cell *matCellDef="let row">
            <div class="table-controls">
              <button title="{{ 'Delete' | translate }}" (click)="onDelete(row)"
                class="btn-icon btn-delete btn-icon-unstyled">
                <mat-icon class="material-symbols-rounded">delete</mat-icon>
              </button>
            </div>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        <tr class="mat-row" *matNoDataRow>
          <td class="mat-cell" [attr.colspan]="displayedColumns.length" style="text-align: center;padding:10px">
            No data found
          </td>
        </tr>
      </table>

    </div>

    <mat-paginator class="paginator" pageSize="10" [pageSizeOptions]="[5, 10, 50,100]"
      aria-label="Select page of users"></mat-paginator>

    <div class="mobile-wrapper">
      <div class="un-cards">
        @for (item of dataSource().data; track item) {
        <div class="un-card">
          <div class="desc">
            <div class="quote-no">{{item.gunnyName}}</div>
            <div style="line-height: 1em;"><span class="tracking-no">{{item.gunnyQrCode}}</span> </div>
          </div>
          <div class="actions">
            <button mat-icon-button class="btn btn-actions" [matMenuTriggerFor]="menu" aria-label="Card actions">
              <mat-icon>more_vert</mat-icon>
            </button>
            <mat-menu #menu="matMenu">

              <button (click)="onDelete(item)" mat-menu-item>
                <mat-icon class="material-symbols-rounded">delete</mat-icon>
                <span>{{ 'Delete' | translate }}</span>
              </button>

            </mat-menu>
          </div>
        </div>
        }
      </div>
    </div>
  </div>
  }

</div>