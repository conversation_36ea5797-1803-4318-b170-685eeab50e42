import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatBadgeModule } from '@angular/material/badge';
import { MatButtonModule } from '@angular/material/button';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatChipsModule } from '@angular/material/chips';
import { MatNativeDateModule, MatOptionModule, MatRippleModule } from '@angular/material/core';
import { MatDialogModule } from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatGridListModule } from '@angular/material/grid-list';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatListModule } from '@angular/material/list';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatSortModule } from '@angular/material/sort';
import { MatTableModule } from '@angular/material/table';
import { MatTabsModule } from '@angular/material/tabs';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { formatDate } from '@angular/common';

import { LeaveManagementService } from '../../../../services/m_apis/service/leave-management.service';

@Component({
  selector: 'app-add-holiday',
  imports: [CommonModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatTableModule,
    MatSelectModule,
    MatOptionModule,
    MatDatepickerModule,
    MatNativeDateModule,
    FormsModule,
    ReactiveFormsModule,
    MatProgressSpinnerModule,
    MatProgressBarModule,
    MatIconModule],
  templateUrl: './add-holiday.component.html',
  styleUrl: './add-holiday.component.scss'
})
export class AddHolidayComponent implements OnInit {
  holidayForm: FormGroup;
  holidayTypes = ['INTERNATIONAL','NATIONAL', 'STATE', 'FESTIVAL', 'RESTRICTED', 'OPTIONAL'];
  constructor(
    private fb: FormBuilder,
    private router: Router,
    private holidayService: LeaveManagementService
  ) {
    this.holidayForm = this.fb.group({
      name: ['', Validators.required],
      date: ['', Validators.required],
      day: [{ value: '', disabled: true }],
      description: [''],
      holidayType: ['', Validators.required],
      region: [''],
      district: [''],
      year: ['', Validators.required],
    });
  }

  ngOnInit(): void {

    this.holidayForm.get('date')?.valueChanges.subscribe(date => {
      if (date) {
        const day = formatDate(date, 'EEEE', 'en-US');
        this.holidayForm.get('day')?.setValue(day);
        this.holidayForm.get('year')?.setValue(date.getFullYear());

        console.log('Selected Date:', date);
        console.log('Formatted Date:', formatDate(date, 'yyyy-MM-dd', 'en-US'));
      }
    });
  }

  addHoliday(): void {
    console.log(this.holidayForm.value);
    if (this.holidayForm.valid) {
      const selectedDate = this.holidayForm.get('date')?.value;
      const utcDate = new Date(selectedDate.getTime() - selectedDate.getTimezoneOffset() * 60000).toISOString().split('T')[0];      
      const holidayData = {
        holidayName: this.holidayForm.get('name')?.value,
        holidayDate: utcDate,
        holidayDay: this.holidayForm.get('day')?.value,
        description: this.holidayForm.get('description')?.value,
        holidayType: this.holidayForm.get('holidayType')?.value,
        region: this.holidayForm.get('region')?.value,
        district: this.holidayForm.get('district')?.value,
        year: this.holidayForm.get('year')?.value,
      };
      console.log(holidayData);
      this.holidayService.saveHoliday(holidayData).subscribe({
        next: (response) => {
          console.log('Holiday saved successfully', response);
          this.holidayForm.reset();
        },
        error: (error) => console.error('Error saving holiday', error)
      });
    }
  }


  cancel(): void {
    this.router.navigate(['/holidays']);
  }
}
