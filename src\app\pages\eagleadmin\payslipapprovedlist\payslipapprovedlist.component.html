<div class="main-content">
  <div class="container-fluid">
    <div class="row">
      <div class="col-12">
        <mat-card class="table-card">
          <mat-card-header>
            <mat-card-title>
              <h2>Approved Payslip List</h2>
            </mat-card-title>
            <div class="header-actions">
              <button mat-raised-button color="primary" (click)="refreshData()" matTooltip="Refresh Data">
                <mat-icon>refresh</mat-icon>
                Refresh
              </button>
            </div>
          </mat-card-header>

          <mat-card-content>
            <!-- Loading Spinner -->
            <div *ngIf="dataSource.length === 0" class="loading-container">
              <mat-progress-spinner mode="indeterminate" diameter="50"></mat-progress-spinner>
              <p>Loading approved payslips...</p>
            </div>

            <!-- Data Table -->
            <div *ngIf="dataSource.length > 0" class="table-container">
              <table mat-table [dataSource]="dataSource" class="mat-elevation-2 full-width-table" matSort>
                
                <!-- Serial Number Column -->
                <ng-container matColumnDef="sno">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>S.No</th>
                  <td mat-cell *matCellDef="let element; let i = index">{{ i + 1 }}</td>
                </ng-container>

                <!-- Employee ID Column -->
                <ng-container matColumnDef="empId">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>Employee ID</th>
                  <td mat-cell *matCellDef="let element">{{ element.empId }}</td>
                </ng-container>
                 <ng-container matColumnDef="employeeName">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>Employee Name</th>
                  <td mat-cell *matCellDef="let element">{{ element.employeeName }}</td>
                </ng-container>

                <!-- Pay Month Column -->
                <ng-container matColumnDef="payMonth">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>Pay Month</th>
                  <td mat-cell *matCellDef="let element">{{ element.payMonth | date:'MMM yyyy' }}</td>
                </ng-container>

                <!-- PF Number Column -->
                <ng-container matColumnDef="pfNumber">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>PF Number</th>
                  <td mat-cell *matCellDef="let element">{{ element.pfNumber || 'N/A' }}</td>
                </ng-container>

                <!-- Basic Pay Column -->
                <ng-container matColumnDef="basicPay">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>Basic Pay</th>
                  <td mat-cell *matCellDef="let element" class="currency">₹{{ element.basicPay | number:'1.2-2' }}</td>
                </ng-container>

                <!-- House Rent Allowance Column -->
                <ng-container matColumnDef="houseRentAllowance">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>HRA</th>
                  <td mat-cell *matCellDef="let element" class="currency">₹{{ element.houseRentAllowance | number:'1.2-2' }}</td>
                </ng-container>

                <!-- Dearness Allowance Column -->
                <ng-container matColumnDef="dearnessAllowance">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>DA</th>
                  <td mat-cell *matCellDef="let element" class="currency">₹{{ element.dearnessAllowance | number:'1.2-2' }}</td>
                </ng-container>

                <!-- Cash Allowance Column -->
                <ng-container matColumnDef="cashAllowance">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>Cash Allowance</th>
                  <td mat-cell *matCellDef="let element" class="currency">₹{{ element.cashAllowance | number:'1.2-2' }}</td>
                </ng-container>

                <!-- City Compensatory Allowance Column -->
                <ng-container matColumnDef="cityCompensatoryAllowance">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>CCA</th>
                  <td mat-cell *matCellDef="let element" class="currency">₹{{ element.cityCompensatoryAllowance | number:'1.2-2' }}</td>
                </ng-container>

                <!-- Conveyance Allowance Column -->
                <ng-container matColumnDef="conveyanceAllowance">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>Conveyance</th>
                  <td mat-cell *matCellDef="let element" class="currency">₹{{ element.conveyanceAllowance | number:'1.2-2' }}</td>
                </ng-container>

                <!-- Medical Allowance Column -->
                <ng-container matColumnDef="medicalAllowance">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>Medical</th>
                  <td mat-cell *matCellDef="let element" class="currency">₹{{ element.medicalAllowance | number:'1.2-2' }}</td>
                </ng-container>

                <!-- Special Pay Column -->
                <ng-container matColumnDef="specialPay">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>Special Pay</th>
                  <td mat-cell *matCellDef="let element" class="currency">₹{{ element.specialPay | number:'1.2-2' }}</td>
                </ng-container>

                <!-- Wash Allowance Column -->
                <ng-container matColumnDef="washAllowance">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>Wash Allowance</th>
                  <td mat-cell *matCellDef="let element" class="currency">₹{{ element.washAllowance | number:'1.2-2' }}</td>
                </ng-container>

                <!-- Total Earnings Column -->
                <ng-container matColumnDef="totalEarnings">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>Total Earnings</th>
                  <td mat-cell *matCellDef="let element" class="currency total-amount">₹{{ element.totalEarnings | number:'1.2-2' }}</td>
                </ng-container>

                <!-- EPF Column -->
                <ng-container matColumnDef="epf">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>EPF</th>
                  <td mat-cell *matCellDef="let element" class="currency deduction">₹{{ element.epf | number:'1.2-2' }}</td>
                </ng-container>

                <!-- FBF Column -->
                <ng-container matColumnDef="fbf">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>FBF</th>
                  <td mat-cell *matCellDef="let element" class="currency deduction">₹{{ element.fbf | number:'1.2-2' }}</td>
                </ng-container>

                <!-- HIS Column -->
                <ng-container matColumnDef="his">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>HIS</th>
                  <td mat-cell *matCellDef="let element" class="currency deduction">₹{{ element.his | number:'1.2-2' }}</td>
                </ng-container>

                <!-- SPF Column -->
                <ng-container matColumnDef="spf">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>SPF</th>
                  <td mat-cell *matCellDef="let element" class="currency deduction">₹{{ element.spf | number:'1.2-2' }}</td>
                </ng-container>

                <!-- Loan Deduction Column -->
                <ng-container matColumnDef="loanDeduction">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>Loan Deduction</th>
                  <td mat-cell *matCellDef="let element" class="currency deduction">
                    {{ element.loanDeduction ? ('₹' + (element.loanDeduction | number:'1.2-2')) : 'N/A' }}
                  </td>
                </ng-container>

                <!-- Total Deductions Column -->
                <ng-container matColumnDef="totalDeductions">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>Total Deductions</th>
                  <td mat-cell *matCellDef="let element" class="currency total-deduction">₹{{ element.totalDeductions | number:'1.2-2' }}</td>
                </ng-container>

                <!-- Net Salary Column -->
                <ng-container matColumnDef="netSalary">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>Net Salary</th>
                  <td mat-cell *matCellDef="let element" class="currency net-salary">₹{{ element.netSalary | number:'1.2-2' }}</td>
                </ng-container>

                <!-- Actions Column -->
                <ng-container matColumnDef="actions">
                  <th mat-header-cell *matHeaderCellDef>Actions</th>
                  <td mat-cell *matCellDef="let element">
                    <div class="action-buttons">
                      <button mat-icon-button 
                              color="primary" 
                             (click)="goPageChange(element.empId)"
                              matTooltip="View Payslip">
                        <mat-icon>visibility</mat-icon>
                      </button>
                      <!-- <button mat-icon-button 
                              color="accent" 
                              (click)="goPageChange(element.empId)"
                              matTooltip="View Payslip">
                        <mat-icon>description</mat-icon>
                      </button> -->
                    </div>
                  </td>
                </ng-container>

                <!-- Table Header and Rows -->
                <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                <tr mat-row *matRowDef="let row; columns: displayedColumns;" 
                    class="table-row" 
                    [ngClass]="{'highlight-row': row.isHighlighted}"></tr>
              </table>

              <!-- Paginator -->
              <mat-paginator 
                [pageSizeOptions]="[5, 10, 20, 50]" 
                [pageSize]="pageSize"
                [length]="count"
                showFirstLastButtons>
              </mat-paginator>
            </div>

            <!-- No Data Message -->
            <div *ngIf="dataSource.length === 0" class="no-data-container">
              <mat-icon class="no-data-icon">inbox</mat-icon>
              <h3>No Approved Payslips Found</h3>
              <p>There are currently no approved payslips to display.</p>
              <button mat-raised-button color="primary" (click)="refreshData()">
                <mat-icon>refresh</mat-icon>
                Refresh
              </button>
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  </div>
</div>