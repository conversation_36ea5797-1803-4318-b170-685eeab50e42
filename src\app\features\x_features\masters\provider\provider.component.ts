import { Component, inject, input, output, resource, signal } from '@angular/core';
import { Form<PERSON>uilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButton } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormField, MatInput, MatError } from '@angular/material/input';
import { MatOption, MatSelect } from '@angular/material/select';
import { Router } from '@angular/router';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { DateInputDirective } from '../../../../directives/date-input.directive';
import { MatNativeDateModule } from '@angular/material/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { LookupService } from '../../../../services/x_apis/lookup.service';
import { AlertService } from '../../../../services/alert.service';
import { InputformatService } from '../../../../services/x_apis/inputformat.service';
import { ProviderService } from '../../../../services/x_apis/masters/provider.service';
import { Provider } from '../../../../models/x_models/masters/provider';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { MatToolbarModule } from '@angular/material/toolbar';

@Component({
  selector: 'app-provider',
  imports: [FormsModule,
    MatFormField,
    MatInput,
    ReactiveFormsModule,
    MatError,
    MatOption,
    MatSelect,
    MatButton,
    MatIconModule,
    MatDatepickerModule,
    DateInputDirective,
    MatNativeDateModule,
    TranslateModule,
    MatToolbarModule,
    MatFormFieldModule],
  templateUrl: './provider.component.html',
  styleUrl: './provider.component.scss'
})
export class ProviderComponent {
  menuService = inject(MenuService);
  lookupService = inject(LookupService);
  providerService = inject(ProviderService);
  alertService = inject(AlertService);
  inputFormatService = inject(InputformatService);

  translate = inject(TranslateService);
  regions = resource({ loader: () => this.lookupService.getRegion() }).value;
  providers = resource({ loader: () => this.lookupService.getProvider() }).value;

  mode = input.required<string>();
  closed = output<boolean>();
  fb = inject(FormBuilder);
  editable = signal<boolean>(true);
  router = inject(Router);

  uniqueId = input.required<any>();
  provider = signal<Provider | null>(null);

  form = this.fb.group({
    id: this.fb.control<number>(0),
    simNo1: this.fb.control<string>('', Validators.required),
    simNo2: this.fb.control<string>(''),
    mobileNo1: this.fb.control<string>('', [Validators.required, Validators.pattern("^[6-9][0-9]{9}$")]),
    mobileNo2: this.fb.control<string>('', Validators.pattern("^[6-9][0-9]{9}$")),
    providerType1: this.fb.control<number | null>(null, Validators.required),
    providerType2: this.fb.control<number | null>(null),
    providerPlan1: this.fb.control<string>('', Validators.required),
    providerPlan2: this.fb.control<string>('')
  });

  ngOnInit() {
    this.translate.onLangChange.subscribe(() => {
      this.fetchRegions();
    });
    if (this.mode() === "edit" || this.mode() === "view") {
      this.getFormData()
    }
  }

  async fetchRegions() {
    this.regions.set(await this.lookupService.getRegion());
  }

  async getFormData() {
    const res = await this.providerService.getById(this.uniqueId());
    this.provider.set(res)
    this.form.patchValue({ ...res });
    if (this.mode() === "view") {
      this.editable.set(false)
      this.form.disable()
    }
  }

  onEdit() {
    this.form.enable();
    this.editable.set(true)
  }

  async onSubmit() {
    debugger
    if (this.form.valid) {
      const formValue = { ...this.form.value };
      const res = await this.providerService.create(formValue as Provider)
      if (res.isSuccess) {
        this.closed.emit(true)
        if (formValue.id == 0) {
          this.alertService.success(`Provider Created successfully.`)
        }
        else {
          this.alertService.success(`Provider Updated successfully.`)
        }
      }
    } else {
      this.form.markAllAsTouched();
    }
  }

  goBack() {
    this.closed.emit(true)
  }

  formReset() {
    let indexId = this.form.value.id;
    this.form.reset();
    this.form.patchValue({
      id: indexId
    });
  }

  onInput(event: any, regexPattern: any): void {
    this.inputFormatService.isNumericOrHyphenOrSpace(event, regexPattern);
  }

}
