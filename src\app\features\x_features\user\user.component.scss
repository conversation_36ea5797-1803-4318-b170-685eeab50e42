.component {
  max-width: 1150px;
  margin: 0 auto;
}

.page-header {
    display: flex;
    gap: 10px;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0px;
    flex-wrap: wrap;

    h1 {
      margin-right: auto;
    }

    nav {
        min-width: 100%;
        margin-bottom: -25px;
    }

    .btn-back {
        margin-top: -10px;
    }
}

.btn-edit {
    color: rgba(130, 143, 162, 0.895);
    mat-icon {
        height: 16px;
        font-size: 16px;
        font-variation-settings: "FILL" 0.2, "wght" 600, "GRAD" 0, "opsz" 24;
    }
}

.form {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(270px, 1fr));
    gap: 0 25px;
    margin-block: 10px 20px;

    .field {
      mat-form-field {
        width: 100%;
        max-width: 100%;
      }
    }

  }
  .actions {
      text-align: right;
      display: flex;
      column-gap: 20px;
      justify-content: flex-end;
  }