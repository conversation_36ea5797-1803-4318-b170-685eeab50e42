export interface StorageLocation {
    id: number,
    storageLocationName: string,
    storageLocationRegionalName: string,
    storageType: number,
    regionId: number | null,
    unitId?: number,
    talukId?: number,
    blockId?: number,
    villageId?: number,
    agencyId?: number,
    capacity: number,
    carpetArea: number,
}

export interface StorageLocations {
    id: number,
    storageLocationName: string
    storageLocationRegionalName: string,
    storageTypeName: string,
    regionName: string,
    agencyName: string,
    capacity: number,
    carpetArea: number,
    latitude: number,
    longitude: number
}

export type StorageLocationResponse = StorageLocations[];