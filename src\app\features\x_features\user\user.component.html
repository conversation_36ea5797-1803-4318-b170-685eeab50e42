<div class="card">
  <div class="component">
    <div class="page-header">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a (click)="goToUsers()">Users</a></li>
                @if(userId() == 0) {
                <li aria-current="page" class="breadcrumb-item active">New User</li>
                } @else {
                <li aria-current="page" class="breadcrumb-item active"> {{user()?.firstName}} {{user()?.lastName}}</li>
                }
            </ol>
        </nav>

        @if(userId() == 0) {
        <h1>New User</h1>
        } @else {
        <h1>
          {{user()?.firstName}} {{user()?.lastName}}

            @if(mode() === "view") {
            <button (click)="onEdit()" title="edit" class="btn-edit btn-icon-unstyled">
                <mat-icon class="material-symbols-rounded">edit_square</mat-icon>
            </button>
            }
        </h1>
        }
    </div>

    <form [formGroup]="form" (ngSubmit)="onSubmit()">
      <div class="form">

        <div class="field">
          <label for="firstName">First name</label>
          <mat-form-field>
            <input id="firstName" formControlName="firstName" matInput placeholder="First name" maxlength="250">
            <mat-error>
              @if(form.controls.firstName.errors?.['required']) {
                First name required
              }
            </mat-error>
          </mat-form-field>
        </div>
  
        <div class="field">
          <label for="lastname">Last name</label>
          <mat-form-field appearance="outline">
              <input autocomplete="off" id="lastname" formControlName="lastName" placeholder="Last name" maxlength="50" matInput>
          </mat-form-field>
        </div>
  
        <div class="field">
          <label for="email">Email</label>
          <mat-form-field>
              <input autocomplete="off" id="email" formControlName="email" placeholder="Email" maxlength="50" matInput>
              <mat-error>
                @if (form.controls.email?.errors?.['email']) {
                  Please enter a valid email address
                } @else if (form.controls.email?.errors?.['required']) {
                  Email is required
                }
              </mat-error>
          </mat-form-field>
        </div>
  
        <div class="field">
          <label for="username">Username</label>
          <mat-form-field>
              <input autocomplete="off" id="username" formControlName="userName" placeholder="Username" maxlength="50" matInput>
          </mat-form-field>
        </div>
  
        <div class="field">
          <label for="mobile">Mobile number</label>
          <mat-form-field appearance="outline">
              <input autocomplete="off" id="mobile" formControlName="mobileNo" placeholder="Mobil number" appPhoneNumber maxlength="10" matInput>
          </mat-form-field>
        </div>
  
        <div class="field">
          <label for="role">Role</label>
          <mat-form-field appearance="outline">
              <mat-select id="role" formControlName="roleId">
                <!-- <mat-option value="" disabled>Select a role</mat-option> -->
                @for (role of roles(); track role) {
                  <mat-option [value]="role.key">{{role.value}}</mat-option>
                }
              </mat-select>
          </mat-form-field>
        </div>
      </div>

      <div class="actions">
        <button mat-flat-button (click)="goToUsers()" class="btn btn-secondary">Back</button>
        <button [disabled]="editable() === false" mat-flat-button class="btn btn-theme" type="submit">{{userId() == 0 ? 'Create' :
            'Update'}}</button>
        </div>
    </form>
  </div>
</div>
