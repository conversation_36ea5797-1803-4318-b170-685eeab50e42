.component {
    padding: 20px 20px 20px 20px;
    // border-radius: 10px;
    // margin-top: 12px;
    // min-height: calc(100% - 145px);
    // background-color: #FFFFFF;

    // max-width: max-content; // for grid full view

    @media (max-width: 700px) {
        // background-color: #F6F6F6;
        // padding: 20px;
        max-width: 100%;
        min-height: 100%;
    }

    .page-header {
        display: flex;
        flex-direction: row;
        gap: 10px;
        justify-content: space-between;
        align-items: center;
        padding: 10px 10px;
        flex-wrap: wrap;

        h1 {
            display: flex;
            margin-right: auto;
            color: var(--headertheme-20);
        }

        @media (max-width: 700px) {
            h1 {
                order: 0;
            }
        }
    }

    .header {
        padding-inline: 10px;

        @media (max-width: 700px) {
            padding-left: 0;
        }

        .filters-wrapper {
            // max-width: 615px;
            display: flex;
            align-items: center;



            .search {
                max-width: calc(100% - 26px);
                width: 100%;
                //margin-top: 20px;
                --form-field-bg: #fff;

                .icon-search {
                    width: 15px;
                    height: 22px;
                    padding-inline: 17px 13px;
                    color: var(--theme-80);
                }
            }

            .btn-info {
                margin-left: 10px;
            }

            .filters-more {
                position: relative;
                display: flex;

                .btn-filter {
                    background-color: #fff;
                    border: 1.5px solid var(--grey-20);
                    border-radius: 10px;
                    margin-left: 15px;
                    width: fit-content;
                    font-size: 12px;
                    display: flex;
                    gap: 30px;
                    --btn-icon-radius: 8px;

                    span {
                        z-index: 1;

                        @media (max-width: 700px) {
                            display: none;
                        }
                    }

                    .icon-filter {
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        //padding-inline: 17px 13px;
                        color: var(--theme-80);
                    }
                }
            }

        }
    }

    .content {
        width: max-content;
        max-width: 100%;
        margin-top: 10px;
        padding-inline: 10px;

        @media (max-width: 700px) {
            width: 100%;
            margin-top: 20px;
        }

        // .table-wrapper,
        .mobile-wrapper {
            background-color: #FFFFFF;
            border-radius: 8px;
        }

        .table-wrapper {
            background-color: #FFFFFF;
            @media (max-width: 700px) {
                display: none;
            }
        }

        .mobile-wrapper {
            display: none;
            --mdc-checkbox-state-layer-size: 20px;

            @media (max-width: 700px) {
                display: block
            }

            .un-card {
                display: grid;
                grid-template-columns: 1fr 36px;
                padding-block: 8px 15px;
                border-bottom: 1px solid #e9e9e9;

                .select {
                    mat-checkbox {
                        margin-top: 7px;
                    }
                }

                .desc {
                    .quote-no {
                        font-size: 12px;
                        width: 100%;

                        .cost {
                            font-weight: 700;
                        }
                    }

                    .tracking-no {
                        font-size: 10px;
                        line-height: 1em;
                        color: var(--grey-40);
                    }
                }

                .actions {
                    .btn {
                        margin-top: 2px;
                    }
                }
            }
        }

    }
}

table {


    .pill {
        width: 99px;
        height: 23px;
        padding: 0px 31px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 25px;
        color: #000;

        &.quote {
            background-color: var(--grey-10);
        }

        &.invoiced {
            background-color: var(--theme-40);
        }
    }

    .actions {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 3px;

        .btn-icon {
            // color: var(--theme-80)
        }

        mat-icon {
            font-size: 16px;
        }
    }

    // .actions {
    //   padding-inline: 0;

    //   .btn {
    //     --mdc-icon-button-state-layer-size: 24px;
    //     --mat-icon-button-state-layer-color: var(--theme-40);
    //     display: flex;
    //     justify-content: center;
    //     padding-inline: 0;

    //     &:has(.file-lines) {
    //       margin-right: 3px;
    //     }

    //     mat-icon {
    //       width: 13.5px;
    //     }

    //     mat-icon.file-lines {
    //       width: 15px;
    //     }
    //   }

    //   .btn-theme {
    //     color: var(--theme-80);

    //     &[disabled] {
    //       color: #E7E8E9;
    //     }
    //   }
    // }
    th {
        text-transform: uppercase;
    }
}


:host ::ng-deep .btn-close svg {
    width: 16px;
    height: 16px;
}

:host ::ng-deep .icon-filter svg {
    width: 15px;
    height: 9px;
}



[statusType='1'] {
    color: #55a609;
}

[statusType='2'] {
    color: #e70202;
}

.btn-icon {
    // color: var(--sys-primary);
    color: rgb(106, 117, 135);
    display: flex;
    align-items: center;
    padding-left: 5px;
    padding-right: 5px;
}

.btn-delete {
    // color: rgb(84 95 113);
    // padding: 0;
    color: var(--red-80);

    mat-icon {
        font-variation-settings: 'FILL' 1, 'wght' 700, 'GRAD' 200, 'opsz' 24;
    }
}

.btn-activate {
    padding: 0;
    color: #6DB02D;

    mat-icon {
        font-variation-settings: 'FILL' 1, 'wght' 700, 'GRAD' 200, 'opsz' 24;
    }
}

.btn-edit {
    color: rgba(130, 143, 162, 0.895);

    mat-icon {
        height: 16px;
        font-size: 16px;
        font-variation-settings: "FILL" 0.2, "wght" 600, "GRAD" 0, "opsz" 24;
    }
}

.form {

    display: grid;
    grid-template-columns: repeat(4, 1fr); /* 3 equal columns */
    gap: 1rem;

    // display: grid;
    // grid-template-columns: repeat(auto-fit, minmax(270px, 1fr));
    // gap: 0 25px;
    margin-block: 10px 20px;

    .field {
        mat-form-field {
            width: 100%;
            max-width: 100%;
        }
    }

}


.field {
    display: flex;
    flex-direction: column;
  }
  
  /* Tablet: 2 columns */
  @media (max-width: 992px) {
    .form {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  
  /* Mobile: 1 column */
  @media (max-width: 600px) {
    .form {
      grid-template-columns: 1fr;
    }
  }

.actions {
    text-align: right;
    display: flex;
    column-gap: 20px;
    justify-content: flex-end;
}

.component1 {
    // max-width: 1150px;  
    // margin: 0 auto;
}

.code {
color: rgb(161, 24, 6);
}

.page-header1 {
    display: flex;
    gap: 10px;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0px;
    flex-wrap: wrap;

    h1 {
        margin-right: auto;
    }

    nav {
        min-width: 100%;
        margin-bottom: -25px;
    }

    .btn-back {
        margin-top: -10px;
    }
}


.component.card,
.content,
.table-wrapper {
    width: 100% !important;
    overflow-x: auto !important;    
}

.table-wrapper{
    max-height: 550px;
}

.table-controls {
    display: flex;
    justify-content: center;
    align-items: center;
}

.action {
    text-align: center !important;
   
}

.mat-icon {
    font-size: 20px;
  }


  .required-label::after {
    content: ' *';
    color: red;
    font-weight: bold;
  }

  .container{
    max-height: 520px;
    overflow-x: auto;
  }
  .mat-mdc-table thead, .mat-table-sticky {
    position: sticky;
}

.mat-mdc-table thead{ 
  background:var(--theme-90) !important;
}



.mat-mdc-header-row {
    color: var(--theme-05) !important;

}

.mat-sort-header .mat-sort-header-sorted .mat-sort-header-arrow {
    color: var(--theme-05) !important;
}
.mat-sort-header.cdk-keyboard-focused .mat-sort-header-arrow, .mat-sort-header.cdk-program-focused .mat-sort-header-arrow, .mat-sort-header:hover .mat-sort-header-arrow {
    opacity: 1;
    color: var(--theme-05) !important;
}
.mat-mdc-header-cell {
    border-bottom-color: var(--headertheme-20) !important;
}

.mat-mdc-cell {
    border-bottom-color: var(--theme-10) !important;
}


@media (max-width: 575px) {
    .paginator {
      display: none !important;
    }
  }
  

// For Form & summary card

form {
    padding-left: 10px;
    padding-right: 10px;
    margin-bottom: 20px;
  }
  
  .grid-container {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    gap: 1rem;
    margin-bottom: 20px
  }
  
  .row-12 {
    grid-column: span 12;
  }
  
  .row-11 {
    grid-column: span 11;
  }
  
  .row-10 {
    grid-column: span 10;
  }
  
  .row-9 {
    grid-column: span 9;
  }
  
  .row-8 {
    grid-column: span 8;
  }
  
  .row-7 {
    grid-column: span 7;
  }
  
  .row-6 {
    grid-column: span 6;
  }
  
  .row-5 {
    grid-column: span 5;
  }
  
  .row-4 {
    grid-column: span 4;
  }
  
  .row-3 {
    grid-column: span 3;
  }
  
  .row-2 {
    grid-column: span 2;
  }
  
  .row-1 {
    grid-column: span 1;
  }
  
  
  @media (max-width: 768px) {
    .grid-container {
      grid-template-columns: repeat(12, 1fr);
    }
  
    .row-12 {
      grid-column: span 12;
      text-align: left;
    }
  
    .row-11 {
      grid-column: span 12;
      text-align: left;
    }
  
    .row-10 {
      grid-column: span 12;
      text-align: left;
    }
  
    .row-9 {
      grid-column: span 12;
      text-align: left;
    }
  
    .row-8 {
      grid-column: span 12;
      text-align: left;
    }
  
    .row-7 {
      grid-column: span 12;
      text-align: left;
    }
  
    .row-6 {
      grid-column: span 12;
      text-align: left;
    }
  
    .row-5 {
      grid-column: span 12;
      text-align: left;
    }
  
    .row-4 {
      grid-column: span 12;
      text-align: left;
    }
  
    .row-3 {
      grid-column: span 12;
      text-align: left;
    }
  
    .row-2 {
      grid-column: span 12;
      text-align: left;
    }
  
    .row-1 {
      grid-column: span 12;
      text-align: left;
    }
  
  
    .actions {
      justify-content: flex-start;
    }
  
    .card-container {
      grid-template-columns: 1fr;
    }
  }
  
  mat-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 30px;
    padding-right: 20px;
    box-shadow: 0 0 20px rgba(6, 10, 135, 0.2);
  }
  
  .nav-aln {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }
  
  button {
    display: inline-flex;
    align-items: center;
    padding: 8px 16px;
  }
  
  mat-icon {
    margin-right: 10px;
    margin-left: 10px;
  }
  
  .navbar {
    position: sticky;
    top: 0;
    background-color: #fff;
    z-index: 1000;
  }
  
  .crumb {
    font-size: 15px;
  }
  
  .nav-active {
    color: rgb(4, 4, 143);
    font-size: 18px;
  }
  
  @media (max-width: 600px) {
    .navbar {
      flex-direction: column;
      align-items: flex-start;
    }
  
    nav {
      width: 100%;
      margin-bottom: 10px;
    }
  
    button {
      width: 100%;
      justify-content: flex-start;
    }
  }
  
  .summary-grid {
    margin-right: 10px;
    margin-left: 10px;
  }
  
  .summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-radius: 8px;
    background-color: #d9f1ff;
    box-shadow: 5px 2px 4px 0px rgb(33 57 225 / 10%);
  }
  
  .subject-name {
    font-size: 1rem;
    color: #333;
  }
  
  .badge {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #2196f3;
    color: white;
    font-weight: bold;
    font-size: 1rem;
  }

 