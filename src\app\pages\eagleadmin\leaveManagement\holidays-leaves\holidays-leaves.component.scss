.leave-calendar-container {
    padding: 24px;
    background-color: #f5f5f5;
    min-height: 100vh;
  
    .header-card {
      background: linear-gradient(135deg, #203664 0%, darken(#203664, 10%) 100%);
      color: white;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      margin-bottom: 24px;
      transition: transform 0.3s ease;
    
      &:hover {
        transform: translateY(-4px);
      }
    
      .form-header {
        padding: 16px;
        display: flex;
        align-items: center;
    
        .header-content {
          display: flex;
          align-items: center;
          width: 100%;
        }
    
        .logo-section {
          margin-right: 16px;
    
          .tncsc-logo {
            font-size: 40px;
            width: 40px;
            height: 40px;
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
    
        .title-section {
          .form-subtitle {
            font-size: 24px;
            font-weight: 500;
            margin: 0;
          }
    
          .subtitle {
            font-size: 14px;
            opacity: 0.8;
            margin: 4px 0 0;
          }
        }
      }
    }
  
    .month-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 24px;
    }
  
    .month-card {
      padding: 16px;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      background: white;
      border-left: 4px solid #11074e;
      transition: transform 0.2s ease, box-shadow 0.2s ease;
  
      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
      }
  
      .month-content {
        display: flex;
        flex-direction: column;
  
        .month-header {
          display: flex;
          flex-direction: column;
          align-items: center;
          margin-bottom: 16px;
  
          .month-name {
            font-size: 1.5rem;
            font-weight: 500;
            color: #333;
          }
  
          .leave-count {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 8px;
  
            mat-icon {
              font-size: 1.25rem;
              width: 1.25rem;
              height: 1.25rem;
            }
  
            .count {
              font-size: 1.5rem;
              font-weight: 600;
              color: #333;
            }
  
            .label {
              font-size: 0.875rem;
              color: #666;
            }
          }
        }
  
        .holiday-list {
          text-align: left;
  
          .holiday-item {
            display: grid;
            grid-template-columns: 40px 40px 1fr;
            gap: 12px;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
            font-size: 0.875rem;
            color: #333;
  
            &:last-child {
              border-bottom: none;
            }
  
            .holiday-date {
              font-weight: 500;
            }
  
            .holiday-day {
              color: #666;
            }
  
  
  
            .holiday-name {
              font-weight: 400;
            }
          }
  
          .no-holidays {
            font-size: 0.875rem;
            color: #666;
            padding: 16px 0;
          }
        }
      }
    }
  
    @media (max-width: 768px) {
      padding: 16px;
  
      .header .page-title {
        font-size: 2rem;
  
        mat-icon {
          font-size: 2rem;
          width: 2rem;
          height: 2rem;
        }
      }
  
      .month-grid {
        grid-template-columns: 1fr;
        gap: 16px;
      }
  
      .month-card {
        padding: 12px;
  
        .month-content {
          .month-header {
            .month-name {
              font-size: 1.3rem;
            }
  
            .leave-count {
              .count {
                font-size: 1.25rem;
              }
            }
          }
  
          .holiday-list {
            .holiday-item {
              font-size: 0.75rem;
              padding: 6px 0;
              grid-template-columns: 30px 30px 1fr;
              gap: 8px;
            }
          }
        }
      }
    }

    .years{
        display: flex;
        justify-content: end;
        align-items: center;
    }
    
  }