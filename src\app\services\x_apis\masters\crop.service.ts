import { inject, Injectable } from '@angular/core';
import { DataService } from '../../data.service';
import { Crop, CropResponse } from '../../../models/x_models/masters/crop';
import { Response } from '../../../models/x_models/api-response';

@Injectable({
    providedIn: 'root'
})
export class CropService {
    dataService = inject(DataService)

    create(data: Crop) {
        return this.dataService.post<Response>("/crop", data)
    }

    get() {
        return this.dataService.get<CropResponse>("/crop")
    }

    getById(id: number) {
        return this.dataService.get<Crop>(`/crop/${id}`)
    }

    delete(id: number) {
        return this.dataService.delete<Response>(`/crop/${id}`)
    }
}
