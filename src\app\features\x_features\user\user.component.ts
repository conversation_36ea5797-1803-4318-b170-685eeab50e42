import { Component, inject, input, OnInit, output, signal } from '@angular/core';
import {Mat<PERSON><PERSON>on, MatButtonModule} from '@angular/material/button';
import {MatError, MatFormField, MatFormFieldModule} from '@angular/material/form-field';
import {MatInput, MatInputModule} from '@angular/material/input';
import { Router, RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormControl,
  FormGroupDirective,
  FormsModule,
  NgForm,
  ReactiveFormsModule,
  Validators
} from '@angular/forms';
import { ErrorStateMatcher } from '@angular/material/core';
import {MatOption, MatSelect, MatSelectModule, MatSelectTrigger} from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { LookupService } from '../../../services/x_apis/lookup.service';
import { LookUpResponse } from '../../../models/x_models/lookup';
import { UserService } from '../../../services/x_apis/user.service';
import { UserByIdResponse } from '../../../models/x_models/user';

@Component({
  selector: 'app-user',
  imports: [FormsModule,
    MatFormField,
    MatInput,
    ReactiveFormsModule,
    MatError,
    MatOption,
    MatSelect,
    MatButton, MatIconModule],
  templateUrl: './user.component.html',
  styleUrl: './user.component.scss'
})
export class UserComponent implements OnInit {
  mode = input.required<string>()
  userId = input.required<any>()
  lookupService = inject(LookupService)
  userService = inject(UserService)

  closed = output<boolean>()

  router = inject(Router)
  fb = inject(FormBuilder)

  editable = signal<boolean>(true)
  roles = signal<LookUpResponse[]>([])
  user = signal<UserByIdResponse | null>(null)


  form = this.fb.group({
    firstName: ['', Validators.required],
    lastName: ['', Validators.required],
    email: ['', [Validators.required, Validators.email]],
    userName: ['', Validators.required],
    mobileNo: ['', Validators.required],
    roleId: [null, Validators.required]
  })

  // matcher = new MyErrorStateMatcher();

  ngOnInit(): void {
    this.getRole()
    if (this.mode() === "edit" || this.mode() === "view") {
      this.getUser()
    }
  }

  async getUser() {
    const res = await this.userService.getById(this.userId());
    this.user.set(res)
    this.form.patchValue({
      firstName: res.firstName,
      lastName: res.lastName,
      email: res.email,
      userName: res.userName,
      mobileNo: res.mobileNo,
      roleId: res.roleId as any
    })

    if (this.mode() === "view") {
      this.editable.set(false)
      this.form.disable()
    }
  }

  async getRole() {
    const res = await this.lookupService.getRole()
    const filteredRole = res.filter((role:any) => role.key !== 1)
    this.roles.set(filteredRole)
  }

  onEdit() {
    this.form.enable();
    this.editable.set(true)
  }

  async onSubmit() {
    if (this.form.valid) {
      const {firstName, lastName, email, userName, mobileNo, roleId} = this.form.value
      let payload = {
        id: 0,
        firstName: firstName!,
        lastName: lastName!, 
        email: email!,
        userName: userName!,
        mobileNo: mobileNo!,
        roleId: roleId!
      }
      const res = await this.userService.create(payload)
      if (res.isSuccess) {
        this.closed.emit(true)
      }
    }
  }

  goToUsers() {
    this.closed.emit(true)
  }
}
