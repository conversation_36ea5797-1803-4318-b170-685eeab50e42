import { Component, computed, inject, input, OnInit, output, resource, signal } from '@angular/core';
import { AbstractControl, FormBuilder, FormsModule, ReactiveFormsModule, ValidationErrors, Validators } from '@angular/forms';
import { MatButton } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormField, MatInput, MatError } from '@angular/material/input';
import { MatOption, MatSelect } from '@angular/material/select';
import { LookupService } from '../../../../services/x_apis/lookup.service';
import { Router } from '@angular/router';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { provideNativeDateAdapter } from '@angular/material/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { AlertService } from '../../../../services/alert.service';
import { DatePipe } from '@angular/common';
import { InputformatService } from '../../../../services/x_apis/inputformat.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { Vendor } from '../../../../models/x_models/vendor';
import { VendorService } from '../../../../services/x_apis/vendor/vendor.service';

@Component({
  selector: 'app-register',
  imports: [FormsModule,
    MatFormField,
    MatInput,
    ReactiveFormsModule,
    MatError,
    MatOption,
    MatSelect,
    MatButton,
    MatIconModule,
    MatDatepickerModule,
    MatFormFieldModule, TranslateModule],
  providers: [provideNativeDateAdapter(), DatePipe],
  templateUrl: './register.component.html',
  styleUrl: './register.component.scss'
})
export class RegisterComponent {
  menuService = inject(MenuService);
  vendorService = inject(VendorService)
  alert = inject(AlertService);
  inputFormatService = inject(InputformatService);
  translate = inject(TranslateService);

  lookupService = inject(LookupService);
  vendorType = resource({ loader: () => this.lookupService.getVendorType() }).value;

  mode = input.required<string>();
  vendorId = input.required<any>()
  closed = output<boolean>();
  router = inject(Router);
  fb = inject(FormBuilder);
  editable = signal<boolean>(true);
  vendor = signal<Vendor | null>(null);
  minToDate = signal<Date>(new Date());

  form = this.fb.group({
    id: [0],
    name: this.fb.control<string>('', Validators.required),
    regionalName: this.fb.control<string>('', Validators.required),
    authPersonName: this.fb.control<string>('', Validators.required),
    email: this.fb.control<string>('', [Validators.required, this.emailValidator]),
    mobileNo: this.fb.control<string>('', [Validators.required, Validators.pattern("^[6-9][0-9]{9}$")]),
    whatsAppNo: this.fb.control<string>('', [Validators.required, Validators.pattern("^[6-9][0-9]{9}$")]),
    vendorType:this.fb.control<number | null>(null, Validators.required)
  });

  constructor(private datePipe: DatePipe) { }

  ngOnInit() {
    if (this.mode() === "edit" || this.mode() === "view") {
      this.getCrop();
    }
  }

  readonly search = signal('');

  readonly filteredTaluks = computed(() =>
    this.vendorType()?.filter(x =>
      x.value.toLowerCase().includes(this.search().toLowerCase())
    )
  );

  filter(value: any) {
    this.search.set(value.target.value);
  }

  async getCrop() {
    const res = await this.vendorService.getById(this.vendorId());
    this.vendor.set(res);
    this.form.patchValue({ ...res });
    if (this.mode() === "view") {
      this.editable.set(false)
      this.form.disable()
    }
  }

  onEdit() {
    this.form.enable();
    this.editable.set(true)
  }

  async onSubmit() {
    if (this.form.valid) {
      const formValue = { ...this.form.value };
      const res: any = await this.vendorService.create(formValue as Vendor)
      if (res.isSuccess) {
        this.closed.emit(true)
        if (formValue.id == 0) {
          this.alert.success(`Vendor Created Successfully.`)
        }
        else {
          this.alert.success(`Vendor Updated Successfully.`)
        }
      }
    }
    else {
      this.form.markAllAsTouched();
    }
  }

  navigateBack() {
    this.closed.emit(true)
  }

  formReset() {
    let indexId = this.form.value.id;
    this.form.reset();
    this.form.patchValue({
      id: indexId
    });
  }

  onInput(event: any, regexPattern: any): void {
    this.inputFormatService.isNumericOrHyphenOrSpace(event, regexPattern);
  }

  emailValidator(control: AbstractControl): ValidationErrors | null {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    if (control.value && !emailRegex.test(control.value)) {
      return { invalidEmail: true };
    }
    return null;
  }
}
