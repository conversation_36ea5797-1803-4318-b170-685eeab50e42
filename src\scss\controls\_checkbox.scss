body {
  --check-box-label-padding-left: 4px;
}

.mat-mdc-checkbox {
    .mdc-label {
      margin-bottom: -2px;
      padding-left: var(--check-box-label-padding-left);
    }

    .mdc-checkbox__background {
        width: 16px;
        height: 16px;
        border-radius: 2px;
        border: 1.5px solid var(--grey-100);
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .mdc-checkbox__checkmark {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-48%, -49%);
        width: 78%;
        opacity: 0;
        transition: opacity 180ms cubic-bezier(0.4, 0, 0.6, 1);
        color: var(--mdc-checkbox-selected-checkmark-color, var(--mat-sys-on-primary));
    }

    .mdc-checkbox__mixedmark {
        width: 70%;
        transform: scaleX(0) rotate(0deg);
        opacity: 0;
        border-color: transparent;
        background-color: var(--mat-sys-on-primary);
        height: 1px;
        border-radius: 2px;
    }


    &.square .mdc-checkbox__background {
        border-radius: 2px;
    }


    &.bold .mdc-checkbox__background {
        border: 2px solid var(--grey-100);

        .mdc-checkbox__checkmark {
            width: 87%;
        }

        .mdc-checkbox__mixedmark {
            width: 80%;
        }
    }


    .mdc-checkbox{
        .mat-mdc-checkbox-ripple, .mdc-checkbox__ripple {
            display: none;
        }
        .mdc-checkbox__native-control:checked~.mdc-checkbox__ripple {
           display: none;
        }
    }

    .mdc-checkbox__native-control:focus:focus:not(:checked)~.mdc-checkbox__background, .mdc-checkbox__native-control:focus:focus:not(:indeterminate)~.mdc-checkbox__background {
        box-shadow: 0px 0px 0px 2px var(--theme-30) !important;
    }
    .mdc-checkbox__native-control:focus:focus:checked~.mdc-checkbox__background {
        box-shadow: 0px 0px 0px 1px var(--theme-30) !important;
        border-color: var(--theme-80);
        background-color: var(--theme-80);
    }

    .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background {
        border-color: var(--theme-80);
        background-color: var(--theme-80);
    }
    .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background {
        border-color: var(--grey-100);
        background-color: var(--grey-100);
    }

    .mdc-checkbox__native-control:focus:focus:indeterminate~.mdc-checkbox__background {
        border-color: var(--grey-100);
        background-color: var(--grey-100);
    }

    .mdc-checkbox:hover {
        .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background {
            border-color: var(--theme-80);
            background-color: var(--theme-80);
        }
        .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background {
            border-color: var(--grey-100);
            background-color: var(--grey-100);
        }
    }

    &:not(.mat-mdc-checkbox-checked, [ng-reflect-indeterminate="true"]) {
        .mdc-checkbox {
            &:hover{
                .mat-mdc-checkbox-ripple, .mdc-checkbox__ripple {
                    display: none;
                }
                .mdc-checkbox__native-control:checked~.mdc-checkbox__ripple {
                   display: none;
                }
                .mdc-checkbox__background {
                    // background-color: var(--theme-30) !important;
                }
            }
        }
    }

}
