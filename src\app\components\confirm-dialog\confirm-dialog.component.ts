import {Component, inject} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialog, MatDialogConfig, MatDialogRef} from '@angular/material/dialog';
import {firstValueFrom} from 'rxjs';
import {<PERSON><PERSON><PERSON><PERSON>, MatIconButton} from '@angular/material/button';
import {MatIcon} from '@angular/material/icon';

@Component({
  selector: 'app-confirm-dialog',
  imports: [
    MatButton,
    MatIcon,
    MatIconButton
  ],
  templateUrl: './confirm-dialog.component.html',
  styleUrl: './confirm-dialog.component.scss'
})
export class ConfirmDialogComponent {
  data = inject(MAT_DIALOG_DATA)
  dialogRef = inject(MatDialogRef)
  prompt = "Do you want to proceed?"
  constructor() {
    console.log(this.data)
  }

  onClose() {
    this.dialogRef.close();
  }

  onConfirm() {
    this.dialogRef.close(true)
  }
}

export const openConfirmDialog = (dialog: MatDialog, data: any) => {
  const config = new MatDialogConfig();
  config.disableClose = false;
  config.autoFocus = true;
  config.data = data;

  const close$ = dialog.open(ConfirmDialogComponent, config).afterClosed()

  return firstValueFrom(close$)
}
