import { NgT<PERSON>plateOutlet, DatePipe, CurrencyPipe } from '@angular/common';
import { Component, computed, effect, inject, linkedSignal, OnInit, resource, signal, viewChild } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckbox } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInput, MatPrefix } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatOption, MatSelect } from '@angular/material/select';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { CloseFilterOnBlurDirective } from '../../../../directives/close-filter-on-blur.directive';
import { SeasonComponent } from './season.component';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { Season } from '../../../../models/x_models/masters/season';
import { StatusType } from '../../../../enums/x_enums/dotnet/status-type';
import { SeasonService } from '../../../../services/x_apis/masters/season.service';
import { confirmAndDelete } from '../../../../models/x_models/masters/shared';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatToolbarModule } from '@angular/material/toolbar';

@Component({
  selector: 'app-seasons',
  imports: [
    MatIcon,
    MatInput,
    MatPrefix,
    ReactiveFormsModule,
    MatCheckbox,
    MatPaginatorModule,
    MatTableModule,
    NgTemplateOutlet,
    MatButtonModule,
    MatMenuModule,
    DatePipe,
    MatSortModule,
    MatOption,
    MatSelect,
    CloseFilterOnBlurDirective,
    CurrencyPipe,
    MatFormFieldModule,
    SeasonComponent,
    TranslateModule,
    MatToolbarModule
  ],
  providers: [DatePipe],
  templateUrl: './seasons.component.html',
  styleUrls: ['./seasons.component.scss']
})
export class SeasonsComponent implements OnInit {
  menuService = inject(MenuService);
  access = computed(() => this.menuService.activeMenu()?.controlAccess);

  paginator = viewChild<MatPaginator>(MatPaginator);
  sort = viewChild<MatSort>(MatSort);

  translate = inject(TranslateService);
  seasonService = inject(SeasonService);
  sessionResource = resource({ loader: () => this.seasonService.get() });
  dataSource = linkedSignal(() => new MatTableDataSource(this.sessionResource.value()));

  loading = signal(false);
  mode = signal<'view' | 'edit' | 'add'>('add');
  search = new FormControl("");
  showGrid = signal(false);
  status = StatusType;

  season = signal<Season>({
    id: 0,
    seasonName: "",
    fromDate: "",
    toDate: ""
  });

  displayedColumns: string[] = [
    'seasonName',
    'fromDate',
    'toDate',
    'actions',
  ];

  constructor(private datePipe: DatePipe) {
    effect(() => {
      if (this.paginator()) {
        this.dataSource().sort = this.sort()!;
        this.dataSource().paginator = this.paginator()!;
      }
      this.dataSource().filterPredicate = (data: Season, filter: string) => {
        const formattedFromDate = this.datePipe.transform(data.fromDate, 'dd-MM-yyyy') ?? '';
        const formattedToDate = this.datePipe.transform(data.toDate, 'dd-MM-yyyy') ?? '';
        const combinedData = `${data.seasonName} ${formattedFromDate} ${formattedToDate}`.toLowerCase();
        return combinedData.includes(filter);
      };
    });
  }

  ngOnInit(): void {
  }

  onAdd() {
    this.season.set({
      id: 0,
      seasonName: "",
      fromDate: "",
      toDate: ""
    });
    this.mode.set("add");
    this.showGrid.set(true);
  }

  onEdit(season: Season) {
    this.season.set(season);
    this.mode.set('edit');
    this.showGrid.set(true);
  }

  onView(season: Season) {
    this.season.set(season);
    this.mode.set('view');
    this.showGrid.set(true);
  }

  onClose() {
    this.showGrid.set(false);
    this.sessionResource.reload();
    this.search.setValue("");
  }

  onRefresh() {
    this.sessionResource.reload();
    this.search.setValue("");
  }

  async onDelete(season: Season) {
    await confirmAndDelete(season, season.seasonName, 'Season', this.seasonService, () => this.sessionResource.reload());
  }

  onSearch() {
    const filterValue = this.search.value?.trim().toLowerCase() || '';
    this.dataSource().filter = filterValue;
  }

}
