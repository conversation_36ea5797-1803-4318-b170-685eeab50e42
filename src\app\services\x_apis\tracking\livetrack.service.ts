import { inject, Injectable } from '@angular/core';
import { DataService } from '../../data.service';
import { Response } from '../../../models/x_models/api-response';
import {VehicleResponse, Vehicles } from '../../../models/x_models/tracking/livetrack';

@Injectable({
    providedIn: 'root'
})
export class LivetrackService {
    dataService = inject(DataService)

    create(data: Vehicles) {
        return this.dataService.post<Response>("/vehicle", data)
    }

    get() {
        return this.dataService.get<VehicleResponse>("/vehicle")
    }

    getById(id: number) {
        return this.dataService.get<Vehicles>(`/vehicle/${id}`)
    }

    delete(id: number) {
        return this.dataService.delete<Response>(`/vehicle/${id}`)
    }

    getByRegionId(regionId: number) {
        return this.dataService.get<Vehicles>(`/vehiclestatus/${regionId}`)
    }
}
