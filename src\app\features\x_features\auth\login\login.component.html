<img class="bg-img" src="/images/login_bg.jpg" alt="">
<div class="component">
    <form class="login-form" [formGroup]="loginForm" (ngSubmit)="onLogin()">
        <mat-card class="card" tabindex="-1" appearance="outlined">
            <mat-card-header>
                <mat-card-title>Login</mat-card-title>
            </mat-card-header>
            <mat-card-content>
                <div class="field">
                  <label for="username"></label>
                  <mat-form-field appearance="outline">
                      <input matInput id="username" formControlName="username" placeholder="">
                      @if (loginForm.get("username")?.hasError("required")) {
                          <mat-error>Username is required </mat-error>
                      }
                    </mat-form-field>
                </div>

                <div class="field">
                  <label for="password">Password</label>
                  <mat-form-field appearance="outline">
                      <input id="password" matInput formControlName="password" [type]="hide() ? 'password' : 'text'" placeholder="" />
                      <button
                        type="button"
                        mat-icon-button
                        matSuffix
                        (click)="clickEvent($event)"
                        [attr.aria-label]="'Hide password'"
                        [attr.aria-pressed]="hide()"
                      >
                        <mat-icon class="material-symbols-rounded">{{hide() ? 'visibility_off' : 'visibility'}}</mat-icon>
                      </button>
                      @if (loginForm.get("password")?.hasError("required")) {
                          <mat-error>Password is required </mat-error>
                      }
                    </mat-form-field>
                </div>

            </mat-card-content>
            <mat-card-actions>
                <a class="link" (click)="onForgotPassword()">Forgot
                    Password?</a>
                <button class="btn" type="submit" mat-flat-button>Login</button>
              </mat-card-actions>
        </mat-card>
    </form>
</div>


