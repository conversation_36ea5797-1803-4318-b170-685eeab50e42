import { Component } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { PayrollService } from '../../../services/m_apis/service/payroll.service';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTableModule } from '@angular/material/table';
import { MatInput } from '@angular/material/input';
import { ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-payslipview',
  imports: [
    ReactiveFormsModule,
    CommonModule,
    MatInput,
    MatCardModule,
    MatTableModule,
    MatProgressSpinnerModule,
    MatButtonModule,
    MatIconModule,
    MatSnackBarModule,
    MatDividerModule
  ],
  templateUrl: './payslipview.component.html',
  styleUrl: './payslipview.component.scss'
})
export class PayslipviewComponent {
 empId: string | null = null;
  
  showNotification = false;
  isLoading = false;
  empObj: any;
 constructor(
    private route: ActivatedRoute,
    private service: PayrollService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.empId = this.route.snapshot.paramMap.get('empId');
    if (this.empId) {
      this.getEmployeeId();
    } else {
      console.error('Employee ID is null');
      this.router.navigateByUrl('/main/payroll');
    }

  }




   getEmployeeId() {
    if (this.empId) {
      this.isLoading = true;
      this.service.getpayslipapproved(this.empId).subscribe({
        next: (res: any) => {
          console.log('Employee Details:', res);
          
          this.empObj = res;
                    console.log("detatis",this.empObj);

          this.isLoading = false;
        },
        error: (error: any) => {
          console.error('Error fetching employee details:', error);
          this.isLoading = false;
          // Handle error appropriately
        }
      });
    }
  }
  
download() {
    if (this.empId) {
      this.service.downloadPayslip(this.empId);
    } else {
      console.error('Employee ID is null');
      // Optionally, show a notification or handle the error as needed
    }
    // .subscribe({
    //   next: (response) => {
    //     const blob = new Blob([response], { type: 'application/pdf' });
    //     const fileURL = window.URL.createObjectURL(blob);

    //     const link = document.createElement('a');
    //     link.href = fileURL;
    //     link.download = `payslip_${empId}.pdf`;
    //     link.click();
    //     window.URL.revokeObjectURL(fileURL);
    //   },
    //   error: (error) => {
    //     console.error('Error downloading payslip', error);
    //     alert('Failed to download payslip');
    //   }
    // });
  }


}
