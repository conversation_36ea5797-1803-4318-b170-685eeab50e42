import { Component, computed, inject, input, OnInit, output, resource, signal } from '@angular/core';
import { AbstractControl, FormBuilder, FormsModule, ReactiveFormsModule, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { <PERSON><PERSON><PERSON>on } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormField, MatInput, MatError } from '@angular/material/input';
import { MatOption, MatSelect } from '@angular/material/select';
import { Router } from '@angular/router';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { DatePipe } from '@angular/common';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MenuService } from '../../../../../services/x_apis/menu.service';
import { CropService } from '../../../../../services/x_apis/masters/crop.service';
import { AlertService } from '../../../../../services/alert.service';
import { InputformatService } from '../../../../../services/x_apis/inputformat.service';
import { LookupService } from '../../../../../services/x_apis/lookup.service';
import { Crop } from '../../../../../models/x_models/masters/crop';
import { MatNativeDateModule } from '@angular/material/core';

@Component({
  selector: 'app-supplier',
  imports: [
    MatFormField,
    MatInput,
    ReactiveFormsModule,
    MatError,
    MatOption,
    MatSelect,
    MatButton,
    MatIconModule,
    MatDatepickerModule,
    MatFormFieldModule,
    TranslateModule,
    MatNativeDateModule],
  templateUrl: './supplier.component.html',
  styleUrl: './supplier.component.scss'
})
export class SupplierComponent {
  menuService = inject(MenuService);
  cropService = inject(CropService)
  alert = inject(AlertService);
  inputFormatService = inject(InputformatService);
  translate = inject(TranslateService);

  lookupService = inject(LookupService);
  seasons = resource({ loader: () => this.lookupService.getSeason() }).value;

  mode = input.required<string>();
  cropId = input.required<any>()
  closed = output<boolean>();
  router = inject(Router);
  fb = inject(FormBuilder);
  editable = signal<boolean>(true);
  crop = signal<Crop | null>(null);
  minToDate = signal<Date>(new Date());

 form = this.fb.group({
    id: [0],
    GSTNumber: this.fb.control<string>('', [Validators.required, this.gstValidator]),
    panNo: this.fb.control<string>('', [Validators.required, this.panValidator]),
    LegalBusinessName: this.fb.control<string>(''),
    uin: this.fb.control<string>(''),
    DateofRegistration: this.fb.control<string>(''),
    StatusofGSTIN: this.fb.control<string>(''),
    NatureofBusiness: this.fb.control<string>(''),
    LandlineNumberofCompany: this.fb.control<string>(''),
    ContactPersonName: this.fb.control<string>(''),
    Address: this.fb.control<string>(''),

    PANNo: this.fb.control<string>('', Validators.required),
    FSSAI: this.fb.control<string>('', Validators.required),
    MSME: this.fb.control<string>('', Validators.required),
    AgmarkLicense: this.fb.control<string>('', Validators.required),
    ContactEmail: this.fb.control<string>('', [Validators.required, this.emailValidator]),
    OwnerName: this.fb.control<string>('', Validators.required),
    MobileNo: this.fb.control<string>('', [Validators.required, Validators.pattern("^[6-9][0-9]{9}$")]),
    Region: this.fb.control<number | null>(null, Validators.required),
    Unit: this.fb.control<number | null>(null, Validators.required),
    Taluk: this.fb.control<number | null>(null, Validators.required),
    Block: this.fb.control<number | null>(null, Validators.required),
    Village: this.fb.control<number | null>(null, Validators.required),
    Pincode: this.fb.control<string>('', Validators.required),

    BankAccountNumber: this.fb.control<string>('', Validators.required),
    ConfirmBankAccountNumber: this.fb.control<string>('', Validators.required),
    IFSCCode: this.fb.control<string>('', Validators.required),
    BankName: this.fb.control<string>('', Validators.required),
    Branch: this.fb.control<string>('', Validators.required),
    Commodity: this.fb.control<number | null>(null, Validators.required),

    ContractID: this.fb.control<string>('', Validators.required),
    ContractStartDate: this.fb.control<string>('', Validators.required),
    ContractEndDate: this.fb.control<string>('', Validators.required),
    ContractDoc: this.fb.control<string>('', Validators.required),

    MillName: this.fb.control<string>('', Validators.required),
    MillOwnerName: this.fb.control<string>('', Validators.required),
    MillType: this.fb.control<string>('', Validators.required),
    MillCode: this.fb.control<string>('', Validators.required),
    Latitude: this.fb.control<string>('', Validators.required),
    Longitude: this.fb.control<string>('', Validators.required),
    AdhaarNo: this.fb.control<string>('', Validators.required),
    FactoryLicenseNo: this.fb.control<string>('', Validators.required),
    FactoryLicenseUpto: this.fb.control<string>('', Validators.required),
    HullingMonthlyCapacityinMTs: this.fb.control<string>('', Validators.required),
    IsGeneratorAvailable: this.fb.control<string>('', Validators.required),
    IsSeparateEBAvailable: this.fb.control<string>('', Validators.required),
    EBConsumptionNoBoiler: this.fb.control<string>('', Validators.required),
    TypeofEBServiceOffice: this.fb.control<string>('', Validators.required),
    BoilerLicenseNo: this.fb.control<string>('', Validators.required),
    MRMType: this.fb.control<string>('', Validators.required),
    AgreementUpto: this.fb.control<string>('', Validators.required),
    SecurityDepositValue: this.fb.control<string>('', Validators.required),
    BankGuaranteeValue: this.fb.control<string>('', Validators.required),
    BGNumber: this.fb.control<string>('', Validators.required),
    BGValidUpTo: this.fb.control<string>('', Validators.required),
    GenModel: this.fb.control<string>('', Validators.required),
    EBConsumptionNoMill: this.fb.control<string>('', Validators.required),
    TypeofEBServiceBoiler: this.fb.control<string>('', Validators.required),
    BoilerLicenseUpto: this.fb.control<string>('', Validators.required),
    DateOfAppointment: this.fb.control<string>('', Validators.required),
    Capacity: this.fb.control<string>('', Validators.required),
    TypeofEBServiceMill: this.fb.control<string>('', Validators.required),
    EBConsumptionNoOffice: this.fb.control<string>('', Validators.required)
  },
  { validators: this.matchAccountNumbers() }
);

  constructor() { }

  ngOnInit() {
    if (this.mode() === "edit" || this.mode() === "view") {
      this.getCrop();
    }
  }

  readonly search = signal('');

  readonly filteredTaluks = computed(() =>
    this.seasons()?.filter(x =>
      x.value.toLowerCase().includes(this.search().toLowerCase())
    )
  );

  filter(value: any) {
    this.search.set(value.target.value);
  }

    emailValidator(control: AbstractControl): ValidationErrors | null {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    if (control.value && !emailRegex.test(control.value)) {
      return { invalidEmail: true };
    }
    return null;
  }

  gstValidator(control: AbstractControl): ValidationErrors | null {
    const gstRegex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/;
    const value = control.value?.toUpperCase();
    return gstRegex.test(value) ? null : { invalidGst: true };
  }

  panValidator(control: AbstractControl): ValidationErrors | null {
    const panRegex = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/;
    const value = control.value?.toUpperCase();
    return panRegex.test(value) ? null : { invalidPan: true };
  }

    matchAccountNumbers(): ValidatorFn {
    return (group: AbstractControl): ValidationErrors | null => {
      const accountNumber = group.get('BankAccountNumber')?.value;
      const confirmAccountNumber = group.get('ConfirmBankAccountNumber')?.value;
  
      return accountNumber === confirmAccountNumber ? null : { noMatch: true };
    };
  }

  async getCrop() {
    const res = await this.cropService.getById(this.cropId());
    this.crop.set(res);
    this.form.patchValue({ ...res });
    if (this.mode() === "view") {
      this.editable.set(false)
      this.form.disable()
    }
  }

  onEdit() {
    this.form.enable();
    this.editable.set(true)
  }

  async onSubmit() {
    if (this.form.valid) {
      const formValue = { ...this.form.value };
      const res = await this.cropService.create(formValue as Crop)
      if (res.isSuccess) {
        this.closed.emit(true)
        if (formValue.id == 0) {
          this.alert.success(`Vendor Created Successfully.`)
        }
        else {
          this.alert.success(`Vendor Updated Successfully.`)
        }
      }
    }
    else {
      this.form.markAllAsTouched();
    }
  }

  navigateBack() {
    this.closed.emit(true)
  }

  formReset() {
    let indexId = this.form.value.id;
    this.form.reset();
    this.form.patchValue({
      id: indexId
    });
  }

  onInput(event: any, regexPattern: any): void {
    this.inputFormatService.isNumericOrHyphenOrSpace(event, regexPattern);
  }


}
