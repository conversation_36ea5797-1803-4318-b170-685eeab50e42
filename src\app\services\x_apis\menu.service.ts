import {inject, Injectable, signal} from '@angular/core';
import {DataService} from '../data.service';
import {Menu} from '../../models/x_models/menu';

@Injectable({
  providedIn: 'root'
})
export class MenuService {
  dataService = inject(DataService)

  #activeMenuSignal = signal<Menu | null>(null)
  activeMenu = this.#activeMenuSignal.asReadonly();

  #menuSignal = signal<Menu[]>([])
  menu = this.#menuSignal.asReadonly()

  get() {
    // let lang = localStorage.getItem('language') === 'en' ? 1 : 2;
    let lang = 1;
    return this.dataService.get<Menu[]>(`/user/menu/${lang}`)
  }

  setActiveMenu(menu: Menu) {
    this.#activeMenuSignal.set(menu)
  }


}
