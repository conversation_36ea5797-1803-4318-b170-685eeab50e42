import { NgT<PERSON>plateOutlet, DatePipe, CurrencyPipe } from '@angular/common';
import { Component, computed, effect, inject, linkedSignal, resource, signal, viewChild } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckbox } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInput, MatPrefix } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatOption, MatSelect } from '@angular/material/select';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { CloseFilterOnBlurDirective } from '../../../../directives/close-filter-on-blur.directive';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { confirmAndDelete } from '../../../../models/x_models/masters/shared';
import { StatusType } from '../../../../enums/x_enums/dotnet/status-type';
import { ProviderService } from '../../../../services/x_apis/masters/provider.service';
import { Providers } from '../../../../models/x_models/masters/provider';
import { ProviderComponent } from './provider.component';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatToolbarModule } from '@angular/material/toolbar';

@Component({
    selector: 'app-providers',
    imports: [MatIcon,
        MatIcon,
        MatInput,
        MatPrefix,
        ReactiveFormsModule,
        MatCheckbox,
        MatPaginatorModule,
        MatTableModule,
        NgTemplateOutlet,
        MatButtonModule,
        TranslateModule,
        MatMenuModule,
        DatePipe,
        MatToolbarModule,
        MatSortModule,
        MatOption,
        MatSelect,
        CloseFilterOnBlurDirective, CurrencyPipe, MatFormFieldModule, ProviderComponent],
    templateUrl: './providers.component.html',
    styleUrl: './providers.component.scss'
})
export class ProvidersComponent {
    menuService = inject(MenuService);
    access = computed(() => this.menuService.activeMenu()?.controlAccess);
    translate = inject(TranslateService);

    paginator = viewChild<MatPaginator>(MatPaginator);
    sort = viewChild<MatSort>(MatSort);

    providerService = inject(ProviderService);
    providerResource = resource({ loader: () => this.providerService.get() });
    dataSource = linkedSignal(() => new MatTableDataSource(this.providerResource.value()));

    loading = signal(false);
    mode = signal<'view' | 'edit' | 'add'>('add');
    search = new FormControl("");
    showGrid = signal(false);
    status = StatusType;

    list = signal<Providers>({
        id: 0,
        providerName1: "",
        mobileNo1: ""
    });

    displayedColumns: string[] = [
        'providerName1',
        'mobileNo1',
        'actions'
    ];

    constructor() {
        effect(() => {
            if (this.paginator()) {
                this.dataSource().sort = this.sort()!;
                this.dataSource().paginator = this.paginator()!
            }
        });
    }

    ngOnInit(): void {
    }

    onAdd() {
        this.list.set({
            id: 0,
            providerName1: "",
            mobileNo1: ""
        });
        this.mode.set("add")
        this.showGrid.set(true)
    }

    onView(rowData: Providers) {
        this.list.set(rowData);
        this.mode.set('view');
        this.showGrid.set(true)
    }

    onEdit(rowData: Providers) {
        this.list.set(rowData);
        this.mode.set('edit');
        this.showGrid.set(true)
    }

    onClose() {
        this.showGrid.set(false)
        this.providerResource.reload()
        this.search.setValue("");
    }

    onRefresh() {
        this.providerResource.reload()
        this.search.setValue("");
    }

    async onDelete(rowData: Providers) {
        await confirmAndDelete(rowData, rowData.providerName1, 'Provider', this.providerService, () => this.providerResource.reload());
    }
    onSearch() {
        const filterValue = this.search.value?.trim().toLowerCase() || '';
        this.dataSource().filter = filterValue;
      }

}
