import { inject, Injectable } from '@angular/core';
import { DataService } from '../../data.service';
import { Response } from '../../../models/x_models/api-response';
import { Region, RegionResponse } from '../../../models/x_models/masters/region';

@Injectable({
  providedIn: 'root'
})
export class RegionService {

  dataService = inject(DataService)

  create(data: Region) {
      return this.dataService.post<Response>("/region", data)
    }
  
    get() {
      return this.dataService.get<RegionResponse>("/region")
    }
  
    getById(id: number) {
      return this.dataService.get<Region>(`/region/${id}`)
    }
  
    delete(id: number) {
      return this.dataService.delete<Response>(`/region/${id}`)
    }

}
