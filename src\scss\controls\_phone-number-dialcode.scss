.code {
    .mat-mdc-select-arrow-wrapper.mat-mdc-select-arrow-wrapper .mat-mdc-select-arrow::after {
        font-size: 17px;
        left: 0px;
        font-weight: 400;
    }

    .mat-mdc-select-value {
        text-overflow: clip;
    }
}

.mat-mdc-select-panel:has(.code-options) {
    min-width: 200px;

    .dial-code {
        font-weight: 500;
        font-size: 11px;
        opacity: 0.8;
        margin-left: 2px;
        line-height: 16px;
    }
}

.flag {
  background: url("/icons/flags.png") no-repeat 0 0;
  background-size: 24px 4012px;
  width: 24px;
  height: 16px;
  overflow: hidden;
  margin-right: 13px;
  display: inline-block;

  &.IN {background-position: -1px -2245px;}
  &.AF {background-position: -1px -3180px;}
  &.AX {background-position: -1px -3996px;}
  &.AL {background-position: -1px -1310px;}
  &.D<PERSON> {background-position: -1px -681px;}
  &.AS {background-position: -1px -2058px;}
  &.AD {background-position: -1px -766px;}
  &.AO {background-position: -1px -2636px;}
  &.AI {background-position: -1px -2687px;}
  &.AG {background-position: -1px -1140px;}
  &.AR {background-position: -1px -3282px;}
  &.AM {background-position: -1px -205px;}
  &.AW {background-position: -1px -1021px;}
  &.AC {background-position: -1px -86px;}
  &.AU {background-position: -1px -2279px;}
  &.AT {background-position: -1px -1735px;}
  &.AZ {background-position: -1px -1599px;}
  &.BS {background-position: -1px -460px;}
  &.BH {background-position: -1px -1956px;}
  &.BD {background-position: -1px -2364px;}
  &.BB {background-position: -1px -2075px;}
  &.BY {background-position: -1px -1412px;}
  &.BE {background-position: -1px -1px;}
  &.BZ {background-position: -1px -613px;}
  &.BJ {background-position: -1px -1684px;}
  &.BM {background-position: -1px -2585px;}
  &.BT {background-position: -1px -2483px;}
  &.BO {background-position: -1px -2177px;}
  &.BA {background-position: -1px -2092px;}
  &.BW {background-position: -1px -3724px;}
  &.BR {background-position: -1px -1004px;}
  &.IO {background-position: -1px -86px;}
  &.VG {background-position: -1px -1854px;}
  &.BN {background-position: -1px -2228px;}
  &.BG {background-position: -1px -3537px;}
  &.BF {background-position: -1px -953px;}
  &.BI {background-position: -1px -2551px;}
  &.KH {background-position: -1px -290px;}
  &.CM {background-position: -1px -2806px;}
  &.CA {background-position: -1px -1803px;}
  &.CV {background-position: -1px -3639px;}
  &.BQ {background-position: -1px -3741px;}
  &.KY {background-position: -1px -375px;}
  &.CF {background-position: -1px -2466px;}
  &.TD {background-position: -1px -1055px;}
  &.CL {background-position: -1px -1752px;}
  &.CN {background-position: -1px -1072px;}
  &.CX {background-position: -1px -3945px;}
  &.CC {background-position: -1px -3928px;}
  &.CO {background-position: -1px -409px;}
  &.KM {background-position: -1px -1871px;}
  &.CG {background-position: -1px -2398px;}
  &.CD {background-position: -1px -1990px;}
  &.CK {background-position: -1px -3112px;}
  &.CR {background-position: -1px -2857px;}
  &.CI {background-position: -1px -2194px;}
  &.HR {background-position: -1px -1174px;}
  &.CU {background-position: -1px -987px;}
  &.CW {background-position: -1px -3758px;}
  &.CY {background-position: -1px -732px;}
  &.CZ {background-position: -1px -3095px;}
  &.DK {background-position: -1px -1820px;}
  &.DJ {background-position: -1px -2874px;}
  &.DM {background-position: -1px -3350px;}
  &.DO {background-position: -1px -2007px;}
  &.EC {background-position: -1px -1531px;}
  &.EG {background-position: -1px -3027px;}
  &.SV {background-position: -1px -2160px;}
  &.GQ {background-position: -1px -1973px;}
  &.ER {background-position: -1px -936px;}
  &.EE {background-position: -1px -3333px;}
  &.SZ {background-position: -1px -3129px;}
  &.ET {background-position: -1px -3367px;}
  &.FK {background-position: -1px -3809px;}
  &.FO {background-position: -1px -1429px;}
  &.FJ {background-position: -1px -2500px;}
  &.FI {background-position: -1px -2568px;}
  &.FR {background-position: -1px -324px;}
  &.GF {background-position: -1px -324px;}
  &.PF {background-position: -1px -2262px;}
  &.GA {background-position: -1px -1157px;}
  &.GM {background-position: -1px -817px;}
  &.GE {background-position: -1px -1123px;}
  &.DE {background-position: -1px -3452px;}
  &.GH {background-position: -1px -2891px;}
  &.GI {background-position: -1px -341px;}
  &.GR {background-position: -1px -188px;}
  &.GL {background-position: -1px -2347px;}
  &.GD {background-position: -1px -3316px;}
  &.GP {background-position: -1px -511px;}
  &.GU {background-position: -1px -3265px;}
  &.GT {background-position: -1px -1208px;}
  &.GG {background-position: -1px -3877px;}
  &.GN {background-position: -1px -3520px;}
  &.GW {background-position: -1px -2602px;}
  &.GY {background-position: -1px -1038px;}
  &.HT {background-position: -1px -392px;}
  &.HN {background-position: -1px -2959px;}
  &.HK {background-position: -1px -3707px;}
  &.HU {background-position: -1px -902px;}
  &.IS {background-position: -1px -2704px;}
  &.IN {background-position: -1px -2245px;}
  &.ID {background-position: -1px -2653px;}
  &.IR {background-position: -1px -2738px;}
  &.IQ {background-position: -1px -851px;}
  &.IE {background-position: -1px -2670px;}
  &.IM {background-position: -1px -3894px;}
  &.IL {background-position: -1px -426px;}
  &.IT {background-position: -1px -154px;}
  &.JM {background-position: -1px -2296px;}
  &.JP {background-position: -1px -528px;}
  &.JE {background-position: -1px -3911px;}
  &.JO {background-position: -1px -1905px;}
  &.KZ {background-position: -1px -1565px;}
  &.KE {background-position: -1px -3605px;}
  &.KI {background-position: -1px -477px;}
  &.XK {background-position: -1px -3860px;}
  &.KW {background-position: -1px -3435px;}
  &.KG {background-position: -1px -2143px;}
  &.LA {background-position: -1px -562px;}
  &.LV {background-position: -1px -2619px;}
  &.LB {background-position: -1px -1616px;}
  &.LS {background-position: -1px -3010px;}
  &.LR {background-position: -1px -2823px;}
  &.LY {background-position: -1px -137px;}
  &.LI {background-position: -1px -1276px;}
  &.LT {background-position: -1px -1446px;}
  &.LU {background-position: -1px -1922px;}
  &.MO {background-position: -1px -3554px;}
  &.MG {background-position: -1px -1667px;}
  &.MW {background-position: -1px -2942px;}
  &.MY {background-position: -1px -2517px;}
  &.MV {background-position: -1px -800px;}
  &.ML {background-position: -1px -3469px;}
  &.MT {background-position: -1px -2041px;}
  &.MH {background-position: -1px -1463px;}
  &.MQ {background-position: -1px -239px;}
  &.MR {background-position: -1px -307px;}
  &.MU {background-position: -1px -2993px;}
  &.YT {background-position: -1px -324px;}
  &.MX {background-position: -1px -2755px;}
  &.FM {background-position: -1px -2313px;}
  &.MD {background-position: -1px -3690px;}
  &.MC {background-position: -1px -1191px;}
  &.MN {background-position: -1px -3503px;}
  &.ME {background-position: -1px -2976px;}
  &.MS {background-position: -1px -749px;}
  &.MA {background-position: -1px -3214px;}
  &.MZ {background-position: -1px -834px;}
  &.MM {background-position: -1px -18px;}
  &.NA {background-position: -1px -2534px;}
  &.NR {background-position: -1px -2330px;}
  &.NP {background-position: -1px -120px;}
  &.NL {background-position: -1px -1888px;}
  &.NC {background-position: -1px -1650px;}
  &.NZ {background-position: -1px -2024px;}
  &.NI {background-position: -1px -171px;}
  &.NE {background-position: -1px -715px;}
  &.NG {background-position: -1px -3418px;}
  &.NU {background-position: -1px -2840px;}
  &.NF {background-position: -1px -256px;}
  &.KP {background-position: -1px -2415px;}
  &.MK {background-position: -1px -1769px;}
  &.MP {background-position: -1px -919px;}
  &.NO {background-position: -1px -1089px;}
  &.OM {background-position: -1px -3384px;}
  &.PK {background-position: -1px -2772px;}
  &.PW {background-position: -1px -273px;}
  &.PS {background-position: -1px -1548px;}
  &.PA {background-position: -1px -1106px;}
  &.PG {background-position: -1px -1939px;}
  &.PY {background-position: -1px -3231px;}
  &.PE {background-position: -1px -1225px;}
  &.PH {background-position: -1px -2432px;}
  &.PL {background-position: -1px -1514px;}
  &.PT {background-position: -1px -664px;}
  &.PR {background-position: -1px -596px;}
  &.QA {background-position: -1px -579px;}
  &.RE {background-position: -1px -324px;}
  &.RO {background-position: -1px -885px;}
  &.RU {background-position: -1px -868px;}
  &.RW {background-position: -1px -3673px;}
  &.WS {background-position: -1px -3163px;}
  &.SM {background-position: -1px -2908px;}
  &.ST {background-position: -1px -3299px;}
  &.SA {background-position: -1px -52px;}
  &.SN {background-position: -1px -2925px;}
  &.RS {background-position: -1px -3401px;}
  &.SC {background-position: -1px -1327px;}
  &.SL {background-position: -1px -970px;}
  &.SG {background-position: -1px -35px;}
  &.SX {background-position: -1px -3826px;}
  &.SK {background-position: -1px -3044px;}
  &.SI {background-position: -1px -1582px;}
  &.SB {background-position: -1px -1361px;}
  &.SO {background-position: -1px -1786px;}
  &.ZA {background-position: -1px -3248px;}
  &.KR {background-position: -1px -3078px;}
  &.SS {background-position: -1px -3775px;}
  &.ES {background-position: -1px -1480px;}
  &.LK {background-position: -1px -3622px;}
  &.BL {background-position: -1px -324px;}
  &.SH {background-position: -1px -630px;}
  &.KN {background-position: -1px -103px;}
  &.LC {background-position: -1px -1837px;}
  &.MF {background-position: -1px -86px;}
  &.PM {background-position: -1px -1378px;}
  &.VC {background-position: -1px -3588px;}
  &.SD {background-position: -1px -443px;}
  &.SR {background-position: -1px -3656px;}
  &.SJ {background-position: -1px -1089px;}
  &.SE {background-position: -1px -494px;}
  &.CH {background-position: -1px -1718px;}
  &.SY {background-position: -1px -2449px;}
  &.TW {background-position: -1px -647px;}
  &.TJ {background-position: -1px -222px;}
  &.TZ {background-position: -1px -3146px;}
  &.TH {background-position: -1px -1242px;}
  &.TL {background-position: -1px -3843px;}
  &.TG {background-position: -1px -783px;}
  &.TK {background-position: -1px -3792px;}
  &.TO {background-position: -1px -1395px;}
  &.TT {background-position: -1px -545px;}
  &.TA {background-position: -1px -3979px;}
  &.TN {background-position: -1px -698px;}
  &.TR {background-position: -1px -2126px;}
  &.TM {background-position: -1px -3486px;}
  &.TC {background-position: -1px -1701px;}
  &.TV {background-position: -1px -358px;}
  &.VI {background-position: -1px -2381px;}
  &.UG {background-position: -1px -1497px;}
  &.UA {background-position: -1px -2721px;}
  &.AE {background-position: -1px -3061px;}
  &.GB {background-position: -1px -86px;}
  &.US {background-position: -1px -69px;}
  &.UY {background-position: -1px -3571px;}
  &.UZ {background-position: -1px -1293px;}
  &.VU {background-position: -1px -1633px;}
  &.VA {background-position: -1px -3197px;}
  &.VE {background-position: -1px -1344px;}
  &.VN {background-position: -1px -1259px;}
  &.WF {background-position: -1px -324px;}
  &.EH {background-position: -1px -3962px;}
  &.YE {background-position: -1px -2211px;}
  &.ZM {background-position: -1px -2109px;}
  &.ZW {background-position: -1px -2789px;}
}

mat-select-trigger {
  .flag {
    top: 4px;
    position: relative;
  }
}