import { <PERSON><PERSON><PERSON><PERSON>, NgTemplateOutlet } from '@angular/common';
import { Component, computed, effect, inject, linkedSignal, OnInit, resource, signal, viewChild } from '@angular/core';
import { ReactiveFormsModule, FormControl } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInput, MatPrefix } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginatorModule, MatPaginator } from '@angular/material/paginator';
import { MatSortModule, MatSort } from '@angular/material/sort';
import { MatTableModule, MatTableDataSource } from '@angular/material/table';
import { StatusType } from '../../../../enums/x_enums/dotnet/status-type';
import { confirmAndDelete } from '../../../../models/x_models/masters/shared';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { DpcComponent } from './dpc.component';
import { DpcService } from '../../../../services/x_apis/masters/dpc.service';
import { dpcs } from '../../../../models/x_models/masters/dpc';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatToolbarModule } from '@angular/material/toolbar';

@Component({
  selector: 'app-DPCs',
  imports: [
    MatIcon,
    MatInput,
    MatPrefix,
    ReactiveFormsModule,
    MatPaginatorModule,
    MatTableModule,
    NgTemplateOutlet,
    MatButtonModule,
    MatMenuModule,
    MatSortModule,
    MatFormFieldModule, 
    DpcComponent,
    DatePipe,
    TranslateModule,
    MatToolbarModule
  ],
  templateUrl: './dpcs.component.html',
  styleUrls: ['./dpcs.component.scss']
})


export class DpcsComponent implements OnInit {
  menuService = inject(MenuService);
  access = computed(() => this.menuService.activeMenu()?.controlAccess);
  translate = inject(TranslateService);

  paginator = viewChild<MatPaginator>(MatPaginator);
  sort = viewChild<MatSort>(MatSort);

  dpcService = inject(DpcService);
  DpcResource = resource({ loader: () => this.dpcService.get() });
  dataSource = linkedSignal(() => new MatTableDataSource(this.DpcResource.value()));

  loading = signal(false);
  mode = signal<'view' | 'edit' | 'add'>('add');
  search = new FormControl("");
  showGrid = signal(false);
  status = StatusType;

  dpc = signal<dpcs>({
    id: 0,
    dpcCode: "",
    dpcName: "",
    permanentAddress: "",
    dpcTypeName: "",
    posTypeName: "",
    fromDate: "",
    toDate: "",
    villageName: "",
    agencyName: "",
    storageLocationName: ""
  });

  displayedColumns: string[] = [
    'villageName',
    'agencyName',
    'dpcCode',
    'dpcName',
    'dpcTypeName',
    'actions',
  ];

  constructor() {
    effect(() => {
      if (this.paginator()) {
        this.dataSource().sort = this.sort()!;
        this.dataSource().paginator = this.paginator()!
      }
    })
  }

  ngOnInit() {
  }


  onAdd() {
    this.dpc.set({
      id: 0,
      dpcCode: "",
      dpcName: "",
      permanentAddress: "",
      dpcTypeName: "",
      posTypeName: "",
      fromDate: "",
      toDate: "",
      villageName: "",
      agencyName: "",
      storageLocationName: ""
    });
    this.mode.set("add")
    this.showGrid.set(true)
  }

  onView(rowData: dpcs) {
    this.dpc.set(rowData);
    this.mode.set('view');
    this.showGrid.set(true)
  }

  onEdit(rowData: dpcs) {
    this.dpc.set(rowData);
    this.mode.set('edit');
    this.showGrid.set(true)
  }

  onClose() {
    this.showGrid.set(false)
    this.DpcResource.reload()
    this.search.setValue("");
  }

  onRefresh() {
    this.DpcResource.reload();
    this.search.setValue("");
  }

  async onDelete(dpc: dpcs) {
    await confirmAndDelete(dpc, dpc.dpcName, 'DPC', this.dpcService, () => this.DpcResource.reload());
  }

  onSearch() {
    const filterValue = this.search.value?.trim().toLowerCase() || '';
    this.dataSource().filter = filterValue;
  }

}

