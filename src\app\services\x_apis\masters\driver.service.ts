import { inject, Injectable } from '@angular/core';
import { DataService } from '../../data.service';
import { Response } from '../../../models/x_models/api-response';
import { Driver, DriverResponse } from '../../../models/x_models/masters/driver';

@Injectable({
    providedIn: 'root'
})
export class DriverService {
    dataService = inject(DataService)

    create(data: Driver) {
        return this.dataService.post<Response>("/driver", data)
    }

    get() {
        return this.dataService.get<DriverResponse>("/driver")
    }

    getById(id: number) {
        return this.dataService.get<Driver>(`/driver/${id}`)
    }

    delete(id: number) {
        return this.dataService.delete<Response>(`/driver/${id}`)
    }
}
