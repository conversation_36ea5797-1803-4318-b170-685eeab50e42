$border-color: #878799;
$content-bg: #f9fafb;
$footer-height: 75px;
$footer-bg: $content-bg;
// $footer-color: color(#282f3a);
$action-transition-duration: 0.2s;
$action-transition-timing-function: ease;
$default-font-size: .875rem; // 14px as base font size
$default-font-weight: 400;
$default-line-height: 1.5;
$lead-font-size: 1.19rem;
$type1: 'Overpass', sans-serif;



.footer {
    color: #686868;
    padding: 10px 25px 10px;
    // border-top: 1px solid white;
    font-size: 12px !important;
    border-top: 1px solid $border-color;
    transition: all $action-transition-duration $action-transition-timing-function;
    -moz-transition: all $action-transition-duration $action-transition-timing-function;
    -webkit-transition: all $action-transition-duration $action-transition-timing-function;
    -ms-transition: all $action-transition-duration $action-transition-timing-function;
    font-size: calc(#{$default-font-size} - 0.05rem);
    font-family: $type1;
    font-weight: 400;
    margin-top: auto;
    background-color: #F9FAFB ;

    a {
        color: var(--headertheme-20);
        font-size: inherit;
    }

    i {
        font-size: 14px;
    }

    @media (max-width: 991px) {
        padding-bottom: 25px;
    }

    @media (max-width: 991px) {
        margin-left: 0;
        width: 100%;
    }
}


