import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatOptionModule, MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { MatSortModule } from '@angular/material/sort';
import { MatTableModule } from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';
interface LeaveData {
  type: string;
  granted: number;
  consumed: number;
  balance: number;
  icon: string;
  color: string;
}
@Component({
  selector: 'app-leave-balance',
  imports: [ CommonModule,
    MatTableModule,
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    MatPaginatorModule,
    MatSortModule,
    MatTooltipModule,
    MatFormFieldModule,
    MatSelectModule,
    MatOptionModule,
    MatInputModule,
    MatDatepickerModule,
    MatNativeDateModule,
    FormsModule,
    ReactiveFormsModule,
    MatProgressSpinnerModule,
    MatProgressBarModule
  ],
  templateUrl: './leave-balance.component.html',
  styleUrl: './leave-balance.component.scss'
})
export class LeaveBalanceComponent implements OnInit {
  leaveData: LeaveData[] = [
    {
      type: 'Earned Leave',
      granted: 21,
      consumed: 8,
      balance: 13,
      icon: 'work',
      color: '#4CAF50'
    },
    {
      type: 'Sick Leave',
      granted: 12,
      consumed: 3,
      balance: 9,
      icon: 'local_hospital',
      color: '#FF9800'
    },
    {
      type: 'Casual Leave',
      granted: 12,
      consumed: 5,
      balance: 7,
      icon: 'weekend',
      color: '#2196F3'
    }
  ];
  constructor() { }

  ngOnInit(): void {
  }
  getProgressPercentage(consumed: number, granted: number): number {
    return (consumed / granted) * 100;
  }

  getTotalGranted(): number {
    return this.leaveData.reduce((sum, leave) => sum + leave.granted, 0);
  }

  getTotalConsumed(): number {
    return this.leaveData.reduce((sum, leave) => sum + leave.consumed, 0);
  }

  getTotalAvailable(): number {
    return this.leaveData.reduce((sum, leave) => sum + leave.balance, 0);
  }
}
