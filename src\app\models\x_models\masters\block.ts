export interface Block {
    id: number,
    blockName: string,
    blockRegionalName: string,
    blockLgdCode: string,
    talukId: number,
    regionId?: number,
    unitId?: number
}
export interface Blocks {
    id: number,
    blockName: string,
    blockRegionalName: string,
    blockLgdCode: string,
    regionName: string,
    unitName: string,
    talukName: string
}

export type BlockResponse = Blocks[];