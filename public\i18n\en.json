{"Region": "Region", "Unit": "Unit", "Taluk": "Taluk", "Block": "Block", "Village": "Village", "Season": "Season", "Crop": "Crop", "FromDate": "From Date", "ToDate": "To Date", "Back": "Back", "Reset": "Reset", "Create": "Create", "Update": "Update", "Actions": "Actions", "Search": "Search", "Master": "Master", "View": "View", "Edit": "Edit", "Delete": "Delete", "New": "New", "InTamil": "in Tamil", "LGDCode": "LGD Code", "DeltaType": "Delta Type", "Hulling": "Hulling", "Code": "Code", "Agency": "Agency", "HullingCode": "Hulling Code", "MillName": "Mill Name", "MillAddress": "Mill Address", "MillPincode": "Mill Pincode", "MillLatitude": "Mill Latitude", "MillLongitude": "Mill Longitude", "MillLandmark": "Mill Landmark", "MillerName": "<PERSON> Name", "MillerPresentAddress": "<PERSON> Present Address", "MillType": "Mill Type", "FactoryLicenseNo": "Factory License No", "FactoryLicenseUpto": "Factory License Upto", "BoilerLicenseNo": "Boiler License No", "BoilerLicenseUpto": "Boiler License Upto", "DateofAppointment": "Date of Appointment", "AgreementValidFrom": "Agreement Valid From", "AgreementValidTo": "Agreement Valid To", "MRMType": "MRM Type", "GSTNo": "GST No", "PANNo": "PAN No", "PaddyType": "Paddy Type", "FortifiedType": "Fortified Type", "IsGeneratorAvailable?": "Is Generator Available?", "GeneratorCapacity": "Generator Capacity", "GeneratorModelNo": "Generator Model No", "IsSeparateEBMeter?": "Is Separate EB Meter?", "DPCName": "DPC Name", "PermanentAddress": "Permanent Address", "PresentAddress": "Present Address", "Pincode": "Pincode", "DPCType": "DPC Type", "POSType": "POS Type", "Latitude": "Latitude", "Longitude": "Longitude", "AuthBypassFlag": "Auth Bypass Flag", "StorageLocation": "Storage Location", "StorageType": "Storage Type", "Capacity": "Capacity", "CarpetArea": "Carpet Area", "NewProvider": "New Provider", "UpdateProvider": "Update Provider", "PrimarySIM": "Primary SIM", "PrimaryMobileNo": "Primary Mobile No", "PrimaryProvider": "Primary Provider", "PrimaryPlan": "Primary Plan", "SecondarySIM": "Secondary SIM", "SecondaryMobileNo": "Secondary Mobile No", "SecondaryProvider": "Secondary Provider", "SecondaryPlan": "Secondary Plan", "NewScheme": "New Scheme", "Scheme": "Scheme", "SchemeCode": "Scheme Code", "Description": "Description", "NewGunny": "New Gunny", "GunnyName": "<PERSON><PERSON>", "GunnyCode": "<PERSON><PERSON>", "GunnyShortCode": "<PERSON><PERSON>", "NewDevice": "New Device", "IMEINumber": "IMEI Number", "DeviceManufacturer": "Device Manufacturer", "FirmwareVersion": "Firmware Version", "Protocol": "Protocol", "Device": "<PERSON><PERSON>", "Provider": "Provider", "NewProcurementTiming": "New Procurement Timing", "NoOfBags": "No Of Bags", "NewProcurementRate": "New Procurement Rate", "Product": "Product", "Variety": "Variety", "Price": "Price", "BonusPrice": "Bonus Price", "GradeCut": "Grade Cut", "NewProcurementPriceCut": "New Procurement Price Cut", "ProcurementPriceCutType": "Procurement Price Cut Type", "MinimumRange": "Minimum Range", "MaximumRange": "Maximum Range", "PriceCutAmount": "Price Cut Amount", "NewHullingPriceCut": "New Hulling Price Cut", "HullingPriceCutType": "Hulling Price Cut Type", "DelayedDayCutType": "Delayed Day Cut Type", "NewDelayedDayCut": "New Delayed Day Cut", "DPC": "DPC", "Gunny": "<PERSON><PERSON>", "ProcurementTiming": "Procurement Timing", "Procurement Rate": "Procurement Rate", "PriceCut": "Price Cut", "HullingPriceCut": "Hulling Price Cut", "DelayedDaysCut": "Delayed Days Cut", "Vehicle": "Vehicle", "IsRequired": "is Required", "ProcurementPriceCut": "ProcurementPriceCut", "DriverName": "Driver Name", "DriverMobileNo": "Driver Mobile No", "LicenseNo": "License No", "DateOfBirth": "Date Of Birth", "Driver": "Driver", "InvalidFormat": "Invalid Format", "VehicleTracking": "Vehicle Tracking", "VehicleStatus": "Vehicle Status", "LastUpdated": "Last Updated", "PowerStatus": "Power Status", "Refresh": "Refresh", "MapView": "Switch to Map View", "GridView": "Switch to Grid View", "RegisterNo": "Register No", "VAOApproval": "VAO Approval", "FarmerId": "Farmer Id", "FarmerName": "Farmer Name", "MobileNo": "Mobile No", "ApprovedLand": "Land (in Acre(s))", "SkipPayment": "Skip Payment", "Reason": "Reason", "Enterreason": "Enter reason", "Optional": "Optional", "CompanyName": "Company Name", "AuthorizedPersonName": "Authorized Person Name", "EmailId": "Email ID", "WhatsappNo": "Whatsapp No", "VendorType": "Vendor Type", "LegalBusinessName": "Legal Business Name", "UIN": "UIN", "DateofRegistration": "Date of Registration", "StatusofGSTIN": "Status of GSTIN", "NatureofBusiness": "Nature of Business", "LandlineNumberofCompany": "Landline Number of Company", "ContactPersonName": "Contact Person Name", "Address": "Address", "FSSAI": "FSSAI", "MSME": "MSME", "AgmarkLicense": "Agmark License", "ContactEmail": "Contact Email", "OwnerName": "Owner Name", "BankAccountNumber": "Bank Account Number", "IFSCCode": "IFSC Code", "BankName": "Bank Name", "Branch": "Branch", "Commodity": "Commodity", "ContractID/WorkOrderID": "Contract ID / Work Order ID", "ContractStartDate": "Contract Start Date", "ContractEndDate": "Contract End Date", "Contract/WorkOrderDocument": "Contract / Work Order Document", "MillOwnerName": "Mill Owner Name", "MillCode": "Mill Code", "AdhaarNo": "<PERSON><PERSON>ar No", "HullingMonthlyCapacityinMTs": "Hulling Monthly Capacity in MTs", "IsGeneratorAvailable": "Is Generator Available", "IsSeparateEBAvailable": "Is Separate EB Available", "EBConsumptionNoBoiler": "EB Consumption No Boiler", "TypeofEBServiceOffice": "Type of EB Service Office", "AgreementUpto": "Agreement Upto", "SecurityDepositValue": "Security Deposit Value", "BankGuaranteeValue": "Bank Guarantee Value", "BGNumber": "BG Number", "BGValidUpTo": "BG Valid UpTo", "GenModel": "Gen Model", "EBConsumptionNoMill": "EB Consumption No Mill", "TypeofEBServiceBoiler": "Type of EB Service Boiler", "DateOfAppointment": "Date Of Appointment", "Capacity(Kwh.)": "Capacity (Kwh.)", "TypeofEBServiceMill": "Type of EB Service Mill", "EBConsumptionNoOffice": "EB Consumption No Office", "ApprovedStatus": "Approved Status", "TokenId": "Token Id", "ProductType": "Product Type", "NoofBags": "Total No of Bags", "TokenCreatedDate": "Token Created Date", "TokenNo": "Token No", "RequestedPerson": "Requested Person", "ProductName": "Product Name", "VarietyName": "Variety Name", "SeasonName": "Season Name", "SurveyNo": "Survey No", "LandType": "Land Type", "SubDivsion": "Sub Divsion", "ExpectedYield": "Expected <PERSON>eld", "BillNo": "Bill No", "Bags": "Bags", "PurchaseQuality": "Purchase Quality", "Incentive": "Incentive", "Amount": "Amount", "MoistureCut": "Moisture Cut", "DDSCut": "DDS Cut", "ISSCut": "ISS Cut", "PaymentAmount": "Payment Amount", "TripSheet": "Trip Sheet", "PlayBack": "Play Back", "ConfirmBankAccountNumber": "Confirm Bank Account Number", "QrCode": "QR Code", "AddQR": "Add QR", "ScanQR": "Scan QR", "StorageLocationFrom": "Storage Location From", "StorageLocationTo": "Storage Location To", "TotalGunny": "Total Gunny", "GunnyType": "Gunny <PERSON>", "QRCode": "QR Code", "GodownLocation": "Godown Location", "ScannedDate": "Scanned Date", "TruckMemoId": "Truck Memo Id", "TruckMemoDate": "Truck Memo Date", "TruckFrom": "Truck From", "VehicleNo": "Vehicle No", "TotalQty": "Total Qty (in Kgs.)", "Moisture": "Moisture", "StorageTypeFrom": "Storage Type From"}