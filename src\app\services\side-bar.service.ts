import { ComponentType } from '@angular/cdk/portal';
import {Injectable, signal } from '@angular/core';
import {Subject, take} from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class SideBarService {
  #sideBarEndOpenedSignal = signal<ComponentType<any> | null>(null)
  sideBarEndOpened = this.#sideBarEndOpenedSignal.asReadonly()

  #sideBarEndClosedSignal = signal<any>(null)
  sideBarEndClosed = this.#sideBarEndClosedSignal.asReadonly()

  #sideBarInputSignal = signal<any>(null)
  sideBarInput = this.#sideBarInputSignal.asReadonly()

  #sidebarHeightSignal = signal("calc(-130px - var(--sidebar-margin-top) + 100svh)")
  sidebarHeight = this.#sidebarHeightSignal.asReadonly()

  #sidebarMarginTopSignal = signal("4px")
  sidebarMarginTop = this.#sidebarMarginTopSignal.asReadonly()

  #sideBarResultSubject = new Subject<any>()
  sideBarResult$ = this.#sideBarResultSubject.asObservable().pipe(take(1))

  constructor() { }

  open<T, D = any>(component: ComponentType<T>, config?:SideBarEndConfig<D>) {
    this.#sideBarEndOpenedSignal.set(component)
    this.#sideBarInputSignal.set(config?.data)
    this.#sideBarEndClosedSignal.set(false)

    if (config?.height) {
      this.#sidebarHeightSignal.set(config.height)
    } else {
      this.#sidebarHeightSignal.set("calc(-130px - var(--sidebar-margin-top) + 100svh)")
    }

    return this.sideBarResult$
  }

  dismiss(result?: any) {
    setTimeout(() => {
      this.#sideBarEndOpenedSignal.set(null)
    }, 100)
    this.#sideBarEndClosedSignal.set(true)
    this.#sideBarResultSubject.next(result)
  }
}

export interface SideBarEndConfig<D = any> {
  data: D
  height?: string
}
