import { Component, computed, inject, input, output, resource, signal } from '@angular/core';
import { Form<PERSON><PERSON>er, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButton } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormField, MatInput, MatError } from '@angular/material/input';
import { MatOption, MatSelect } from '@angular/material/select';
import { Router } from '@angular/router';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { DateInputDirective } from '../../../../directives/date-input.directive';
import { MatNativeDateModule } from '@angular/material/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { RegionService } from '../../../../services/x_apis/masters/region.service';
import { Region } from '../../../../models/x_models/masters/region';
import { LookupService } from '../../../../services/x_apis/lookup.service';
import { AlertService } from '../../../../services/alert.service';
import { InputformatService } from '../../../../services/x_apis/inputformat.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { MatToolbarModule } from '@angular/material/toolbar';
@Component({
  selector: 'app-region',
  imports: [FormsModule,
    MatFormField,
    MatInput,
    ReactiveFormsModule,
    MatError,
    MatOption,
    MatSelect,
    MatButton,
    MatIconModule,
    MatDatepickerModule,
    DateInputDirective,
    MatNativeDateModule,
    MatToolbarModule,
    TranslateModule,
    MatFormFieldModule],
  templateUrl: './region.component.html',
  styleUrl: './region.component.scss'
})
export class RegionComponent {
  menuService = inject(MenuService);
  lookupService = inject(LookupService);
  regionService = inject(RegionService);
  alertService = inject(AlertService);
  inputFormatService = inject(InputformatService);
  translate = inject(TranslateService);

  deltas = resource({ loader: () => this.lookupService.getDelta() }).value;

  mode = input.required<string>();
  closed = output<boolean>();
  fb = inject(FormBuilder);
  editable = signal<boolean>(true);
  router = inject(Router);

  regionId = input.required<any>();
  region = signal<Region | null>(null);

  form = this.fb.group({
    id: this.fb.control<number>(0),
    regionName: this.fb.control<string>('', Validators.required),
    regionRegionalName: this.fb.control<string>('', Validators.required),
    regionLgdCode: this.fb.control<string>('', Validators.required),
    deltaTypeId: this.fb.control<number | null>(null, Validators.required),
  });

  ngOnInit() {
    if (this.mode() === "edit" || this.mode() === "view") {
      this.getFormData()
    }
  }

    readonly search = signal('');


    readonly filteredDeltas = computed(() =>
    this.deltas()?.filter(x =>
      x.value.toLowerCase().includes(this.search().toLowerCase())
    )
  );
  


  filter(value: any) {
    this.search.set(value.target.value);
  }

  async getFormData() {
    const res = await this.regionService.getById(this.regionId());
    this.region.set(res)
    this.form.patchValue({ ...res });
    if (this.mode() === "view") {
      this.editable.set(false)
      this.form.disable()
    }
  }

  onEdit() {
    this.form.enable();
    this.editable.set(true)
  }

  async onSubmit() {
    if (this.form.valid) {
      const formValue = { ...this.form.value };
      const res = await this.regionService.create(formValue as Region)
      if (res.isSuccess) {
        this.closed.emit(true)
        if (formValue.id == 0) {
          this.alertService.success(`Region ${formValue.regionName} Created successfully.`)
        }
        else {
          this.alertService.success(`Region ${formValue.regionName} Updated successfully.`)
        }
      }
    } else {
      this.form.markAllAsTouched();
    }
  }

  goBack() {
    this.closed.emit(true)
  }

  formReset() {
    let indexId = this.form.value.id;
    this.form.reset();
    this.form.patchValue({
      id: indexId
    });
  }

  onInput(event: any, regexPattern: any): void {
    this.inputFormatService.isNumericOrHyphenOrSpace(event, regexPattern);
  }

}
