<mat-toolbar color="primary" class="navbar">
    <nav aria-label="Breadcrumb navigation" style="display: flex; align-items: center; flex-wrap: wrap;">
      <a routerLink="/" class="crumb">{{'Master' | translate}}</a>
      <mat-icon>chevron_right</mat-icon>
      <a class="crumb" (click)="goBack()">{{menuService.activeMenu()?.title}}</a>
      <mat-icon>chevron_right</mat-icon>
      <span aria-current="page" class="nav-active">
        @if(uniqueId() == 0) {
        {{'New' | translate}} {{menuService.activeMenu()?.title}}
        } @else {
            {{scheme()?.schemeName}}
        }
      </span>
      @if(mode() === "view") {
      <button (click)="onEdit()" title="edit" class="btn-edit btn-icon-unstyled">
        <mat-icon class="material-symbols-rounded">edit_square</mat-icon>
      </button>
      }
    </nav>
    <button mat-flat-button color="primary" (click)="goBack()" aria-label="Go back" class="back-btn">
      <mat-icon>arrow_back_ios</mat-icon>
      Back
    </button>
  </mat-toolbar>
  
    <div class="card component">
      
        <form [formGroup]="form" (ngSubmit)="onSubmit()">
            <div class="form">


                <div class="field">
                    <label for="schemeName" class="required-label"> {{'Scheme' | translate }}</label>
                    <mat-form-field>
                        <input id="schemeName" formControlName="schemeName" matInput
                            (input)="onInput($event,'/^[a-zA-Z0-9 ]*$/')" maxlength="100">
                        <mat-error>
                            @if(form.controls.schemeName.errors?.['required']) {
                            <!-- Scheme is required -->
                            {{'Scheme' | translate}} {{'IsRequired' | translate}}
                            }
                        </mat-error>
                    </mat-form-field>
                </div>



                <div class="field">
                    <label for="schemeCode" class="required-label">{{'SchemeCode' | translate }}</label>
                    <mat-form-field>
                        <input id="schemeCode" formControlName="schemeCode" matInput
                            (input)="onInput($event,'/^[a-zA-Z0-9 ]*$/')" maxlength="100">
                        <mat-error>
                            @if(form.controls.schemeCode.errors?.['required']) {
                                <!-- Scheme code is required -->
                                {{'SchemeCode' | translate}} {{'IsRequired' | translate}}
                            }
                        </mat-error>
                    </mat-form-field>
                </div>

                
                <div class="field">
                    <label for="description" class="required-label">{{'Description' | translate }}</label>
                    <mat-form-field>
                        <input id="description" formControlName="description" matInput
                            (input)="onInput($event,'/^[a-zA-Z0-9 ]*$/')" maxlength="100">
                        <mat-error>
                            @if(form.controls.description.errors?.['required']) {
                                <!-- Description is required -->
                                {{'Description' | translate}} {{'IsRequired' | translate}}
                            }

                        </mat-error>
                    </mat-form-field>
                </div>

            </div>

            <div class="actions">
              
                @if(editable() === true) {
                @if(mode() === 'add') {
                <button type="button" mat-flat-button (click)="formReset()" class="btn btn-danger">{{'Reset' | translate}}</button>
                }
                <button [disabled]="editable() === false" mat-flat-button class="btn btn-theme"
                    type="submit">{{uniqueId() == 0
                    ? ('Create' | translate) : ('Update' | translate) }}</button>
                }
            </div>


        </form>
    </div>
