<svg width="8" height="8" viewBox="0 0 8 8" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_824_681)">
<g filter="url(#filter0_f_824_681)">
<circle cx="4" cy="4" r="3.7" stroke="#00BA00" stroke-width="0.6"/>
</g>
<circle cx="4" cy="4" r="2.84375" fill="#00BA00"/>
<g clip-path="url(#clip1_824_681)">
<path d="M3.61906 5.18692C3.58789 5.21826 3.54539 5.23576 3.50122 5.23576C3.45706 5.23576 3.41456 5.21826 3.38339 5.18692L2.44239 4.24576C2.34472 4.14809 2.34472 3.98976 2.44239 3.89226L2.56022 3.77442C2.65789 3.67676 2.81606 3.67676 2.91372 3.77442L3.50122 4.36192L5.08872 2.77442C5.18639 2.67676 5.34472 2.67676 5.44222 2.77442L5.56006 2.89226C5.65772 2.98992 5.65772 3.14826 5.56006 3.24576L3.61906 5.18692Z" fill="white"/>
</g>
</g>
<defs>
<filter id="filter0_f_824_681" x="-0.5" y="-0.5" width="9" height="9" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.25" result="effect1_foregroundBlur_824_681"/>
</filter>
<clipPath id="clip0_824_681">
<rect width="8" height="8" fill="white"/>
</clipPath>
<clipPath id="clip1_824_681">
<rect width="4" height="4" fill="white" transform="translate(2 2)"/>
</clipPath>
</defs>
</svg>
