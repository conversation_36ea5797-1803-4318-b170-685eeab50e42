import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of, BehaviorSubject } from 'rxjs';
import { map, delay } from 'rxjs/operators';
import { TransferStaticDataService } from './transfer-static-data.service';
import {
  Region,
  Department,
  Employee,
  Position,
  District,
  SubDistrict,
  Location,
  TransferRequest,
  WithinRegionTransferRequest,
  InterRegionTransferRequest,
  MutualTransferRequest,
  TransferType,
  TransferStatus,
  Priority,
  ApprovalStep
} from '../../enums/m_enums/transfer.model';

@Injectable({
  providedIn: 'root'
})
export class TransferDataService {
  private transferRequestsSubject = new BehaviorSubject<TransferRequest[]>([]);
  public transferRequests$ = this.transferRequestsSubject.asObservable();

  constructor(
    private http: HttpClient,
    private staticData: TransferStaticDataService
  ) {
    // Initialize with mock data
    this.loadMockTransferRequests();
  }

  // Master Data Methods
  getRegions(): Observable<Region[]> {
    return of(this.staticData.getRegions());
  }

  getDepartments(): Observable<Department[]> {
    return of(this.staticData.getDepartments());
  }

  getDepartmentsByRegion(regionId: string): Observable<Department[]> {
    return of(this.staticData.getDepartmentsByRegion(regionId));
  }

  getEmployees(): Observable<Employee[]> {
    return of(this.staticData.getEmployees());
  }

  getEmployeesByRegion(regionId: string): Observable<Employee[]> {
    return of(this.staticData.getEmployeesByRegion(regionId));
  }

  getPositions(): Observable<Position[]> {
    return of(this.staticData.getPositions());
  }

  getPositionsByDepartment(departmentId: string): Observable<Position[]> {
    return of(this.staticData.getPositionsByDepartment(departmentId));
  }

  getCurrentEmployee(): Observable<Employee> {
    return of(this.staticData.getCurrentEmployee());
  }

  // Geographical Data Methods
  getDistricts(): Observable<District[]> {
    return of(this.staticData.getDistricts());
  }

  getDistrictsByRegion(regionId: string): Observable<District[]> {
    return of(this.staticData.getDistrictsByRegion(regionId));
  }

  getSubDistricts(): Observable<SubDistrict[]> {
    return of(this.staticData.getSubDistricts());
  }

  getSubDistrictsByDistrict(districtId: string): Observable<SubDistrict[]> {
    return of(this.staticData.getSubDistrictsByDistrict(districtId));
  }

  getLocations(): Observable<Location[]> {
    return of(this.staticData.getLocations());
  }

  getLocationsBySubDistrict(subDistrictId: string): Observable<Location[]> {
    return of(this.staticData.getLocationsBySubDistrict(subDistrictId));
  }

  getLocationsByDistrict(districtId: string): Observable<Location[]> {
    return of(this.staticData.getLocationsByDistrict(districtId));
  }

  getLocationsByRegion(regionId: string): Observable<Location[]> {
    return of(this.staticData.getLocationsByRegion(regionId));
  }

  getTransferTypes(): Observable<{ value: string; label: string }[]> {
    return of(this.staticData.getTransferTypes());
  }

  // Transfer Request Methods
  getTransferRequests(): Observable<TransferRequest[]> {
    return this.transferRequests$;
  }

  getTransferRequestById(id: string): Observable<TransferRequest | null> {
    const requests = this.transferRequestsSubject.value;
    const request = requests.find(r => r.id === id);
    return of(request || null);
  }

  getTransferRequestsByEmployee(employeeId: string): Observable<TransferRequest[]> {
    const requests = this.transferRequestsSubject.value;
    const employeeRequests = requests.filter(r => r.employeeId === employeeId);
    return of(employeeRequests);
  }

  // Submit Transfer Requests
  submitWithinRegionTransfer(data: WithinRegionTransferRequest): Observable<{ success: boolean; requestId?: string; message?: string }> {
    console.log('Submitting Within Region Transfer:', data);

    // Simulate API call with delay
    return of({ success: true, requestId: this.generateRequestId(), message: 'Transfer request submitted successfully!' })
      .pipe(delay(1000));
  }

  submitInterRegionTransfer(data: InterRegionTransferRequest): Observable<{ success: boolean; requestId?: string; message?: string }> {
    console.log('Submitting Inter Region Transfer:', data);

    return of({ success: true, requestId: this.generateRequestId(), message: 'Inter-region transfer request submitted successfully!' })
      .pipe(delay(1000));
  }

  submitMutualTransfer(data: MutualTransferRequest): Observable<{ success: boolean; requestId?: string; message?: string }> {
    console.log('Submitting Mutual Transfer:', data);

    return of({ success: true, requestId: this.generateRequestId(), message: 'Mutual transfer request submitted successfully!' })
      .pipe(delay(1000));
  }

  // Update Transfer Request
  updateTransferRequest(id: string, data: Partial<TransferRequest>): Observable<{ success: boolean; message?: string }> {
    const requests = this.transferRequestsSubject.value;
    const index = requests.findIndex(r => r.id === id);

    if (index !== -1) {
      requests[index] = { ...requests[index], ...data, lastModifiedDate: new Date() };
      this.transferRequestsSubject.next([...requests]);
      return of({ success: true, message: 'Transfer request updated successfully!' });
    }

    return of({ success: false, message: 'Transfer request not found!' });
  }

  // Cancel Transfer Request
  cancelTransferRequest(id: string, reason: string): Observable<{ success: boolean; message?: string }> {
    return this.updateTransferRequest(id, {
      status: TransferStatus.CANCELLED,
      comments: [
        ...(this.transferRequestsSubject.value.find(r => r.id === id)?.comments || []),
        {
          id: this.generateId(),
          comment: `Request cancelled: ${reason}`,
          commentBy: 'Current User',
          commentDate: new Date(),
          isInternal: false
        }
      ]
    });
  }

  // Approval Methods
  approveTransferRequest(id: string, stepId: string, comments?: string): Observable<{ success: boolean; message?: string }> {
    const requests = this.transferRequestsSubject.value;
    const request = requests.find(r => r.id === id);

    if (request && request.approvalWorkflow) {
      const step = request.approvalWorkflow.find(s => s.id === stepId);
      if (step) {
        step.status = 'APPROVED';
        step.comments = comments;
        step.actionDate = new Date();

        // Update overall status based on approval workflow
        const allApproved = request.approvalWorkflow.filter(s => s.isRequired).every(s => s.status === 'APPROVED');
        if (allApproved) {
          request.status = TransferStatus.COMPLETED;
        } else {
          request.status = TransferStatus.UNDER_REVIEW;
        }

        this.transferRequestsSubject.next([...requests]);
        return of({ success: true, message: 'Transfer request approved successfully!' });
      }
    }

    return of({ success: false, message: 'Transfer request or approval step not found!' });
  }

  rejectTransferRequest(id: string, stepId: string, reason: string): Observable<{ success: boolean; message?: string }> {
    const requests = this.transferRequestsSubject.value;
    const request = requests.find(r => r.id === id);

    if (request && request.approvalWorkflow) {
      const step = request.approvalWorkflow.find(s => s.id === stepId);
      if (step) {
        step.status = 'REJECTED';
        step.comments = reason;
        step.actionDate = new Date();
        request.status = TransferStatus.REJECTED;

        this.transferRequestsSubject.next([...requests]);
        return of({ success: true, message: 'Transfer request rejected!' });
      }
    }

    return of({ success: false, message: 'Transfer request or approval step not found!' });
  }

  // Utility Methods
  private generateRequestId(): string {
    const prefix = 'TR';
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `${prefix}${timestamp}${random}`;
  }

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }

  private loadMockTransferRequests(): void {
    const mockRequests: TransferRequest[] = [
      {
        id: 'TR001',
        requestNumber: 'TR2024001',
        employeeId: 'E1001',
        employeeName: 'Rajesh Kumar',
        transferType: TransferType.WITHIN_REGION,
        status: TransferStatus.UNDER_REVIEW,
        priority: Priority.MEDIUM,
        currentRegionId: 'R1',
        currentRegionName: 'Chennai',
        currentDepartmentId: 'HR',
        currentDepartmentName: 'Human Resources',
        currentPosition: 'Assistant Manager',
        currentGrade: 'Grade 3',
        proposedDepartmentId: 'FIN',
        proposedDepartmentName: 'Finance',
        proposedPosition: 'Assistant Manager',
        reason: 'Career growth opportunity in finance domain',
        submittedDate: new Date('2024-01-15'),
        submittedBy: 'Rajesh Kumar',
        createdDate: new Date('2024-01-15'),
        createdBy: 'Rajesh Kumar',
        approvalWorkflow: [
          {
            id: 'AP001',
            stepNumber: 1,
            approverRole: 'RM',
            status: 'APPROVED',
            isRequired: true,
            actionDate: new Date('2024-01-16')
          },
          {
            id: 'AP002',
            stepNumber: 2,
            approverRole: 'Head Office',
            status: 'PENDING',
            isRequired: true
          }
        ]
      }
    ];

    this.transferRequestsSubject.next(mockRequests);
  }
}
