<div class="main-content">
  <div class="container-fluid">
    <div class="row">
      <div class="col-12">
        <mat-card class="table-card">
          <mat-card-header>
            <mat-card-title>
              <h2>Pending Payslip List</h2>
            </mat-card-title>
            <div class="header-actions">
              <button mat-raised-button color="primary" (click)="refreshData()" matTooltip="Refresh Data">
                <mat-icon>refresh</mat-icon>
                Refresh
              </button>
             
            </div>
          </mat-card-header>

          <mat-card-content>
            <!-- Loading Spinner -->
            <div *ngIf="isLoading" class="loading-container">
              <mat-progress-spinner mode="indeterminate" diameter="50"></mat-progress-spinner>
              <p>Loading employee data...</p>
            </div>

            <!-- Data Table -->
            <div *ngIf="!isLoading && dataSource.length > 0" class="table-container">
              <table mat-table [dataSource]="dataSource" class="mat-elevation-2 full-width-table" matSort>
                
                <!-- Serial Number Column -->
                <ng-container matColumnDef="position">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>S.No</th>
                  <td mat-cell *matCellDef="let element; let i = index">{{ i + 1 }}</td>
                </ng-container>

                <!-- Employee ID Column -->
                <ng-container matColumnDef="empId">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>Employee ID</th>
                  <td mat-cell *matCellDef="let element">{{ element.empId }}</td>
                </ng-container>

                <!-- Employee Name Column -->
                <ng-container matColumnDef="employeeName">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>Employee Name</th>
                  <td mat-cell *matCellDef="let element">{{ element.employeeName || 'N/A' }}</td>
                </ng-container>

                <!-- Department Column -->
                <ng-container matColumnDef="department">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>Department</th>
                  <td mat-cell *matCellDef="let element">{{ element.department || 'N/A' }}</td>
                </ng-container>

                <!-- Employee Role Column -->
                <ng-container matColumnDef="employeeRole">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>Role</th>
                  <td mat-cell *matCellDef="let element">{{ element.employeeRole || 'N/A' }}</td>
                </ng-container>

                <!-- Grade Column -->
                <ng-container matColumnDef="grade">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>Level/Grade</th>
                  <td mat-cell *matCellDef="let element">
                    <mat-chip-set>
                      <mat-chip [ngClass]="'grade-chip'">{{ element.grade || 'N/A' }}</mat-chip>
                    </mat-chip-set>
                  </td>
                </ng-container>

                <!-- PAN Number Column -->
                <ng-container matColumnDef="panNumber">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>PAN Number</th>
                  <td mat-cell *matCellDef="let element" class="pan-number">{{ element.panNumber || 'N/A' }}</td>
                </ng-container>

                <!-- PF Number Column -->
                <ng-container matColumnDef="pfNumber">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>PF Number</th>
                  <td mat-cell *matCellDef="let element" class="pf-number">{{ element.pfNumber || 'N/A' }}</td>
                </ng-container>

                <!-- Special Pay Type Column -->
                <ng-container matColumnDef="specialPayType">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>Special Pay Type</th>
                  <td mat-cell *matCellDef="let element">
                    <mat-chip-set *ngIf="element.specialPayType">
                      <mat-chip [ngClass]="'special-pay-chip'">{{ element.specialPayType }}</mat-chip>
                    </mat-chip-set>
                    <span *ngIf="!element.specialPayType" class="no-data">N/A</span>
                  </td>
                </ng-container>

                <!-- Actions Column -->
                <ng-container matColumnDef="view">
                  <th mat-header-cell *matHeaderCellDef>Actions</th>
                  <td mat-cell *matCellDef="let element">
                    <div class="action-buttons">
                      <button mat-raised-button 
                              color="primary" 
                              (click)="goPageChange(element.empId)"
                              matTooltip="approval Payslip"
                              class="view-button">
                        <mat-icon>visibility</mat-icon>
                        View
                      </button>
                      <button mat-icon-button 
                              color="accent" 
                              (click)="viewEmployeeDetails(element)"
                              matTooltip="Employee Details">
                        <mat-icon>person</mat-icon>
                      </button>
                    </div>
                  </td>
                </ng-container>

                <!-- Table Header and Rows -->
                <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                <tr mat-row *matRowDef="let row; columns: displayedColumns;" 
                    class="table-row" 
                    [ngClass]="{'highlight-row': row.isHighlighted}"></tr>
              </table>

              <!-- Paginator -->
              <mat-paginator 
                [pageSizeOptions]="[5, 10, 20, 50]" 
                [pageSize]="pageSize"
                [length]="count"
                showFirstLastButtons>
              </mat-paginator>
            </div>

            <!-- No Data Message -->
            <div *ngIf="!isLoading && dataSource.length === 0" class="no-data-container">
              <mat-icon class="no-data-icon">people_outline</mat-icon>
              <h3>No Employees Found</h3>
              <p>There are currently no employees to display for payroll processing.</p>
              <button mat-raised-button color="primary" (click)="refreshData()">
                <mat-icon>refresh</mat-icon>
                Refresh
              </button>
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  </div>
</div>