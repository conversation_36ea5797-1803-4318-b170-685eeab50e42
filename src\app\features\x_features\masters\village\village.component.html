<mat-toolbar color="primary" class="navbar">
    <nav aria-label="Breadcrumb navigation" style="display: flex; align-items: center; flex-wrap: wrap;">
      <a routerLink="/" class="crumb">{{'Master' | translate}}</a>
      <mat-icon>chevron_right</mat-icon>
      <a class="crumb" (click)="goBack()">{{menuService.activeMenu()?.title}}</a>
      <mat-icon>chevron_right</mat-icon>
      <span aria-current="page" class="nav-active">
        @if(uniqueId() == 0) {
        {{'New' | translate}} {{menuService.activeMenu()?.title}}
        } @else {
            {{village()?.villageName}}
        }
      </span>
      @if(mode() === "view") {
      <button (click)="onEdit()" title="edit" class="btn-edit btn-icon-unstyled">
        <mat-icon class="material-symbols-rounded">edit_square</mat-icon>
      </button>
      }
    </nav>
    <button mat-flat-button color="primary" (click)="goBack()" aria-label="Go back" class="back-btn">
      <mat-icon>arrow_back_ios</mat-icon>
      Back
    </button>
  </mat-toolbar>
  
    <div class="card component">
      
        <form [formGroup]="form" (ngSubmit)="onSubmit()">
            <div class="form">

                <div class="field">
                    <label for="Name" class="required-label">{{'Village' | translate}}</label>
                    <mat-form-field>
                        <input id="Name" formControlName="villageName" matInput
                            (input)="onInput($event,'/^[0-9A-Za-z ]*$/')" maxlength="100">
                        <mat-error>
                            @if(form.controls.villageName.errors?.['required']) {
                            <!-- Village is required -->
                            {{'Village' | translate}} {{'IsRequired' | translate}}
                            }
                        </mat-error>
                    </mat-form-field>
                </div>

                <div class="field">
                    <label for="Regionalname" class="required-label"> {{'Village' | translate}} {{'InTamil' | translate}}</label>
                    <mat-form-field>
                        <input id="Regionalname" formControlName="villageRegionalName" matInput
                            (input)="onInput($event,'/^[a-zA-Z0-9 ]*$/')" maxlength="100">
                        <mat-error>
                            @if(form.controls.villageRegionalName.errors?.['required']) {
                            <!-- Village in tamil is required -->
                            {{'Village' | translate}} {{'InTamil' | translate}} {{'IsRequired' | translate}}
                            }
                        </mat-error>
                    </mat-form-field>
                </div>

                <div class="field">
                    <label for="LgdCode" class="required-label">{{'Village' | translate}} {{'LGDCode' | translate}}</label>
                    <mat-form-field>
                        <input id="LgdCode" formControlName="villageLgdCode" matInput
                            (input)="onInput($event,'/^[0-9]*$/')" maxlength="10">
                        <mat-error>
                            @if(form.controls.villageLgdCode.errors?.['required']) {
                            <!-- Village LGD code is required -->
                            {{'Village' | translate}} {{'LGDCode' | translate}} {{'IsRequired' | translate}}
                            }
                        </mat-error>
                    </mat-form-field>
                </div>


                <div class="field">
                    <label for="RegionId" class="required-label">{{'Region' | translate}}</label>
                    <mat-form-field appearance="outline">
                        <mat-select id="RegionId" formControlName="regionId" (openedChange)="resetSearch('region')">
                           <mat-form-field class="select-search hide-subscript" appearance="outline">
                                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                                <input #search1 autocomplete="off" matInput placeholder="Search" (input)="filter($event,'region')">
                            </mat-form-field>
                            @for (region of filteredRegions(); track region) {
                            <mat-option [value]="region.key">{{region.value}}</mat-option>
                            }
                        </mat-select>
                        <mat-error>
                            @if(form.controls.regionId.errors?.['required']) {
                            <!-- Region is required -->
                            {{'Region' | translate}} {{'IsRequired' | translate}}
                            }
                        </mat-error>
                    </mat-form-field>
                </div>

                <div class="field">
                    <label for="unitId" class="required-label">{{'Unit' | translate}}</label>
                    <mat-form-field appearance="outline">
                        <mat-select id="UnitId" formControlName="unitId" (openedChange)="resetSearch('unit')">
                            <mat-form-field class="select-search hide-subscript" appearance="outline">
                                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                                <input #search2 autocomplete="off" matInput placeholder="Search" (input)="filter($event,'unit')">
                            </mat-form-field>
                            @for (unit of filteredUnites(); track unit) {
                            <mat-option [value]="unit.key">{{unit.value}}</mat-option>
                            }
                        </mat-select>
                        <mat-error>
                            @if(form.controls.unitId.errors?.['required']) {
                            <!-- Unit is required -->
                            {{'Unit' | translate}} {{'IsRequired' | translate}}
                            }
                        </mat-error>
                    </mat-form-field>
                </div>

                <div class="field">
                    <label for="talukId">{{'Taluk' | translate}}</label>
                    <mat-form-field appearance="outline">
                        <mat-select id="talukId" formControlName="talukId" (openedChange)="resetSearch('taluk')">
                            <mat-form-field class="select-search hide-subscript" appearance="outline">
                                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                                <input #search3 autocomplete="off" matInput placeholder="Search" (input)="filter($event,'taluk')">
                            </mat-form-field>
                            @for (taluk of filteredTaluks(); track taluk) {
                            <mat-option [value]="taluk.key">{{taluk.value}}</mat-option>
                            }
                        </mat-select>
                        <mat-error>
                            @if(form.controls.talukId.errors?.['required']) {
                            <!-- Taluk is required -->
                            {{'Taluk' | translate}} {{'IsRequired' | translate}}
                            }
                        </mat-error>
                    </mat-form-field>
                </div>

                <div class="field">
                    <label for="blockId">{{'Block' | translate}}</label>
                    <mat-form-field appearance="outline">
                        <mat-select id="blockId" formControlName="blockId" (openedChange)="resetSearch('block')">
                           <mat-form-field class="select-search hide-subscript" appearance="outline">
                                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                                <input #search4 autocomplete="off" matInput placeholder="Search" (input)="filter($event,'block')">
                            </mat-form-field>
                            @for (block of filteredBlocks(); track block) {
                            <mat-option [value]="block.key">{{block.value}}</mat-option>
                            }
                        </mat-select>
                        <mat-error>
                            @if(form.controls.blockId.errors?.['required']) {
                            <!-- Block is required -->
                            {{'Block' | translate}} {{'IsRequired' | translate}}
                            }
                        </mat-error>
                    </mat-form-field>
                </div>
            </div>

            <div class="actions">
               
                @if(editable() === true) {
                    @if(mode() === 'add') {
                        <button type="button" mat-flat-button (click)="formReset()" class="btn btn-danger">{{'Reset' | translate}}</button>
                        }
                <button [disabled]="editable() === false" mat-flat-button class="btn btn-theme"
                    type="submit">{{uniqueId()
                    == 0 ?
                    ('Create' | translate) : ('Update' | translate) }}</button>
                }
            </div>

        </form>
    </div>
