<mat-dialog-content class="dialog-content">
    <h2 class="dialog-title">{{ data.monthName }} 2025 Holidays</h2>
    <mat-calendar
      [selected]="null"
      [dateClass]="dateClass()"
      [startAt]="startDate">
    </mat-calendar>
    <div class="holiday-list">
      <h3>Holidays</h3>
      <div *ngFor="let holiday of data.holidays" class="holiday-item">
        <span class="holiday-date">{{ holiday.date }}</span>
        <span class="holiday-day">{{ holiday.day }}</span>
        <span class="holiday-name">{{ holiday.name }}</span>
      </div>
      <div *ngIf="!data.holidays.length" class="no-holidays">
        No public holidays
      </div>
    </div>
  </mat-dialog-content>
  <mat-dialog-actions class="dialog-actions">
    <button mat-raised-button class="close-button" (click)="closeDialog()">
      Close
    </button>
  </mat-dialog-actions>