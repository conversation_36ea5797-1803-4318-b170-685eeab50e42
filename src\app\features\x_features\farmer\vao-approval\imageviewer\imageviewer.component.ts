import { Component, computed, inject, linkedSignal, OnInit, resource, signal } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { TranslateModule } from '@ngx-translate/core';
import { LandService } from '../../../../../services/x_apis/farmer/land.service';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-imageviewer',
  imports:[TranslateModule,MatIconModule],
  templateUrl: './imageviewer.component.html',
  styleUrls: ['./imageviewer.component.scss']
})
export class ImageviewerComponent implements OnInit {

  dialogRef=inject( MatDialogRef<ImageviewerComponent>)
  data=inject(MAT_DIALOG_DATA)
  landService=inject(LandService);
  base64Image=signal<any>({});
  src=signal<string>('')
  ngOnInit() {
    this.getImageRes();
    console.log(this.data);
    console.log(this.data.detail);
    
  }

  async getImageRes()
  {
    debugger
 const res= await this.landService.getBase64(this.data.detail);
 console.log(res);
 this.base64Image.set(res);
 let img='data:image/png;base64,'+this.base64Image().value

//  this.base64Image.set()
    this.src.set(img)
  }

  back()
  {
    this.dialogRef.close()
  }

}
