import { HttpInterceptorFn } from '@angular/common/http';
import { inject } from '@angular/core';
import { SessionService } from '../services/session.service';
import { SkipToken } from '../services/data.service';
import {AuthService} from '../services/x_apis/auth.service';

export const authorizationInterceptor: HttpInterceptorFn = (req, next) => {
  const sessionService = inject(SessionService)
  const authService = inject(AuthService);

  const session = sessionService.session()
  void authService.logoutIfTokenExpired()

  if (req.context.get(SkipToken)) {
      return next(req);
  } else if (session?.accessToken) {
    req = req.clone({
        setHeaders: {
            Authorization: `Bearer ${session.accessToken}`,
        }
    });
  }

  return next(req);
};
