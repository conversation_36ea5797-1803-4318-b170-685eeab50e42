import { Component, computed, CUSTOM_ELEMENTS_SCHEMA, ElementRef, inject, Input, input, linkedSignal, output, resource, signal, ViewChild } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CommonModule, DatePipe } from '@angular/common';
import { NgxGaugeModule } from 'ngx-gauge';
import { Land, LandAttachmentViewModel, LandDetails } from '../../../../models/x_models/farmer/land';
import { LandService } from '../../../../services/x_apis/farmer/land.service';
import { ApproveStatusType } from '../../../../enums/x_enums/dotnet/ApproveStatusType.enum';
import Swal from 'sweetalert2';
import { AlertService } from '../../../../services/alert.service';
import { environment } from '../../../../../environments/environment';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { MatIconModule } from '@angular/material/icon';
import { MatDialog } from '@angular/material/dialog';
import { ImageviewerComponent } from './imageviewer/imageviewer.component';
import { MatButtonModule } from '@angular/material/button';

@Component({
  selector: 'app-land-approval',
  imports: [
    TranslateModule,
    CommonModule,
    MatIconModule,
    MatButtonModule,

  ],
  templateUrl: './land-approval.component.html',
  styleUrl: './land-approval.component.scss'
})
export class LandApprovalComponent {

  closed = output<boolean>()
  farmerId = input.required<number>()
  villageId=input.required<any>();
  farmerName=input.required<string>()
  public AproveStatus = ApproveStatusType
  menuService=inject(MenuService)
  imageViewerDialog=inject(MatDialog)
  access = computed(() => this.menuService.activeMenu()?.controlAccess);

  landService = inject(LandService)
  alertService=inject(AlertService)

  landDetailsResource = resource({ loader: () => this.landService.LandDetailsApproval(this.farmerId(),this.villageId()) })

  landApprovalDetails = linkedSignal(()=>this.landDetailsResource.value());


  ngOnInit() {

  }

  async reject(land: Land, status: number) {
    const title = 'Do You Want To Send Back?';
    const txt = 'Remarks';
    const Yes = 'Send Back';
    const No = 'Cancel';

    const swalRes = await Swal.fire({
      title,
      text: txt,
      input: 'textarea',
      inputPlaceholder: 'Remarks',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: Yes,
      cancelButtonText: No,
      customClass: {
        confirmButton: 'btn-primary',
        cancelButton: 'btn-secondary',
      },
    }).then(async (textResult) => {

      if (textResult.isConfirmed) {
        let payload = [{
          landApprovalId: land.landApprovalId,
          vaoRemarks: textResult.value,
          vaoApprovalStatusType: ApproveStatusType.Rejected
        }];

        const res = await this.landService.VaoApproval(payload);
        this.alertService.success("Send Back Successfully")
        this.landDetailsResource.reload()
      }
    })


  }

  async approve(land: Land, status: number) {
     const txt = 'Do You Want To Approve?';
    const title = 'Confirmation';
    const Yes = 'Approve';
    const No = 'Cancel';

    const swalRes = await Swal.fire({
      title,
      text: txt,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: Yes,
      cancelButtonText: No,
      customClass: {
        confirmButton: 'btn-primary',
        cancelButton: 'btn-secondary',
      },
    }).then(async (textResult) => {
      if (textResult.isConfirmed) {
    let payload = [{
      landApprovalId: land.landApprovalId,
      vaoRemarks:"",
      vaoApprovalStatusType: ApproveStatusType.Approved
    }];

    const res = await this.landService.VaoApproval(payload);
    // this.closed.emit(true);
    this.alertService.success("Approved Successfully")
    this.landDetailsResource.reload()
  }
  })

  }
  back()
  {
    this.closed.emit(true);
  }

  downloadDoc(detail:any)
  {
    debugger
    const dialogRef = this.imageViewerDialog.open(ImageviewerComponent, {
      autoFocus: false,
      disableClose: true,
      width: "60%",
      height: "85%",
      panelClass: "mat-dialog-serviceschedule",
      data: {detail:detail.landAttachmentGetViewModel?.id,survey:detail.surveyNo,sub:detail.subDivsion},
      
    });

  //    let src =
  //     environment.imageBaseUrl +
  //     "DBT/DeliveryProductImage/" +
  //     detail.attachmentPath;
  //   window.open(encodeURI(src));
  //   const downloadLink = document.createElement("a");
  //   const fileName = detail.attachmentFilename;
  //   downloadLink.href = src;
  //   downloadLink.download = fileName;
  }

}
