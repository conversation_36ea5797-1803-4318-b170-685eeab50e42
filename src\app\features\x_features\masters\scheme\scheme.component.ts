import { Component, inject, input, output, signal } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButton } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatFormField, MatInput, MatError } from '@angular/material/input';
import { MatOption, MatSelect } from '@angular/material/select';
import { SchemeService } from '../../../../services/x_apis/masters/scheme.service';
import { AlertService } from '../../../../services/alert.service';
import { InputformatService } from '../../../../services/x_apis/inputformat.service';
import { Router } from '@angular/router';
import { Scheme } from '../../../../models/x_models/masters/scheme';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { MatToolbarModule } from '@angular/material/toolbar';

@Component({
  selector: 'app-scheme',
  imports: [
    FormsModule,
    MatFormField,
    MatInput,
    ReactiveFormsModule,
    MatError,
    MatOption,
    MatSelect,
    MatButton,
    MatIconModule,
    TranslateModule,
    MatFormFieldModule,
    MatToolbarModule
  ],
  templateUrl: './scheme.component.html',
  styleUrl: './scheme.component.scss'
})
export class SchemeComponent {
  menuService = inject(MenuService);
  schemeService = inject(SchemeService)
  alertService = inject(AlertService);
  inputFormatService = inject(InputformatService);
  translate = inject(TranslateService);

  mode = input.required<string>();
  uniqueId = input.required<any>()
  closed = output<boolean>();
  router = inject(Router);
  fb = inject(FormBuilder);
  editable = signal<boolean>(true);
  scheme = signal<Scheme | null>(null);

  form = this.fb.group({
    id: [0],
    schemeCode: this.fb.control<string>('', Validators.required),
    schemeName: this.fb.control<string>('', Validators.required),
    description: this.fb.control<string>('', Validators.required),
  });


  ngOnInit() {
    if (this.mode() === "edit" || this.mode() === "view") {
      this.getFormData()
    }
  }

  async getFormData() {
    const res = await this.schemeService.getById(this.uniqueId());
    this.scheme.set(res)
    this.form.patchValue({ ...res });
    if (this.mode() === "view") {
      this.editable.set(false)
      this.form.disable()
    }
  }

  onEdit() {
    this.form.enable();
    this.editable.set(true)
  }

  async onSubmit() {
    if (this.form.valid) {
      const formValue = { ...this.form.value };
      const res = await this.schemeService.create(formValue as Scheme)
      if (res.isSuccess) {
        this.closed.emit(true)
        if (formValue.id == 0) {
          this.alertService.success(`Scheme ${formValue.schemeName} Created successfully.`)
        }
        else {
          this.alertService.success(`Scheme ${formValue.schemeName} Updated successfully.`)
        }
      }
    } else {
      this.form.markAllAsTouched();
    }
  }

  goBack() {
    this.closed.emit(true)
  }

  formReset() {
    let indexId = this.form.value.id;
    this.form.reset();
    this.form.patchValue({
      id: indexId
    });
  }

  onInput(event: any, regexPattern: any): void {
    this.inputFormatService.isNumericOrHyphenOrSpace(event, regexPattern);
  }
}
