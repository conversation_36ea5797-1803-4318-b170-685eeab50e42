.tripdetails{
    // padding: 40px 40px !important;
}
.tripstatus{
    margin: 0px 10px 20px 0px ;
}
.memotitle{
    margin-bottom: 10px;

}

.card{
    background-color: white;
    padding: 30px;
    display: flex;
    flex-direction: row;
}
.tsts{
    padding: 10px 25px;
    background: green;
    border: 1px solid #e0dede;
    font-weight: bold;
    color: white;
    border-radius: 15px;
}
.mcts{
    padding: 10px 14px;
    background: rgb(0, 119, 255);
    border: 1px solid #e0dede;
    font-weight: bold;
    color: white;
    border-radius: 15px;
}
.tets{
    padding: 10px 11px;
    background: rgb(255, 0, 0);
    border: 1px solid #e0dede;
    font-weight: bold;
    color: white;
    border-radius: 15px;
}
.bld{
    font-weight: bold;
}
.center{
    display: flex;
    justify-content: center;
}


.triplineStart{
    padding: 0px 4px 10px 15px;
    border-left: 3px solid black;
}

.triplineEnd{
    padding: 10px 4px 0px 15px;
    border-left: 3px solid black;
    align-items: flex-end;
}

.starticon{
    height: 20px ;
    width: 20px;
    background-color: black;
    padding:5px;
    margin-left: -27px;
    border-radius: 20px;
}

.smltxt{
    font-weight: 500;
    font-size: small;
    color: gray;
}