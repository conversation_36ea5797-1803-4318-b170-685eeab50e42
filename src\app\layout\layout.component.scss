// :host {
//   display: block;
//   height: 100%;
//   --mat-toolbar-standard-height: 60px;
// }

// .site {
//   height: 100%;
// }

// .site__body {
//   box-shadow: inset 0px 0px 10px 0px rgba(183, 192, 206, 0.2);
//   height: calc(100% - 43px);
//   width: 100%;
//   background-color: #F9FAFB;
//   padding: 8px 15px 0px 15px;
//   overflow-y: auto;

//   // background-color: #ffffff;
// }

// .mobile-content .site__body {
//   padding: 5px !important;
// }

// mat-toolbar {
//   border-bottom: 1px solid #e9ecef;
//   padding: 0;
// }

// mat-sidenav-content {
//   height: 100%;
// }

// .mobile-content {
//   margin-left: 0 !important;
// }
// .desktop-content {
//   margin-left: 0;
//   transition: margin-left .28s ease,
// }

// .side-nav {
//   // box-shadow: 1px 0px 3px 0px rgba(183, 192, 206, 0.2);
//   width: 240px;
//   transition: width 0s ease;
//   border-radius: 0px;
//   border-right: 1px solid #e9e9eb;
  
//   &.sidenav-collapsed {
//       transition: width .28s ease;
//       width: 60px;
      
//       &+.desktop-content {
//           margin-left: 60px;
//       }
//   }
  
//   &.sidenav-open {
//       width: 240px;
//       transition: width .28s ease;
//   }

//   &:hover {
//       width: 240px;
//   }
// }

// .site__header-desktop {
//   position: fixed;
//   z-index: 2;
// }

// mat-sidenav-container {
//   height: 100%;

//   &:has(.site__header-mobile) {
//       .site__body {
//           height: calc(100% - 98px);
//       }
//       [view-mode="xs"].site__body {
//           height: calc(100% - 60px);
//       }
//   }
// }

// .sidenav-container-desktop {
//   top: 60px;
//   height: calc(100svh - 60px);
// }
:host {
    display: block;
    height: 100%;
    --mat-toolbar-standard-height: 60px;
}

.site {
    height: 100%;
}

.site__body {
    box-shadow: inset 0px 0px 10px 0px rgba(183, 192, 206, 0.2);
    height: calc(100% - 38px);
    width: 100%;
    background-color: #F9FAFB;
    // padding: 8px 15px 0px 15px;
   
    overflow-y: auto;

    // background-color: #ffffff;
}

.mobile-content .site__body {
    padding: 5px !important;
}

mat-toolbar {
    border-bottom: 1px solid #e9ecef;
    padding: 0;
}

mat-sidenav-content {
    height: 100%;
}

.mobile-content {
    margin-left: 0 !important;
}
.desktop-content {
    margin-left: 0;
    transition: margin-left .28s ease,
}

.side-nav {
    // box-shadow: 1px 0px 3px 0px rgba(183, 192, 206, 0.2);
    width: 240px;
    transition: width 0s ease;
    border-radius: 0px;
    border-right: 1px solid #e9e9eb;
    
    &.sidenav-collapsed {
        transition: width .28s ease;
        width: 60px;
        
        &+.desktop-content {
            margin-left: 60px;
        }
    }
    
    &.sidenav-open {
        width: 240px;
        transition: width .28s ease;
    }

    &:hover {
        width: 240px;
    }
}

.site__header-desktop {
    position: fixed;
    z-index: 2;
}

mat-sidenav-container {
    height: 100%;

    &:has(.site__header-mobile) {
        .site__body {
            height: calc(100% - 98px);
        }
        [view-mode="xs"].site__body {
            height: calc(100% - 60px);
        }
    }
}

.sidenav-container-desktop {
    top: 60px;
    height: calc(100svh - 60px);
}