// Header section styling to match devices component
.header {
  padding-inline: 10px;

  @media (max-width: 700px) {
    padding-left: 0;
  }

  .filters-wrapper {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: nowrap; // Prevent wrapping to keep items in single row

    .search {
      flex: 1;
      min-width: 200px;
      max-width: 300px;
      --form-field-bg: #fff;

      .icon-search {
        width: 15px;
        height: 22px;
        padding-inline: 17px 13px;
        color: var(--theme-80);
      }
    }

    .designation-field {
      min-width: 180px;
      flex-shrink: 0; // Prevent shrinking
    }

    .date-field {
      min-width: 150px;
      flex-shrink: 0; // Prevent shrinking
    }

    .filters-more {
      position: relative;
      display: flex;
      margin-left: auto;
      flex-shrink: 0; // Prevent shrinking

      .btn-filter {
        background-color: #fff;
        border: 1.5px solid var(--grey-20);
        border-radius: 10px;
        margin-left: 15px;
        width: fit-content;
        font-size: 12px;
        display: flex;
        gap: 30px;
        --btn-icon-radius: 8px;

        span {
          z-index: 1;

          @media (max-width: 700px) {
            display: none;
          }
        }

        .icon-filter {
          display: flex;
          justify-content: center;
          align-items: center;
          color: var(--theme-80);
        }
      }
    }

    // Responsive behavior for smaller screens
    @media (max-width: 768px) {
      .search {
        min-width: 150px;
        max-width: 200px;
      }

      .designation-field {
        min-width: 140px;
      }

      .date-field {
        min-width: 120px;
      }
    }

    @media (max-width: 576px) {
      gap: 8px;

      .search {
        min-width: 120px;
        max-width: 150px;
      }

      .designation-field {
        min-width: 120px;
      }

      .date-field {
        min-width: 100px;
      }
    }
  }
}

// Hide form field subscripts
.hide-subscript ::ng-deep .mat-mdc-form-field-subscript-wrapper {
  display: none;
}

// Button styling
.btn-icon {
  color: rgb(106, 117, 135);
  display: flex;
  align-items: center;
}

.btn-icon-unstyled {
  border: none;
  padding: 0;
  background: transparent;
  width: min-content;
  height: min-content;
  line-height: 0;
  cursor: pointer;
}

.btn-delete {
  color: #f44336;
}

.btn-delete:hover {
  color: #d32f2f;
}

// Attendance Summary Cards
.attendance-summary {
  margin-bottom: 24px;

  .summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
  }

  .summary-card {
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease-in-out;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    .summary-content {
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .summary-icon {
      font-size: 32px;
      width: 32px;
      height: 32px;
    }

    .summary-text {
      flex: 1;
    }

    .summary-number {
      font-size: 24px;
      font-weight: 600;
      line-height: 1.2;
    }

    .summary-label {
      font-size: 14px;
      color: #666;
      margin-top: 4px;
    }

    &.present-card {
      border-left: 4px solid #4CAF50;
      .summary-icon {
        color: #4CAF50;
      }
      .summary-number {
        color: #4CAF50;
      }
    }

    &.absent-card {
      border-left: 4px solid #F44336;
      .summary-icon {
        color: #F44336;
      }
      .summary-number {
        color: #F44336;
      }
    }

    &.leave-card {
      border-left: 4px solid #2196F3;
      .summary-icon {
        color: #2196F3;
      }
      .summary-number {
        color: #2196F3;
      }
    }

    &.total-card {
      border-left: 4px solid #FF9800;
      .summary-icon {
        color: #FF9800;
      }
      .summary-number {
        color: #FF9800;
      }
    }
  }
}

// Status display styles
.status-display {
  display: flex;
  align-items: center;
  gap: 8px;

  .status-icon {
    font-size: 18px;
    width: 18px;
    height: 18px;
  }
}

// Table controls styling to match devices component
.table-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 3px;

  .btn-icon {
    color: rgb(106, 117, 135);
    display: flex;
    align-items: center;
    padding-left: 5px;
    padding-right: 5px;

    mat-icon {
      font-size: 16px;
    }
  }

  .btn-delete {
    color: var(--red-80);

    mat-icon {
      font-variation-settings: 'FILL' 1, 'wght' 700, 'GRAD' 200, 'opsz' 24;
    }
  }

  .btn-icon-unstyled {
    border: none;
    padding: 0;
    background: transparent;
    width: min-content;
    height: min-content;
    line-height: 0;
    cursor: pointer;

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      color: #ccc;
    }
  }
}

// Late indicator styling
.late-indicator {
  color: #FF9800;
  font-size: 12px;
  font-weight: 500;
}

// No data styling
.no-data {
  text-align: center;
  padding: 40px;
  color: #666;
  font-style: italic;
}
