import { Component, inject, input, OnInit, output, signal } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButton } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormField, MatInput, MatError } from '@angular/material/input';
import { MatOption, MatSelect } from '@angular/material/select';
import { Router } from '@angular/router';
import { SeasonService } from '../../../../services/x_apis/masters/season.service';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { DateInputDirective } from '../../../../directives/date-input.directive';
import { MatNativeDateModule } from '@angular/material/core';
import { Season } from '../../../../models/x_models/masters/season';
import { MatFormFieldModule } from '@angular/material/form-field';
import { AlertService } from '../../../../services/alert.service';
import { DatePipe } from '@angular/common';
import { InputformatService } from '../../../../services/x_apis/inputformat.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { MatToolbarModule } from '@angular/material/toolbar';

@Component({
  selector: 'app-season',
  imports: [
    FormsModule,
    MatFormField,
    MatInput,
    ReactiveFormsModule,
    MatError,
    MatOption,
    MatSelect,
    MatButton,
    MatIconModule,
    MatDatepickerModule,
    DateInputDirective,
    MatNativeDateModule,
    MatFormFieldModule,
    TranslateModule,
    MatToolbarModule
  ],
  providers: [DatePipe],
  templateUrl: './season.component.html',
  styleUrls: ['./season.component.scss']
})
export class SeasonComponent implements OnInit {
  menuService = inject(MenuService);
  seasonService = inject(SeasonService);
  alertService = inject(AlertService);
  inputFormatService = inject(InputformatService);
  translate = inject(TranslateService);

  mode = input.required<string>();
  uinqueId = input.required<any>();
  closed = output<boolean>();
  router = inject(Router);
  fb = inject(FormBuilder);
  editable = signal<boolean>(true);
  season = signal<Season | null>(null);
  minToDate = signal<Date>(new Date());

  form = this.fb.group({
    id: [0],
    seasonName: this.fb.control<string>('', Validators.required),
    fromDate: this.fb.control<string>('', Validators.required),
    toDate: this.fb.control<string>('', Validators.required)
  });

  constructor(private datePipe: DatePipe) { }

  ngOnInit() {
    this.form.get("fromDate")?.valueChanges.subscribe((res) => {
      if (res && this.editable()) {
        const parsedFromDate = new Date(res);
        parsedFromDate.setDate(parsedFromDate.getDate() + 1);
        this.minToDate.set(parsedFromDate);
        this.form.patchValue({ toDate: '' });
      } else {
        this.minToDate.set(new Date());
      }
    });
    if (this.mode() === "edit" || this.mode() === "view") {
      this.getSeason()
    }
  }

  async getSeason() {
    const res = await this.seasonService.getById(this.uinqueId());
    this.season.set(res)
    this.form.patchValue({ ...res });
    if (this.mode() === "view") {
      this.editable.set(false)
      this.form.disable()
    }
  }

  onEdit() {
    this.form.enable();
    this.editable.set(true)
  }

  async onSubmit() {
    if (this.form.valid) {
      const formValue = { ...this.form.value };
      formValue.fromDate = this.datePipe.transform(this.form.value.fromDate, 'yyyy-MM-dd');
      formValue.toDate = this.datePipe.transform(this.form.value.toDate, 'yyyy-MM-dd');
      const res = await this.seasonService.create(formValue as Season)
      if (res.isSuccess) {
        this.closed.emit(true)
        if (this.form.value.id == 0) {
          this.alertService.success(`Season ${formValue.seasonName} Created Successfully.`)
        }
        else {
          this.alertService.success(`Season ${formValue.seasonName} Updated Successfully.`)
        }
      }
    }
    else {
      this.form.markAllAsTouched();
    }
  }

  goBack() {
    this.closed.emit(true)
  }

  async formReset() {
    let indexId = this.form.value.id;
    this.form.reset();
    this.form.patchValue({
      id: indexId
    });
  }

  onInput(event: any, regexPattern: any): void {
    this.inputFormatService.isNumericOrHyphenOrSpace(event, regexPattern);
  }


  formatDateToISOString(date: any): string | null {
    if (!date) return null;
    const parsedDate = new Date(date);
    return parsedDate.toISOString();
  }

}
