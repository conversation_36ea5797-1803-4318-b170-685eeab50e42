import { Component, computed, effect, inject, linkedSignal, OnInit, resource, signal, viewChild } from '@angular/core';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { DelayeddaycutService } from '../../../../services/x_apis/masters/delayeddaycut.service';
import { Delayeddaycut, DelayedDayCuts } from '../../../../models/x_models/masters/delayeddaycut';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { Device } from '../../../../models/x_models/masters/device';
import { confirmAndDelete } from '../../../../models/x_models/masters/shared';
import { NgTemplateOutlet } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInput, MatPrefix } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { DeviceComponent } from '../device/device.component';
import { DelayeddaycutComponent } from "./delayeddaycut.component";
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatToolbarModule } from '@angular/material/toolbar';

@Component({
  selector: 'app-delayeddaycuts',
  imports: [
    MatIcon,
    MatInput,
    MatPrefix,
    ReactiveFormsModule,
    MatPaginatorModule,
    MatTableModule,
    MatButtonModule,
    MatMenuModule,
    MatSortModule,
    MatFormFieldModule,
    DelayeddaycutComponent,
    TranslateModule,
    MatToolbarModule
],
  templateUrl: './delayeddaycuts.component.html',
  styleUrls: ['./delayeddaycuts.component.scss']
})

export class DelayeddaycutsComponent implements OnInit {
  menuService = inject(MenuService);
  access = computed(() => this.menuService.activeMenu()?.controlAccess);
  translate = inject(TranslateService);

  paginator = viewChild<MatPaginator>(MatPaginator);
  sort = viewChild<MatSort>(MatSort);

  delayeddaycutService = inject(DelayeddaycutService)
  delayedDayCutResource = resource({ loader: () => this.delayeddaycutService.get() });
  dataSource = linkedSignal(() => new MatTableDataSource(this.delayedDayCutResource.value()));

  loading = signal(false);
  mode = signal<'view' | 'edit' | 'add'>('add');
  search = new FormControl("");
  showGrid = signal(false);

  delayeddaycut = signal<Delayeddaycut>({
    id: 0,
    delayedDaysTypeName: '',
    seasonName: '',
    regionName: '',
    priceCut: 0
  });

  displayedColumns: string[] = [
    'Name',
    'season',
    'region',
    'pricecut',
    'actions',
  ];
  devices = signal<DelayedDayCuts[]>([]);

  constructor() {
    effect(() => {
      if (this.paginator()) {
        this.dataSource().sort = this.sort()!;
        this.dataSource().paginator = this.paginator()!
      }
    });
  }

  ngOnInit(): void {
  }

  onAdd() {
    this.delayeddaycut.set({
      id: 0,
      delayedDaysTypeName: '',
      seasonName: '',
      regionName: '',
      priceCut: 0
    });
    this.mode.set("add")
    this.showGrid.set(true)
  }

  onView(delayeddaycut: Delayeddaycut) {
    this.delayeddaycut.set(delayeddaycut);
    this.mode.set('view');
    this.showGrid.set(true)
  }

  onEdit(delayeddaycut: Delayeddaycut) {
    this.delayeddaycut.set(delayeddaycut);
    this.mode.set('edit');
    this.showGrid.set(true)
  }

  onClose() {
    this.showGrid.set(false)
    this.delayedDayCutResource.reload();
    this.search.setValue("");
  }

  onRefresh() {
    this.delayedDayCutResource.reload();
    this.search.setValue("");
  }

  async onDelete(delayeddaycut: Delayeddaycut) {
    await confirmAndDelete(delayeddaycut, delayeddaycut.delayedDaysTypeName, 'DelayedDayCut', this.delayeddaycutService, () => this.delayedDayCutResource.reload());
  }

  onSearch() {
    const filterValue = this.search.value?.trim().toLowerCase() || '';
    this.dataSource().filter = filterValue;
  }

}