import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class InputformatService {

constructor() { }

isNumericOrHyphenOrSpace(event: any, regexPattern: string) {
  const inputValue = event.target.value;
  if (!regexPattern) {
    return;
  }
  const regex = new RegExp(regexPattern);
  if (!regex.test(inputValue)) {
    switch (regexPattern) {
      case "/^[0-9.]*$/":
        return event.target.value = inputValue.replace(/[^0-9.]/g, '');//accept number and dot

      case "/^[0-9]*$/":
        return event.target.value = inputValue.replace(/[^0-9]/g, '');//accept only numbers

      case "/^[0-9- ]*$/":
        return event.target.value = inputValue.replace(/[^0-9- ]/g, '');//accept only numbers, dashes, and spaces

      case "/^[0-9,]*$/":
        return event.target.value = inputValue.replace(/[^0-9,]/g, '');//accept  only numbers and commas
        
      case "/^[0-9A-Za-z]*$/":
        return event.target.value = inputValue.replace(/[^0-9A-Za-z]/g, '');//accept  only alphanumeric characters

        case "/^[0-9A-Za-z ]*$/":
          return event.target.value = inputValue.replace(/[^0-9A-Za-z ]/g, '');//accept  only alphanumeric characters and numbers

      case "/^[a-zA-Z ]*$/":
        return event.target.value = inputValue.replace(/[^a-zA-Z \u0B80-\u0BFF]/g, '');//accept only English letters, Tamil letters, and spaces

      case "/^[a-zA-Z0-9 ]*$/":
        return event.target.value = inputValue.replace(/[^a-zA-Z0-9 \u0B80-\u0BFF]/g, '');//accept only alphanumeric English characters, Tamil characters, and spaces 

      case "/^[a-zA-Z0-9a ]*$/":
          return event.target.value = inputValue.replace(/[^a-zA-Z0-9 ]/g, '');// accept only English alphanumeric characters and spaces

      case "/^[A-Z0-9]*$/":
        return event.target.value = inputValue.replace(/[^A-Z0-9]/g, '');// accept keeps only uppercase letters and numbers,

      case "/^[.,/()a-zA-Z -]*$/":
        return event.target.value = inputValue.replace(/[^.,/()a-zA-Z -]/g, '');// accept keeps English letters, punctuation marks (.,/()), spaces, and hyphens

      case "/^[^\u0B80-\u0BFF ]*$/":
        return event.target.value = inputValue.replace(/[^\u0B80-\u0BFF ]/g, '');//accept only Tamil characters and spaces,
        
      case "/^[a-zA-Z\s]*$/":
        return event.target.value = inputValue.replace(/[^a-zA-Z\s]/g, '');//accept only English alphabetic characters and spaces
        case "/^[,a-zA-Z0-9\s -]*$/":
          return event.target.value = inputValue.replace(/[^,a-zA-Z0-9\s -]/g, '');//accept only English alphabetic characters and spaces , -
      default:
        return event.target.value;
    }
  }
}
}
