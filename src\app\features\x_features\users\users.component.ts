import { NgT<PERSON>plateOutlet, DatePipe, CurrencyPipe } from '@angular/common';
import { Component, computed, inject, OnInit, signal, viewChild } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckbox, MatCheckboxChange } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInput, MatPrefix } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginator, MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { MatOption, MatSelect } from '@angular/material/select';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { CloseFilterOnBlurDirective } from '../../../directives/close-filter-on-blur.directive';
import { AppRoutes } from '../../../enums/app-routes';
import { handleSelectAll, handleSelect } from '../../../helpers/helpers';
import { SideBarService } from '../../../services/side-bar.service';
import { UpdateUserStatusPayload, User } from '../../../models/x_models/user';
import { UserService } from '../../../services/x_apis/user.service';
import { MenuService } from '../../../services/x_apis/menu.service';
import Swal from 'sweetalert2';
import { StatusType } from '../../../enums/x_enums/dotnet/status-type';
import { UserComponent } from "../user/user.component";

@Component({
  selector: 'app-users',
  imports: [
    MatIcon,
    MatInput,
    MatPrefix,
    ReactiveFormsModule,
    MatCheckbox,
    MatPaginatorModule,
    MatTableModule,
    NgTemplateOutlet,
    MatButtonModule,
    MatMenuModule,
    DatePipe,
    MatSortModule,
    MatOption,
    MatSelect,
    CloseFilterOnBlurDirective, CurrencyPipe, MatFormFieldModule, UserComponent],
  templateUrl: './users.component.html',
  styleUrl: './users.component.scss'
})
export class UsersComponent implements OnInit {
  sidebarService = inject(SideBarService)
  menuService = inject(MenuService)
  userService = inject(UserService)
  search = new FormControl("")
  user = signal<User>({
    id: 0,
    fullName: '',
    email: '',
    statusType: 0,
    mobileNo: '',
    roleName: '',
  });

  paginator = viewChild<MatPaginator>(MatPaginator)
  sort = viewChild<MatSort>(MatSort)
  dataSource = signal(new MatTableDataSource<User>([]))
  displayedColumns: string[] = [
    'fullName',
    'roleName',
    'email',
    'mobileNo',
    'statusType',
    'actions',
  ];
  users = signal<User[]>([])
  loading = signal(false)
  access = computed(() => this.menuService.activeMenu()?.controlAccess);
  mode = signal<'view' | 'edit' | 'add'>('add');
  showUser = signal(false)
  status = StatusType;

  protected readonly AppRoutes = AppRoutes;

  ngOnInit(): void {
    this.getUsers()
  }

  async getUsers() {
    const res = await this.userService.get();
    this.users.set(res);
    this.dataSource.set(new MatTableDataSource(this.users()))
    setTimeout(() => {
      this.dataSource().sort = this.sort()!;
      this.dataSource().paginator = this.paginator()!
    }, 100)
  }
 

  onSearch() {

  }

  async onDeactivateUser(user: User) {
    const title = 'Deactivation Confirmation';
    const txt = 'You Want To Deactivate User';
    const Yes = 'yes';
    const No = 'No';

    const swalRes = await Swal.fire({
      title,
      text: txt,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: Yes,
      cancelButtonText: No,
      customClass: {
        confirmButton: 'btn-primary',
        cancelButton: 'btn-secondary',
      },
    });

    if (swalRes.isConfirmed) {
      let payload: UpdateUserStatusPayload = {
        id: user.id,
        statusType: StatusType.Inactive,
      };
  
      const res = await this.userService.update(payload);
      if (res.isSuccess) {
        this.getUsers();
      }
    }
  }

  async onActivateUser(user: User) {
    const title = 'Activation Confirmation';
    const txt = 'You Want To Activate User';
    const Yes = 'yes';
    const No = 'No';

    const swalRes = await Swal.fire({
      title,
      text: txt,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: Yes,
      cancelButtonText: No,
      customClass: {
        confirmButton: 'btn-primary',
        cancelButton: 'btn-secondary',
      },
    });

    if (swalRes.isConfirmed) {
      let payload: UpdateUserStatusPayload = {
        id: user.id,
        statusType: StatusType.Active,
      };
  
      const res = await this.userService.update(payload);
      if (res.isSuccess) {
        this.getUsers();
      }
    }
  }

  onAddUser() {
    this.user.set({
      id: 0,
      fullName: "",
      email: "",
      statusType: 0,
      mobileNo: "",
      roleName: "",
    });
    this.mode.set("add")
    this.showUser.set(true)
  }

  onViewUser(user: User) {
    this.user.set(user);
    this.mode.set('view');
    this.showUser.set(true)
  }

  onEditUser(user: User) {
    this.user.set(user);
    this.mode.set('edit');
    this.showUser.set(true)
  }

  onUserClose() {
    this.showUser.set(false)
    this.getUsers();
  }

}
