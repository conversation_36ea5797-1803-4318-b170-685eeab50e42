import { NgT<PERSON>plateOutlet, DatePipe, CurrencyPipe } from '@angular/common';
import { Component, computed, effect, inject, linkedSignal, resource, signal, viewChild } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckbox } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInput, MatPrefix } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatOption, MatSelect } from '@angular/material/select';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CloseFilterOnBlurDirective } from '../../../../directives/close-filter-on-blur.directive';
import { VehicleComponent } from './vehicle.component';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { VehicleService } from '../../../../services/x_apis/masters/vehicle.service';
import { StatusType } from '../../../../enums/x_enums/dotnet/status-type';
import { Vehicles } from '../../../../models/x_models/masters/vehicle';
import { confirmAndDelete } from '../../../../models/x_models/masters/shared';
import { MatToolbarModule } from '@angular/material/toolbar';

@Component({
  selector: 'app-vehicles',
  imports: [
    MatIcon,
    MatInput,
    MatPrefix,
    ReactiveFormsModule,
    MatCheckbox,
    MatPaginatorModule,
    MatTableModule,
    NgTemplateOutlet,
    MatButtonModule,
    MatMenuModule,
    DatePipe,
    MatSortModule,
    MatOption,
    MatSelect,
    CloseFilterOnBlurDirective,
    CurrencyPipe,
    MatFormFieldModule,
    VehicleComponent,
    TranslateModule,
    MatToolbarModule
  ],
  templateUrl: './vehicles.component.html',
  styleUrl: './vehicles.component.scss'
})
export class VehiclesComponent {
  menuService = inject(MenuService);
  access = computed(() => this.menuService.activeMenu()?.controlAccess);

  paginator = viewChild<MatPaginator>(MatPaginator);
  sort = viewChild<MatSort>(MatSort);

  translate = inject(TranslateService);
  vehicleService = inject(VehicleService);
  vehicleResource = resource({ loader: () => this.vehicleService.get() });
  dataSource = linkedSignal(() => new MatTableDataSource(this.vehicleResource.value()));

  loading = signal(false);
  mode = signal<'view' | 'edit' | 'add'>('add');
  search = new FormControl("");
  showGrid = signal(false);
  status = StatusType;

  list = signal<Vehicles>({
    id: 0,
    regNo: "",
    ownerName: "",
    ownerMobileNo: "",
    vendorName: ""
  });

  displayedColumns: string[] = [
    'regNo',
    'ownerName',
    'ownerMobileNo',
    'vendorName',
    'actions'
  ];


  constructor() {
    effect(() => {
      if (this.paginator()) {
        this.dataSource().sort = this.sort()!;
        this.dataSource().paginator = this.paginator()!
      }
    });
  }

  ngOnInit(): void {
  }

  onAdd() {
    this.list.set({
      id: 0,
      regNo: "",
      ownerName: "",
      ownerMobileNo: "",
      vendorName: ''
    });
    this.mode.set("add")
    this.showGrid.set(true)
  }

  onView(rowData: Vehicles) {
    this.list.set(rowData);
    this.mode.set('view');
    this.showGrid.set(true)
  }

  onEdit(rowData: Vehicles) {
    this.list.set(rowData);
    this.mode.set('edit');
    this.showGrid.set(true)
  }

  onClose() {
    this.showGrid.set(false)
    this.vehicleResource.reload()
    this.search.setValue("");
  }

  onRefresh() {
    this.vehicleResource.reload()
    this.search.setValue("");
  }

  async onDelete(rowData: Vehicles) {
    await confirmAndDelete(rowData, rowData.regNo, 'Vehicle', this.vehicleService, () => this.vehicleResource.reload());
  }

  onSearch() {
    const filterValue = this.search.value?.trim().toLowerCase() || '';
    this.dataSource().filter = filterValue;
  }
}
