import { inject, Injectable } from '@angular/core';
import { ProcurePricecut, ProcurePricecutResponse } from '../../../models/x_models/masters/procure-pricecut';
import { DataService } from '../../data.service';
import { Response } from '../../../models/x_models/api-response';
import { ProcurementRateById, ProcurementRates } from '../../../models/x_models/masters/procurement-rate';

@Injectable({
  providedIn: 'root'
})
export class ProcurementRateService {

 dataService = inject(DataService)

  create(data: any) {
    return this.dataService.post<Response>("/procurementrate", data)
  }

  get() {
    return this.dataService.get<ProcurementRates>("/procurementrate")
  }

  getById(id: number) {
    return this.dataService.get<ProcurementRateById>(`/procurementrate/${id}`)
  }

  delete(id: number) {
    return this.dataService.delete<Response>(`/procurementrate/${id}`)
  }

}
