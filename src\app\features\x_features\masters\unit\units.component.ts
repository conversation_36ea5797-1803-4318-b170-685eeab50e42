import { NgTemplateOutlet, DatePipe } from '@angular/common';
import { Component, computed, effect, inject, linkedSignal, resource, signal, viewChild } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckbox } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInput, MatPrefix } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatOption, MatSelect } from '@angular/material/select';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { CloseFilterOnBlurDirective } from '../../../../directives/close-filter-on-blur.directive';
import { UnitComponent } from './unit.component';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { UnitService } from '../../../../services/x_apis/masters/unit.sevice';
import { Units } from '../../../../models/x_models/masters/unit';
import { StatusType } from '../../../../enums/x_enums/dotnet/status-type';
import { AppRoutes } from '../../../../enums/app-routes';
import { confirmAndDelete } from '../../../../models/x_models/masters/shared';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatToolbarModule } from '@angular/material/toolbar';

@Component({
  selector: 'app-units',
  imports: [
    MatIcon,
    MatInput,
    MatPrefix,
    ReactiveFormsModule,
    MatCheckbox,
    MatPaginatorModule,
    MatTableModule,
    NgTemplateOutlet,
    MatButtonModule,
    MatMenuModule,
    DatePipe,
    MatSortModule,
    MatOption,
    MatSelect,
    CloseFilterOnBlurDirective,
    MatFormFieldModule,
     TranslateModule,
    UnitComponent,
    MatToolbarModule
  ],
  templateUrl: './units.component.html',
  styleUrl: './units.component.scss'
})
export class UnitsComponent {
  menuService = inject(MenuService);
  access = computed(() => this.menuService.activeMenu()?.controlAccess);
  translate = inject(TranslateService);

  paginator = viewChild<MatPaginator>(MatPaginator);
  sort = viewChild<MatSort>(MatSort);

  unitService = inject(UnitService);
  unitResource = resource({ loader: () => this.unitService.get() });
  dataSource = linkedSignal(() => new MatTableDataSource(this.unitResource.value()));

  loading = signal(false);
  mode = signal<'view' | 'edit' | 'add'>('add');
  search = new FormControl("");
  showGrid = signal(false);
  status = StatusType;

  unit = signal<Units>({
    id: 0,
    unitName: '',
    unitRegionalName: '',
    regionName: '',
  });

  displayedColumns: string[] = [
    'unitName',
    'unitRegionalName',
    'regionName',
    'actions',
  ];

  units = signal<Units[]>([])
  protected readonly AppRoutes = AppRoutes;

  constructor() {
    effect(() => {
        if (this.paginator()) {
            this.dataSource().sort = this.sort()!;
            this.dataSource().paginator = this.paginator()!
        }
    });
}

  ngOnInit(): void {
  }

  onAdd() {
    this.unit.set({
      id: 0,
      unitName: '',
      unitRegionalName: '',
      regionName: '',
    });
    this.mode.set("add")
    this.showGrid.set(true)
  }

  onView(unit: Units) {
    this.unit.set(unit);
    this.mode.set('view');
    this.showGrid.set(true)
  }

  onEdit(unit: Units) {
    this.unit.set(unit);
    this.mode.set('edit');
    this.showGrid.set(true)
  }

  onRefresh() {
    this.unitResource.reload()
    this.search.setValue("");
  }

  onClose() {
    this.showGrid.set(false)
    this.unitResource.reload()
    this.search.setValue("");
  }

  async onDelete(unit: Units) {
    await confirmAndDelete(unit, unit.unitName, 'Unit', this.unitService, () =>  this.unitResource.reload());
  }

  onSearch() {
    const filterValue = this.search.value?.trim().toLowerCase() || '';
    this.dataSource().filter = filterValue;
  }

}
