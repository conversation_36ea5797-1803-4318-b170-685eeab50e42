import { CommonModule } from '@angular/common';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { RouterModule } from '@angular/router';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { Subject, takeUntil } from 'rxjs';
import {
  TransferRequest,
  TransferType,
  TransferStatus,
  Priority,
  ApprovalStep
} from '../../../enums/m_enums/transfer.model';
import { TransferDataService } from '../../../services/m_apis/transfer-data.service';
import { MatSnackBar } from '@angular/material/snack-bar';

interface TransferStatistics {
  pending: number;
  approved: number;
  rejected: number;
  completed: number;
}

@Component({
  selector: 'app-transfer-dashboard',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    MatCardModule,
    MatIconModule,
    MatMenuModule,
    MatButtonModule,
    MatFormFieldModule,
    MatSelectModule,
    MatInputModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    RouterModule
  ],
  templateUrl: './transfer-dashboard.component.html',
  styleUrl: './transfer-dashboard.component.scss'
})
export class TransferDashboardComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  transferRequests: TransferRequest[] = [];
  filteredRequests: TransferRequest[] = [];
  statistics: TransferStatistics = {
    pending: 0,
    approved: 0,
    rejected: 0,
    completed: 0
  };

  // Filter properties
  searchTerm: string = '';
  selectedStatus: string = '';
  selectedType: string = '';
  selectedPriority: string = '';

  isLoading = false;

  constructor(
    private transferService: TransferDataService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.loadTransferRequests();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadTransferRequests(): void {
    this.isLoading = true;

    this.transferService.getTransferRequests()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (requests) => {
          this.transferRequests = requests;
          this.filteredRequests = [...requests];
          this.calculateStatistics();
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error loading transfer requests:', error);
          this.snackBar.open('Error loading transfer requests', 'Close', { duration: 3000 });
          this.isLoading = false;
        }
      });
  }

  private calculateStatistics(): void {
    this.statistics = {
      pending: this.transferRequests.filter(r =>
        r.status === TransferStatus.SUBMITTED ||
        r.status === TransferStatus.UNDER_REVIEW ||
        r.status === TransferStatus.APPROVED_BY_SUPERVISOR ||
        r.status === TransferStatus.APPROVED_BY_HR
      ).length,
      approved: this.transferRequests.filter(r =>
        r.status === TransferStatus.APPROVED_BY_MANAGEMENT
      ).length,
      rejected: this.transferRequests.filter(r =>
        r.status === TransferStatus.REJECTED
      ).length,
      completed: this.transferRequests.filter(r =>
        r.status === TransferStatus.COMPLETED
      ).length
    };
  }

  onSearch(): void {
    this.applyFilters();
  }

  onStatusFilter(): void {
    this.applyFilters();
  }

  onTypeFilter(): void {
    this.applyFilters();
  }

  onPriorityFilter(): void {
    this.applyFilters();
  }

  private applyFilters(): void {
    this.filteredRequests = this.transferRequests.filter(request => {
      const matchesSearch = !this.searchTerm ||
        request.requestNumber.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        request.employeeName.toLowerCase().includes(this.searchTerm.toLowerCase());

      const matchesStatus = !this.selectedStatus || request.status === this.selectedStatus;
      const matchesType = !this.selectedType || request.transferType === this.selectedType;
      const matchesPriority = !this.selectedPriority || request.priority === this.selectedPriority;

      return matchesSearch && matchesStatus && matchesType && matchesPriority;
    });
  }

  getTransferTypeIcon(type: TransferType): string {
    switch (type) {
      case TransferType.WITHIN_REGION:
        return 'swap_horiz';
      case TransferType.INTER_REGION:
        return 'flight_takeoff';
      case TransferType.MUTUAL:
        return 'swap_horizontal_circle';
      default:
        return 'transfer_within_a_station';
    }
  }

  getTransferTypeLabel(type: TransferType): string {
    switch (type) {
      case TransferType.WITHIN_REGION:
        return 'Within Region';
      case TransferType.INTER_REGION:
        return 'Inter Region';
      case TransferType.MUTUAL:
        return 'Mutual Transfer';
      default:
        return 'Transfer';
    }
  }

  getStatusClass(status: TransferStatus): string {
    switch (status) {
      case TransferStatus.DRAFT:
        return 'draft';
      case TransferStatus.SUBMITTED:
      case TransferStatus.UNDER_REVIEW:
        return 'pending';
      case TransferStatus.APPROVED_BY_SUPERVISOR:
      case TransferStatus.APPROVED_BY_HR:
      case TransferStatus.APPROVED_BY_MANAGEMENT:
        return 'approved';
      case TransferStatus.COMPLETED:
        return 'completed';
      case TransferStatus.REJECTED:
        return 'rejected';
      case TransferStatus.CANCELLED:
        return 'cancelled';
      default:
        return 'pending';
    }
  }

  getStatusLabel(status: TransferStatus): string {
    switch (status) {
      case TransferStatus.DRAFT:
        return 'Draft';
      case TransferStatus.SUBMITTED:
        return 'Submitted';
      case TransferStatus.UNDER_REVIEW:
        return 'Under Review';
      case TransferStatus.APPROVED_BY_SUPERVISOR:
        return 'Approved by Supervisor';
      case TransferStatus.APPROVED_BY_HR:
        return 'Approved by HR';
      case TransferStatus.APPROVED_BY_MANAGEMENT:
        return 'Approved by Management';
      case TransferStatus.COMPLETED:
        return 'Completed';
      case TransferStatus.REJECTED:
        return 'Rejected';
      case TransferStatus.CANCELLED:
        return 'Cancelled';
      default:
        return status;
    }
  }

  getPriorityClass(priority: Priority): string {
    switch (priority) {
      case Priority.LOW:
        return 'low';
      case Priority.MEDIUM:
        return 'medium';
      case Priority.HIGH:
        return 'high';
      case Priority.URGENT:
        return 'urgent';
      default:
        return 'medium';
    }
  }

  getStepClass(status: string): string {
    switch (status) {
      case 'APPROVED':
        return 'approved';
      case 'REJECTED':
        return 'rejected';
      case 'PENDING':
        return 'pending';
      default:
        return 'pending';
    }
  }

  canEdit(request: TransferRequest): boolean {
    return request.status === TransferStatus.DRAFT || request.status === TransferStatus.SUBMITTED;
  }

  canCancel(request: TransferRequest): boolean {
    return request.status !== TransferStatus.COMPLETED &&
           request.status !== TransferStatus.REJECTED &&
           request.status !== TransferStatus.CANCELLED;
  }

  hasApprovalWorkflow(request: TransferRequest): boolean {
    return !!(request.approvalWorkflow && request.approvalWorkflow.length > 0);
  }

  viewRequest(request: TransferRequest): void {
    // Navigate to request details view
    console.log('Viewing request:', request);
    this.snackBar.open('Request details view not implemented yet', 'Close', { duration: 3000 });
  }

  editRequest(request: TransferRequest): void {
    // Navigate to edit form based on transfer type
    console.log('Editing request:', request);
    this.snackBar.open('Request editing not implemented yet', 'Close', { duration: 3000 });
  }

  cancelRequest(request: TransferRequest): void {
    const reason = prompt('Please provide a reason for cancellation:');
    if (reason) {
      this.transferService.cancelTransferRequest(request.id, reason).subscribe({
        next: (response) => {
          if (response.success) {
            this.snackBar.open(response.message || 'Request cancelled successfully', 'Close', { duration: 3000 });
            this.loadTransferRequests(); // Reload the list
          } else {
            this.snackBar.open(response.message || 'Failed to cancel request', 'Close', { duration: 3000 });
          }
        },
        error: (error) => {
          console.error('Error cancelling request:', error);
          this.snackBar.open('Error cancelling request', 'Close', { duration: 3000 });
        }
      });
    }
  }

  downloadRequest(request: TransferRequest): void {
    // Generate and download PDF
    console.log('Downloading request:', request);
    this.snackBar.open('PDF download not implemented yet', 'Close', { duration: 3000 });
  }

  trackProgress(request: TransferRequest): void {
    // Show detailed progress tracking
    console.log('Tracking progress for request:', request);
    this.snackBar.open('Progress tracking view not implemented yet', 'Close', { duration: 3000 });
  }
}
