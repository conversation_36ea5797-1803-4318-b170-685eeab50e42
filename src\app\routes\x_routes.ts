import { Routes } from '@angular/router';

export const X_ROUTES: Routes = [
  {
    path: 'dashboard',
    loadComponent: () =>
      import('../features/x_features/dashboard/dashboard.component')
        .then((m) => m.DashboardComponent),
  },
  {
    path: "report",
    loadComponent: () =>
      import("../features/x_features/report/report.component")
        .then((m) => m.ReportComponent)
  },
  {
    path: "form",
    loadComponent: () =>
      import("../features/x_features/form-components/form-components.component")
        .then((m) => m.FormComponentsComponent)
  },
  {
    path: "grid",
    loadComponent: () =>
      import("../features/x_features/grid/grid.component")
        .then((m) => m.GridComponent)
  },
  {
    path: "user",
    loadComponent: () =>
      import("../features/x_features/user/user.component")
        .then((m) => m.UserComponent)
  },
  {
    path: "users",
    loadComponent: () =>
      import("../features/x_features/users/users.component")
        .then((m) => m.UsersComponent)
  },
  {
    path: "season",
    loadComponent: () =>
      import("../features/x_features/masters/season/seasons.component")
        .then((m) => m.SeasonsComponent)
  },
  {
    path: "crops",
    loadComponent: () =>
      import("../features/x_features/masters/crop/crops.component")
        .then((m) => m.CropsComponent)
  },
  {
    path: "regions",
    loadComponent: () =>
      import("../features/x_features/masters/region/regions.component")
        .then((m) => m.RegionsComponent)
  },
  {
    path: "units",
    loadComponent: () =>
      import("../features/x_features/masters/unit/units.component")
        .then((m) => m.UnitsComponent)
  },
  {
    path: "blocks",
    loadComponent: () =>
      import("../features/x_features/masters/block/blocks.component")
        .then((m) => m.BlocksComponent)
  },
  {
    path: "taluks",
    loadComponent: () =>
      import("../features/x_features/masters/taluk/taluks.component")
        .then((m) => m.TaluksComponent)
  },
  {
    path: "villages",
    loadComponent: () =>
      import("../features/x_features/masters/village/villages.component")
        .then((m) => m.VillagesComponent)
  },
  {
    path: "hullings",
    loadComponent: () =>
      import("../features/x_features/masters/hulling/hullings.component")
        .then((m) => m.HullingsComponent)
  },
  {
    path: "dpcs",
    loadComponent: () =>
      import("../features/x_features/masters/dpc/dpcs.component")
        .then((m) => m.DpcsComponent)
  },
  {
    path: "storagelocations",
    loadComponent: () =>
      import("../features/x_features/masters/storage-location/storage-locations.component")
        .then((m) => m.StorageLocationsComponent)
  },
  {
    path: "providers",
    loadComponent: () =>
      import("../features/x_features/masters/provider/providers.component")
        .then((m) => m.ProvidersComponent)
  },
  {
    path: "devices",
    loadComponent: () =>
      import("../features/x_features/masters/device/devices.component")
        .then((m) => m.DevicesComponent)
  },
  {
    path: "schemes",
    loadComponent: () =>
      import("../features/x_features/masters/scheme/schemes.component")
        .then((m) => m.SchemesComponent)
  },
  {
    path: "gunnytypes",
    loadComponent: () =>
      import("../features/x_features/masters/gunnytype/gunnytypes.component")
        .then((m) => m.GunnytypesComponent)
  },
  {
    path: "gunnys",
    loadComponent: () =>
      import("../features/x_features/masters/gunny/gunnylist.component")
        .then((m) => m.GunnylistComponent)
  },
  {
    path: "procurementtimings",
    loadComponent: () =>
      import("../features/x_features/masters/procure-timing/procure-timings.component")
        .then((m) => m.ProcureTimingsComponent)
  },
  {
    path: "pricecuts",
    loadComponent: () =>
      import("../features/x_features/masters/procure-pricecut/procure-pricecuts.component")
        .then((m) => m.ProcurePricecutsComponent)
  },
  {
    path: "hullingPricecuts",
    loadComponent: () =>
      import("../features/x_features/masters/hulling-pricecut/hulling-pricecuts.component")
        .then((m) => m.HullingPricecutsComponent)
  },
  {
    path: "procurementrates",
    loadComponent: () =>
      import("../features/x_features/masters/procurement-rate/procurement-rates.component")
        .then((m) => m.ProcurementRatesComponent)
  },
  {
    path: "delayeddayscuts",
    loadComponent: () =>
      import("../features/x_features/masters/delayeddaycut/delayeddaycuts.component")
        .then((m) => m.DelayeddaycutsComponent)
  },
  {
    path: "vehicles",
    loadComponent: () =>
      import("../features/x_features/masters/vehicle/vehicles.component")
        .then((m) => m.VehiclesComponent)
  },
  {
    path: "drivers",
    loadComponent: () =>
      import("../features/x_features/masters/driver/drivers.component")
        .then((m) => m.DriversComponent)
  },
  {
    path: "livetrack",
    loadComponent: () =>
      import("../features/x_features/tracking/livetrack/livetrack.component")
        .then((m) => m.LivetrackComponent)
  },
  {
    path: "tripsheet",
    loadComponent: () =>
      import("../features/x_features/tracking/tripsheet/tripsheets.component")
        .then((m) => m.TripsheetsComponent)
  },
  {
    path: "playback",
    loadComponent: () =>
      import("../features/x_features/tracking/playback/playback-form.component")
        .then((m) => m.PlaybackFormComponent)
  },
  {
    path: "vaoapproval",
    loadComponent: () =>
      import("../features/x_features/farmer/vao-approval/land-details.component")
        .then((m) => m.LandDetailsComponent)
  },
  {
    path: "paymentpproval",
    loadComponent: () =>
      import("../features/x_features/procurement/procureapproval/procureapproval.component")
        .then((m) => m.ProcureapprovalComponent)
  },
   {
    path: "procurementdetails",
    loadComponent: () =>
      import("../features/x_features/procurement/purchase/purchase.component")
        .then((m) => m.PurchaseComponent)
  },
   {
    path: "vendorregistration",
    loadComponent: () =>
      import("../features/x_features/vendor/register/registers.component")
        .then((m) => m.RegistersComponent)
  },
   {
    path: "tokendetails",
    loadComponent: () =>
      import("../features/x_features/procurement/procuretoken/procuretoken.component")
        .then((m) => m.ProcuretokenComponent)
  },
  {
    path: "gunnymovement",
    loadComponent: () =>
      import("../features/x_features/gunny/gunny-movement/gunny-movements.component")
        .then((m) => m.GunnyMovementsComponent)
  },
  {
    path: "supplier",
    loadComponent: () =>
      import("../features/x_features/vendor/onboard/supplier/supplier.component")
        .then((m) => m.SupplierComponent)
  },
  {
    path: "contractor",
    loadComponent: () =>
      import("../features/x_features/vendor/onboard/supplier/supplier.component")
        .then((m) => m.SupplierComponent)
  },
  {
    path: "dpcvillagemapping",
    loadComponent: () =>
      import("../features/x_features/masters/dpcvillagemapping/dpcvillagemappings.component")
        .then((m) => m.DpcvillagemappingsComponent)
  },
  // {
  //   path: "truckmemoack",
  //   loadComponent: () =>
  //     import("../features/x_features/acknowledge/truck/trucklist.component")
  //       .then((m) => m.TrucklistComponent)
  // },
  {
    path: "truckmemoack",
    loadComponent: () =>
      import("../features/x_features/acknowledge/paddy/paddylist.component")
        .then((m) => m.PaddylistComponent)
  },
  {
    path: "map",
    loadComponent: () =>
      import("../features/x_features/Demo/route-optimization/route-optimization.component")
        .then((m) => m.RouteOptimizationComponent)
  }

]
