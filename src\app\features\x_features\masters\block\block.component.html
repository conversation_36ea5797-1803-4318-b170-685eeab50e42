<mat-toolbar color="primary" class="navbar">
    <nav aria-label="Breadcrumb navigation" style="display: flex; align-items: center; flex-wrap: wrap;">
      <a routerLink="/" class="crumb">{{'Master' | translate}}</a>
      <mat-icon>chevron_right</mat-icon>
      <a class="crumb" (click)="goBack()">{{menuService.activeMenu()?.title}}</a>
      <mat-icon>chevron_right</mat-icon>
      <span aria-current="page" class="nav-active">
        @if(blockId() == 0) {
        {{'New' | translate}} {{menuService.activeMenu()?.title}}
        } @else {
        {{block()?.blockName}}
        }
      </span>
      @if(mode() === "view") {
      <button (click)="onEdit()" title="edit" class="btn-edit btn-icon-unstyled">
        <mat-icon class="material-symbols-rounded">edit_square</mat-icon>
      </button>
      }
    </nav>
    <button mat-flat-button color="primary" (click)="goBack()" aria-label="Go back" class="back-btn">
      <mat-icon>arrow_back_ios</mat-icon>
      Back
    </button>
  </mat-toolbar>

<div class="card component">

    <form [formGroup]="form" (ngSubmit)="onSubmit()">
        <div class="form">

            <div class="field">
                <label for="blockName" class="required-label">{{'Block' | translate}}</label>
                <mat-form-field>
                    <input id="blockName" formControlName="blockName" matInput
                        (input)="onInput($event,'/^[0-9A-Za-z ]*$/')" maxlength="100">
                    <mat-error>
                        @if(form.controls.blockName.errors?.['required']) {
                        {{'Block' | translate}} {{'IsRequired' | translate}}
                        }
                    </mat-error>
                </mat-form-field>
            </div>

            <div class="field">
                <label for="blockRegionalName" class="required-label">{{'Block' | translate}} {{'InTamil' |
                    translate}}</label>
                <mat-form-field>
                    <input id="blockRegionalName" formControlName="blockRegionalName" matInput
                        (input)="onInput($event,'/^[a-zA-Z0-9 ]*$/')" maxlength="100">
                    <mat-error>
                        @if(form.controls.blockRegionalName.errors?.['required']) {
                        <!-- Block in tamil is required -->
                        {{'Block' | translate}} {{'InTamil' | translate}} {{'IsRequired' | translate}}
                        }
                    </mat-error>
                </mat-form-field>
            </div>

            <div class="field">
                <label for="blockLgdCode" class="required-label">{{'Block' | translate}} {{'LGDCode' |
                    translate}}</label>
                <mat-form-field>
                    <input id="blockLgdCode" formControlName="blockLgdCode" matInput
                        (input)="onInput($event,'/^[0-9]*$/')" maxlength="10">
                    <mat-error>
                        @if(form.controls.blockLgdCode.errors?.['required']) {
                        {{'Block' | translate}} {{'LGDCode' | translate}} {{'IsRequired' | translate}}
                        }
                    </mat-error>
                </mat-form-field>
            </div>

            <div class="field">
                <label for="regionId" class="required-label">{{'Region' | translate}}</label>
                <mat-form-field appearance="outline">
                    <mat-select id="regionId" formControlName="regionId" (selectionChange)="getTaluk()">
                        <mat-form-field class="select-search hide-subscript" appearance="outline">
                            <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                            <input #search autocomplete="off" matInput placeholder="Search" (input)="filter($event)">
                        </mat-form-field>
                        @for (role of filteredRegions(); track role) {
                        <mat-option [value]="role.key">{{role.value}}</mat-option>
                        }
                    </mat-select>
                    <mat-error>
                        @if(form.controls.regionId.errors?.['required']) {
                        {{'Region' | translate}} {{'IsRequired' | translate}}
                        }
                    </mat-error>
                </mat-form-field>
            </div>

            <div class="field">
                <label for="talukId" class="required-label">{{'Unit' | translate}}</label>
                <mat-form-field appearance="outline">
                    <mat-select id="unitId" formControlName="unitId" (selectionChange)="getTaluk()">
                        <mat-form-field class="select-search hide-subscript" appearance="outline">
                            <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                            <input #search autocomplete="off" matInput placeholder="Search" (input)="filter($event)">
                        </mat-form-field>
                        @for (role of filteredUnites(); track role) {
                        <mat-option [value]="role.key">{{role.value}}</mat-option>
                        }
                    </mat-select>
                    <mat-error>
                        @if(form.controls.unitId.errors?.['required']) {
                        {{'Unit' | translate}} {{'IsRequired' | translate}}
                        }
                    </mat-error>
                </mat-form-field>
            </div>

            @if (form.value.regionId && form.value.unitId){
            <div class="field">
                <label for="talukId" class="required-label">{{'Taluk' | translate}}</label>
                <mat-form-field appearance="outline">
                    <mat-select id="talukId" formControlName="talukId">
                        <mat-form-field class="select-search hide-subscript" appearance="outline">
                            <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                            <input #search autocomplete="off" matInput placeholder="Search" (input)="filter($event)">
                        </mat-form-field>
                        @for (role of filteredTaluks(); track role) {
                        <mat-option [value]="role.key">{{role.value}}</mat-option>
                        }
                    </mat-select>
                    <mat-error>
                        @if(form.controls.talukId.errors?.['required']) {
                        {{'Taluk' | translate}} {{'IsRequired' | translate}}
                        }
                    </mat-error>
                </mat-form-field>
            </div>
            }

        </div>

        <div class="actions">

            @if(editable() === true) {
            @if(mode() === 'add') {
            <button type="button" mat-flat-button (click)="formReset()" class="btn btn-danger">{{'Reset' |
                translate}}</button>
            }
            <button [disabled]="editable() === false" mat-flat-button class="btn btn-theme" type="submit">{{blockId()
                == 0
                ? ('Create' | translate) : ('Update' | translate)}}</button>
            }
        </div>

    </form>
</div>