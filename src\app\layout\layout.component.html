<!-- @if(viewMode()!='lg')
{
<div class="site" [style.--sidebar-margin-top]="sidebarMarginTop()">
  <mat-sidenav-container [class.sidenav-container-desktop]="viewMode() === 'lg'">
    <mat-sidenav #snav
      [disableClose]="viewMode() === 'lg'"
      [mode]="viewMode() !== 'lg' ? 'over' : 'side'"
      [opened]="viewMode() === 'lg'" class="side-nav" (closed)="this.viewModeService.closeSideNav()">
      <app-sidebar (submenuSelected)="onSubmenuSelected()"/>
    </mat-sidenav>


    <mat-sidenav-content (focusin)="setSideNavFocused(false)" tabindex="-1"
      [ngClass]="{'mobile-content': viewMode() !== 'lg', 'desktop-content': viewMode() === 'lg'}"
      [style.margin-left]="'230px'">
      <app-header />

      <div [attr.view-mode]="viewMode()" class="site__body">
        <router-outlet />
     

      </div>

    </mat-sidenav-content>

  

    <mat-sidenav autoFocus="false" #snavEnd disableClose="false" [mode]="'over'" position="end" [opened]="false" class="side-nav-end" [style.--sidebar-height]="sidebarHeight()" (closed)="sideBarEndSerivce.dismiss()">
      <ng-container *ngComponentOutlet="sideBarEndSerivce.sideBarEndOpened(); inputs:sideBarInputs()"></ng-container>
    </mat-sidenav>

  </mat-sidenav-container>

</div>
}
@else
{
<div class="site">
  @if (viewMode() === "lg") {
      <mat-toolbar class="site__header-desktop">
          <app-header (sideNavToggled)="onSideNavToggle()" />
      </mat-toolbar>
  }

  <mat-sidenav-container [class.sidenav-container-desktop]="viewMode() === 'lg'">
      <mat-sidenav #snav [mode]="viewMode() !== 'lg' ? 'over' : 'side'" [opened]="viewMode() === 'lg' ? true : false"
          class="side-nav"
          [ngClass]="{'sidenav-collapsed': sideNavCollapsed(), 'sidenav-open': sideNavCollapsed() === false}"
          (mouseenter)="setSideNavFocused(true)" (mouseleave)="setSideNavFocused(false)">
          <app-sidebar  />
      </mat-sidenav>

      <mat-sidenav-content [ngClass]="{'mobile-content': viewMode() !== 'lg', 'desktop-content': viewMode() === 'lg'}"
          [style.margin-left]="sideNavCollapsed() ? '60px' : '240px'">

          @if(viewMode() !== "lg") {
              <mat-toolbar class="site__header-mobile">
                  <app-header (sideNavToggled)="onSideNavToggle()" />
              </mat-toolbar>
          }

          <div [attr.view-mode]="viewMode()" class="site__body">
              <router-outlet />
          </div>

          @if(viewMode() !== 'xs') {
              <app-footer />
          }
      </mat-sidenav-content>
  </mat-sidenav-container>
</div>
} -->

<div class="site">
    @if (viewMode() === "lg") {
        <mat-toolbar class="site__header-desktop">
            <app-header (sideNavToggled)="onSideNavToggle()" />
        </mat-toolbar>
    }

    <mat-sidenav-container [class.sidenav-container-desktop]="viewMode() === 'lg'">
        <mat-sidenav #snav [mode]="viewMode() !== 'lg' ? 'over' : 'side'" [opened]="viewMode() === 'lg' ? true : false"
            class="side-nav"
            [ngClass]="{'sidenav-collapsed': sideNavCollapsed(), 'sidenav-open': sideNavCollapsed() === false}"
            (mouseenter)="setSideNavFocused(true)" (mouseleave)="setSideNavFocused(false)">
            <app-sidebar (menuSelected)="onMenuSelected($event)" [focused]="sideNavFocused()" [collapsed]="this.sideNavCollapsed()"
                (sideNavToggled)="onSideNavToggle()" />
        </mat-sidenav>

        <mat-sidenav-content [ngClass]="{'mobile-content': viewMode() !== 'lg', 'desktop-content': viewMode() === 'lg'}"
            [style.margin-left]="sideNavCollapsed() ? '60px' : '240px'">

            @if(viewMode() !== "lg") {
                <mat-toolbar class="site__header-mobile">
                    <app-header (sideNavToggled)="onSideNavToggle()" />
                </mat-toolbar>
            }

            <div [attr.view-mode]="viewMode()" class="site__body">
                <router-outlet />
            </div>

            @if(viewMode() !== 'xs') {
                <app-footer />
            }
        </mat-sidenav-content>
    </mat-sidenav-container>
</div>
