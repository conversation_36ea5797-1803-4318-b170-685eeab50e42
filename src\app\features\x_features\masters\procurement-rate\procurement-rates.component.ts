import { DatePipe } from '@angular/common';
import { Component, computed, effect, inject, linkedSignal, OnInit, resource, signal, viewChild } from '@angular/core';
import { ReactiveFormsModule, FormControl } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInput, MatPrefix } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginatorModule, MatPaginator } from '@angular/material/paginator';
import { MatSortModule, MatSort } from '@angular/material/sort';
import { MatTableModule, MatTableDataSource } from '@angular/material/table';
import { StatusType } from '../../../../enums/x_enums/dotnet/status-type';
import { HullingPricecuts } from '../../../../models/x_models/masters/hulling-pricecut';
import { confirmAndDelete } from '../../../../models/x_models/masters/shared';
import { HullingPricecutService } from '../../../../services/x_apis/masters/hulling-pricecut.service';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { ProcurementRateComponent } from './procurement-rate.component';
import { ProcurementRateService } from '../../../../services/x_apis/masters/procurement-rate.service';
import { ProcurementRate } from '../../../../models/x_models/masters/procurement-rate';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatToolbarModule } from '@angular/material/toolbar';

@Component({
  selector: 'app-procurement-rates',
  imports: [MatIcon,
    MatIcon,
    MatInput,
    MatPrefix,
    ReactiveFormsModule,
    MatPaginatorModule,
    MatTableModule,
    MatButtonModule,
    MatMenuModule,
    DatePipe,
    MatToolbarModule,
    MatSortModule,
    TranslateModule,
    MatFormFieldModule,
    ProcurementRateComponent],
  templateUrl: './procurement-rates.component.html',
  styleUrls: ['./procurement-rates.component.scss']
})

export class ProcurementRatesComponent {
  menuService = inject(MenuService);
  access = computed(() => this.menuService.activeMenu()?.controlAccess);
  translate = inject(TranslateService);

  paginator = viewChild<MatPaginator>(MatPaginator);
  sort = viewChild<MatSort>(MatSort);

  procurementRateService = inject(ProcurementRateService);
  procurementRateResource = resource({ loader: () => this.procurementRateService.get() });
  dataSource = linkedSignal(() => new MatTableDataSource(this.procurementRateResource.value()));

  loading = signal(false);
  mode = signal<'view' | 'edit' | 'add'>('add');
  search = new FormControl("");
  showGrid = signal(false);
  status = StatusType;

  list = signal<ProcurementRate>({
    id: 0,
    price: 0,
    bonusPrice: 0,
    fromDate: "",
    toDate: "",
    productName: "",
    varietyName: "",
    gradeCut: 0,
    seasonName: ""
  });

  displayedColumns: string[] = [
    'productName',
    'varietyName',
    'seasonName',
    'gradeCut',
    'price',
    'bonusPrice',
    'fromDate',
    'toDate',
    'actions'
  ];

  constructor() {
    effect(() => {
      if (this.paginator()) {
        this.dataSource().sort = this.sort()!;
        this.dataSource().paginator = this.paginator()!
      }
    });
  }

  ngOnInit(): void {
  }

  onAdd() {
    this.list.set({
      id: 0,
      price: 0,
      bonusPrice: 0,
      fromDate: "",
      toDate: "",
      productName: "",
      varietyName: "",
      gradeCut: 0,
      seasonName: ""
    });
    this.mode.set("add")
    this.showGrid.set(true)
  }

  onView(rowData: ProcurementRate) {
    this.list.set(rowData);
    this.mode.set('view');
    this.showGrid.set(true)
  }

  onEdit(rowData: ProcurementRate) {
    this.list.set(rowData);
    this.mode.set('edit');
    this.showGrid.set(true)
  }

  onClose() {
    this.showGrid.set(false)
    this.procurementRateResource.reload()
    this.search.setValue("");
  }

  onRefresh() {
    this.procurementRateResource.reload()
    this.search.setValue("");
  }

  async onDelete(rowData: ProcurementRate) {
    await confirmAndDelete(rowData, rowData.productName, 'Procurement Rate', this.procurementRateService, () => this.procurementRateResource.reload());
  }

  onSearch() {
    const filterValue = this.search.value?.trim().toLowerCase() || '';
    this.dataSource().filter = filterValue;
  }

}