.dialog-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 16px;
  
    .dialog-title {
      font-size: 1.5rem;
      font-weight: 500;
      color: #333;
      margin-bottom: 16px;
    }
  
    mat-calendar {
      width: 100%;
      max-width: 300px;
      margin-bottom: 16px;
  
      .holiday-date {
        background-color: #11074e;
        color: white;
        border-radius: 50%;
      }
    }
  
    .holiday-list {
      width: 100%;
      max-width: 300px;
      text-align: left;
  
      h3 {
        font-size: 1.1rem;
        color: #333;
        margin-bottom: 8px;
      }
  
      .holiday-item {
        display: grid;
        grid-template-columns: 40px 40px 1fr;
        gap: 12px;
        padding: 8px 0;
        border-bottom: 1px solid #eee;
        font-size: 0.875rem;
        color: #333;
  
        &:last-child {
          border-bottom: none;
        }
  
        .holiday-date {
          font-weight: 500;
        }
  
        .holiday-day {
          color: #666;
        }
  
        .holiday-name {
          font-weight: 400;
        }
      }
  
      .no-holidays {
        font-size: 0.875rem;
        color: #666;
        padding: 16px 0;
      }
    }
  }
  
  .dialog-actions {
    display: flex;
    justify-content: flex-end;
    padding: 8px 16px;
  
    .close-button {
      display: flex;
      align-items: center;
      gap: 8px;
      background-color: #11074e;
      color: white;
      padding: 8px 16px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      transition: background-color 0.2s ease, box-shadow 0.2s ease;
  
      &:hover {
        background-color: #1a0c7a; // Slightly lighter shade of #11074e
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
      }
  
      mat-icon {
        font-size: 1rem;
        width: 1rem;
        height: 1rem;
      }
    }
  }
  
  @media (max-width: 400px) {
    .dialog-content {
      padding: 12px;
  
      .dialog-title {
        font-size: 1.3rem;
      }
  
      mat-calendar {
        max-width: 250px;
      }
  
      .holiday-list {
        max-width: 250px;
  
        .holiday-item {
          font-size: 0.75rem;
          grid-template-columns: 30px 30px 1fr;
          gap: 8px;
        }
      }
    }
  
    .dialog-actions {
      padding: 6px 12px;
  
      .close-button {
        padding: 6px 12px;
        font-size: 0.875rem;
  
        mat-icon {
          font-size: 0.875rem;
          width: 0.875rem;
          height: 0.875rem;
        }
      }
    }
  }