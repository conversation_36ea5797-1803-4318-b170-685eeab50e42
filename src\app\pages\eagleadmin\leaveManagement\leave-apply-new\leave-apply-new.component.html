<div class="leave-form-container">
    <!-- Header Section -->
    <mat-card class="header-card">
      <mat-card-header class="form-header">
        <div class="header-content">
          <div class="logo-section">
            <mat-icon class="tncsc-logo">account_balance</mat-icon>
          </div>
          <div class="title-section">
            <!-- <h1 class="form-title">Tamil Nadu Civil Supplies Corporation</h1> -->
            <h2 class="form-subtitle">Leave Application Form</h2>
          </div>
        </div>
      </mat-card-header>
    </mat-card>
  
    <!-- Main Form Card -->
    <mat-card class="form-card">
      <form [formGroup]="leaveForm" (ngSubmit)="onSubmit()" class="leave-form">
        
        <!-- Employee Information Section -->
        <div class="form-section">
          <h3 class="section-title">
            <mat-icon>person</mat-icon>
            Leave Details
          </h3>
          
          <div class="form-row">
            <!-- Leave Type -->
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Leave Type</mat-label>
              <mat-select formControlName="leaveType" required>
                <mat-option value="">Select Leave Type</mat-option>
                <mat-option *ngFor="let type of leaveTypes" [value]="type.leaveTypeId">
                  {{ type.leaveTypeName }}
                </mat-option>
              </mat-select>
              <mat-icon matSuffix>arrow_drop_down</mat-icon>
              <mat-error *ngIf="leaveForm.get('leaveType')?.hasError('required')">
                Leave type is required
              </mat-error>
            </mat-form-field>
          </div>
  
          <!-- Date Selection Row -->
          <div class="form-row two-columns">
            <!-- From Date -->
            <div class="date-container">
                <mat-form-field appearance="outline" class="form-field highlight-date tight-spacing">
                    <mat-label>From Date</mat-label>
                    <input matInput [matDatepicker]="fromPicker" formControlName="fromDate" required (dateChange)="updateDaysCalculation()">
                    <mat-datepicker-toggle matSuffix [for]="fromPicker"></mat-datepicker-toggle>
                    <mat-datepicker #fromPicker></mat-datepicker>
                    <mat-error *ngIf="leaveForm.get('fromDate')?.hasError('required')">From date is required</mat-error>
                  </mat-form-field>
              
              <!-- From Session -->
              <mat-form-field appearance="outline" class="full-width session-field">
                <mat-label>Session</mat-label>
                <mat-select formControlName="fromSession">
                  <mat-option *ngFor="let session of sessions" [value]="session.value">
                    {{ session.label }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>
  
            <!-- To Date -->
            <div class="date-container">
                <mat-form-field appearance="outline" class="form-field highlight-date tight-spacing">
                    <mat-label>To Date</mat-label>
                    <input matInput [matDatepicker]="toPicker" formControlName="toDate" required (dateChange)="updateDaysCalculation()">
                    <mat-datepicker-toggle matSuffix [for]="toPicker"></mat-datepicker-toggle>
                    <mat-datepicker #toPicker></mat-datepicker>
                    <mat-error *ngIf="leaveForm.get('toDate')?.hasError('required')">To date is required</mat-error>
                  </mat-form-field>
              
              <!-- To Session -->
              <mat-form-field appearance="outline" class="full-width session-field">
                <mat-label>Session</mat-label>
                <mat-select formControlName="toSession">
                  <mat-option *ngFor="let session of sessions" [value]="session.value">
                    {{ session.label }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>
          </div>
  
          <!-- Leave Duration Display -->
          <div class="leave-duration" *ngIf="calculatedDays !== null">
            <mat-chip-listbox>
              <mat-chip-option selected>
                <mat-icon>schedule</mat-icon>
                Total Leave Days: {{ calculatedDays }} {{ calculatedDays === 0.5 ? 'day' : 'days' }}
              </mat-chip-option>
            </mat-chip-listbox>
          </div>
        </div>
  
        <!-- Approval Section -->
        <div class="form-section">
          <h3 class="section-title">
            <mat-icon>supervisor_account</mat-icon>
            Approval Details
          </h3>
          
          <div class="form-row">
            <!-- Reporting Manager -->
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Applying To (Reporting Manager)</mat-label>
              <mat-select formControlName="approver" required>
                <mat-option value="">Select Reporting Manager</mat-option>
                <mat-option *ngFor="let person of approvers" [value]="person.id">{{person.name}}</mat-option>
              </mat-select>
              

              <mat-icon matSuffix>person</mat-icon>
              <mat-error *ngIf="leaveForm.get('approver')?.hasError('required')">
                Approver selection is required
              </mat-error>

            </mat-form-field>
            <span class="selected-approver" style="margin-left: 10px;padding-top: 10px;"  *ngIf="leaveForm.get('approver')?.value">{{ getApprovername(leaveForm.get('approver')?.value) }}</span>

          </div>
        </div>
  
        <!-- Reason Section -->
        <div class="form-section">
          <h3 class="section-title">
            <mat-icon>description</mat-icon>
            Leave Reason
          </h3>
          
          <div class="form-row">
            <!-- Reason -->
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Reason for Leave</mat-label>
              <textarea matInput 
                        formControlName="reason" 
                        rows="4" 
                        placeholder="Please provide a detailed reason for your leave application..."
                        required></textarea>
              <mat-hint>Minimum 10 characters required</mat-hint>
              <mat-error *ngIf="leaveForm.get('reason')?.hasError('required')">
                Reason is required
              </mat-error>
              <mat-error *ngIf="leaveForm.get('reason')?.hasError('minlength')">
                Reason must be at least 10 characters long
              </mat-error>
            </mat-form-field>
          </div>
        </div>
  
        <!-- Attachments Section -->
        <div class="form-section">
          <h3 class="section-title">
            <mat-icon>attach_file</mat-icon>
            Supporting Documents
          </h3>
          
          <div class="form-row">
            <!-- File Upload -->
            <div class="file-upload-container">
              <input type="file" 
                     #fileInput 
                     multiple 
                     accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                     (change)="onFileSelected($event)"
                     style="display: none;">
              
              <button type="button" 
                      mat-stroked-button 
                      color="primary" 
                      class="upload-button"
                      (click)="fileInput.click()">
                <mat-icon>cloud_upload</mat-icon>
                Choose Files
              </button>
              
              <span class="file-info">
                Supported formats: PDF, DOC, DOCX, JPG, PNG (Max 5MB each)
              </span>
            </div>
  
            <!-- Selected Files Display -->
            <div class="selected-files-container" *ngIf="selectedFiles.length > 0">
              <h4>Selected Files:</h4>
              <mat-chip-listbox>
                <mat-chip-option *ngFor="let file of selectedFiles; let i = index" 
                                 class="file-chip">
                  <mat-icon>description</mat-icon>
                  {{ file.name }}
                  <button type="button" 
                          matChipRemove 
                          (click)="removeFile(i)"
                          aria-label="Remove file">
                    <mat-icon>cancel</mat-icon>
                  </button>
                </mat-chip-option>
              </mat-chip-listbox>
            </div>
          </div>
        </div>
  
        <!-- Action Buttons -->
        <div class="form-actions">
          <button type="button" 
                  mat-stroked-button 
                  color="warn" 
                  class="action-button"
                  (click)="onReset()">
            <mat-icon>refresh</mat-icon>
            Reset Form
          </button>
          
          <button type="submit" 
                  mat-raised-button 
                  color="primary" 
                  class="action-button submit-button"
                  [disabled]="!leaveForm.valid">
            <mat-icon>send</mat-icon>
            Submit Application
          </button>
        </div>
      </form>
    </mat-card>
  
    <!-- Footer -->
    <div class="form-footer">
      <p>
        <mat-icon>info</mat-icon>
        Please ensure all information is accurate before submitting. 
        Your application will be reviewed by your reporting manager.
      </p>
    </div>
  </div>