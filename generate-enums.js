const axios = require('axios');
const fs = require('fs');
const path = require('path');

const apiBaseUrl = "https://dev.api.tncscerp.co.za/api/lookup/";
const directory = "src/app/enums/dotnet";

// ["endpoint", "enum-name"]
const apiEndpoints = [ "proofType", "fileType" ]

async function fetchEnumValues(endpoint) {
  try {
    const response = await axios.get(apiBaseUrl + endpoint);
    return response.data
  } catch (error) {
    console.error('Error fetching enum values:', error);
    process.exit(1);
  }
}

function generateTypeScriptEnum(enumName, enumValues) {
  let parts = enumName.split("-")
  enumName = ""
  parts.forEach(part => {
    enumName += part[0].toUpperCase() + part.slice(1)
  })

  let enumString = `export enum ${enumName} {\n`;

  enumValues.forEach(item => {
    if (typeof item.value === "number" || !item.value || item.value.split("/").length > 0) {
      enumString += `    "${item.value.split(" ").join("").split("-").join("")}" = ${item.key},\n`
    } else {
      enumString += `    ${item.value.split(" ").join("").split("-").join("")} = ${item.key},\n`
    }
  })

  enumString += '}\n';

  return enumString;
}

function deleteFilesInDirectory() {
  if (fs.existsSync(directory)) {
    const files = fs.readdirSync(directory);

    files.forEach((file) => {
      const filePath = path.join(directory, file);

      if (fs.statSync(filePath).isFile()) {
        fs.unlinkSync(filePath);
        console.log(`Deleted: ${filePath}`);
      }
    });
  } else {
    console.log(`Directory does not exist: ${directory}`);
  }
}

function writeEnumToFile(fileName, enumString) {
  const filePath = path.join(__dirname, `${directory}/${fileName}.ts`);
  fs.writeFileSync(filePath, enumString, 'utf-8');
  console.log(`Enum ${fileName} generated successfully at ${filePath}`);
}

function dasherize(str) {
  return str.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase();
}

async function generateEnum() {
  deleteFilesInDirectory()

  apiEndpoints.forEach(endpoint => {
    (async () => {
      const enums = await fetchEnumValues(endpoint);
      const fileName = dasherize(endpoint);

      const enumString = generateTypeScriptEnum(fileName, enums);
      writeEnumToFile(fileName, enumString);

    })()
  })
}

generateEnum();
