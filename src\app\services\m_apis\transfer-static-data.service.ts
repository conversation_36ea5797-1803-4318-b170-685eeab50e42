
import { Injectable } from '@angular/core';
import { Region, Department, Employee, Position, District, SubDistrict, Location } from '../../enums/m_enums/transfer.model';

@Injectable({
  providedIn: 'root'
})
export class TransferStaticDataService {

  getRegions(): Region[] {
    return [
      { id: 'R1', name: 'Chennai', code: 'CHN', description: 'Chennai Regional Office' },
      { id: 'R2', name: 'Coimbatore', code: 'CBE', description: 'Coimbatore Regional Office' },
      { id: 'R3', name: 'Madurai', code: 'MDU', description: 'Madurai Regional Office' },
      { id: 'R4', name: 'Salem', code: 'SLM', description: 'Salem Regional Office' },
      { id: 'R5', name: 'Trichy', code: 'TRY', description: 'Trichy Regional Office' }
    ];
  }

  getDepartments(): Department[] {
    return [
      { id: 'HR', name: 'Human Resources', code: 'HR', description: 'Human Resources Department' },
      { id: 'FIN', name: 'Finance', code: 'FIN', description: 'Finance Department' },
      { id: 'OPS', name: 'Operations', code: 'OPS', description: 'Operations Department' },
      { id: 'IT', name: 'Information Technology', code: 'IT', description: 'IT Department' },
      { id: 'ADMIN', name: 'Administration', code: 'ADM', description: 'Administration Department' },
      { id: 'PROC', name: 'Procurement', code: 'PROC', description: 'Procurement Department' },
      { id: 'QUAL', name: 'Quality Assurance', code: 'QA', description: 'Quality Assurance Department' }
    ];
  }

  getDepartmentsByRegion(regionId: string): Department[] {
    // In a real application, this would filter based on region
    return this.getDepartments();
  }

  getEmployees(): Employee[] {
    return [
      {
        id: 'E1001',
        name: 'Rajesh Kumar',
        position: 'Assistant Manager',
        region: 'R1',
        department: 'HR',
        employeeCode: 'EMP001',
        email: '<EMAIL>',
        mobileNumber: '9876543210',
        joiningDate: new Date('2020-01-15'),
        currentGrade: 'Grade 3',
        currentSalary: 45000,
        gender: 'Male',
        location: 'Chennai',
        dateOfBirth: new Date('1990-05-15'),
        designation: 'Assistant Manager',
        fatherName: 'Kumar Raman',
        empIdOrEcpfNo: 'ECPF001'
      },
      {
        id: 'E1002',
        name: 'Priya S',
        position: 'Junior Assistant',
        region: 'R2',
        department: 'FIN',
        employeeCode: 'EMP002',
        email: '<EMAIL>',
        mobileNumber: '9876543211',
        joiningDate: new Date('2021-03-10'),
        currentGrade: 'Grade 2',
        currentSalary: 35000,
        gender: 'Female',
        location: 'Coimbatore',
        dateOfBirth: new Date('1992-08-22'),
        designation: 'Junior Assistant',
        fatherName: 'Suresh Kumar',
        empIdOrEcpfNo: 'ECPF002'
      },
      {
        id: 'E1003',
        name: 'Suresh Babu',
        position: 'Store Keeper',
        region: 'R3',
        department: 'OPS',
        employeeCode: 'EMP003',
        email: '<EMAIL>',
        mobileNumber: '9876543212',
        joiningDate: new Date('2019-07-20'),
        currentGrade: 'Grade 1',
        currentSalary: 28000,
        gender: 'Male',
        location: 'Madurai',
        dateOfBirth: new Date('1988-12-10'),
        designation: 'Store Keeper',
        fatherName: 'Babu Krishnan',
        empIdOrEcpfNo: 'ECPF003'
      },
      {
        id: 'E1004',
        name: 'Anitha Devi',
        position: 'Senior Manager',
        region: 'R1',
        department: 'IT',
        employeeCode: 'EMP004',
        email: '<EMAIL>',
        mobileNumber: '9876543213',
        joiningDate: new Date('2018-05-12'),
        currentGrade: 'Grade 4',
        currentSalary: 65000,
        gender: 'Female',
        location: 'Chennai',
        dateOfBirth: new Date('1985-03-18'),
        designation: 'Senior Manager',
        fatherName: 'Devi Prasad',
        empIdOrEcpfNo: 'ECPF004'
      },
      {
        id: 'E1005',
        name: 'Karthik Raj',
        position: 'Assistant Manager',
        region: 'R2',
        department: 'PROC',
        employeeCode: 'EMP005',
        email: '<EMAIL>',
        mobileNumber: '9876543214',
        joiningDate: new Date('2020-09-01'),
        currentGrade: 'Grade 3',
        currentSalary: 48000,
        gender: 'Male',
        location: 'Coimbatore',
        dateOfBirth: new Date('1991-11-25'),
        designation: 'Assistant Manager',
        fatherName: 'Raj Mohan',
        empIdOrEcpfNo: 'ECPF005'
      }
    ];
  }

  getEmployeesByRegion(regionId: string): Employee[] {
    return this.getEmployees().filter(emp => emp.region === regionId);
  }

  getPositions(): Position[] {
    return [
      { id: 'P001', title: 'Junior Assistant', grade: 'Grade 1', department: 'HR', region: 'R1' },
      { id: 'P002', title: 'Assistant Manager', grade: 'Grade 3', department: 'HR', region: 'R1' },
      { id: 'P003', title: 'Senior Manager', grade: 'Grade 4', department: 'HR', region: 'R1' },
      { id: 'P004', title: 'Junior Assistant', grade: 'Grade 1', department: 'FIN', region: 'R1' },
      { id: 'P005', title: 'Assistant Manager', grade: 'Grade 3', department: 'FIN', region: 'R1' },
      { id: 'P006', title: 'Senior Manager', grade: 'Grade 4', department: 'FIN', region: 'R1' },
      { id: 'P007', title: 'Store Keeper', grade: 'Grade 1', department: 'OPS', region: 'R1' },
      { id: 'P008', title: 'Operations Executive', grade: 'Grade 2', department: 'OPS', region: 'R1' },
      { id: 'P009', title: 'Operations Manager', grade: 'Grade 3', department: 'OPS', region: 'R1' },
      { id: 'P010', title: 'System Administrator', grade: 'Grade 2', department: 'IT', region: 'R1' },
      { id: 'P011', title: 'IT Manager', grade: 'Grade 4', department: 'IT', region: 'R1' }
    ];
  }

  getPositionsByDepartment(departmentId: string): Position[] {
    return this.getPositions().filter(pos => pos.department === departmentId);
  }

  getCurrentEmployee(): Employee {
    // Return current logged-in employee (mock data)
    return this.getEmployees()[0]; // Rajesh Kumar
  }

  // Geographical Data Methods
  getDistricts(): District[] {
    return [
      { id: 'D001', name: 'Chennai', code: 'CHN', regionId: 'R1', description: 'Chennai District' },
      { id: 'D002', name: 'Kanchipuram', code: 'KAN', regionId: 'R1', description: 'Kanchipuram District' },
      { id: 'D003', name: 'Coimbatore', code: 'CBE', regionId: 'R2', description: 'Coimbatore District' },
      { id: 'D004', name: 'Erode', code: 'ERD', regionId: 'R2', description: 'Erode District' },
      { id: 'D005', name: 'Madurai', code: 'MDU', regionId: 'R3', description: 'Madurai District' },
      { id: 'D006', name: 'Theni', code: 'THN', regionId: 'R3', description: 'Theni District' },
      { id: 'D007', name: 'Salem', code: 'SLM', regionId: 'R4', description: 'Salem District' },
      { id: 'D008', name: 'Namakkal', code: 'NMK', regionId: 'R4', description: 'Namakkal District' },
      { id: 'D009', name: 'Tiruchirappalli', code: 'TRY', regionId: 'R5', description: 'Tiruchirappalli District' },
      { id: 'D010', name: 'Thanjavur', code: 'TNJ', regionId: 'R5', description: 'Thanjavur District' }
    ];
  }

  getDistrictsByRegion(regionId: string): District[] {
    return this.getDistricts().filter(district => district.regionId === regionId);
  }

  getSubDistricts(): SubDistrict[] {
    return [
      { id: 'SD001', name: 'Chennai North', code: 'CHN-N', districtId: 'D001', description: 'Chennai North Sub-District' },
      { id: 'SD002', name: 'Chennai South', code: 'CHN-S', districtId: 'D001', description: 'Chennai South Sub-District' },
      { id: 'SD003', name: 'Chengalpattu', code: 'CGP', districtId: 'D002', description: 'Chengalpattu Sub-District' },
      { id: 'SD004', name: 'Coimbatore North', code: 'CBE-N', districtId: 'D003', description: 'Coimbatore North Sub-District' },
      { id: 'SD005', name: 'Coimbatore South', code: 'CBE-S', districtId: 'D003', description: 'Coimbatore South Sub-District' },
      { id: 'SD006', name: 'Erode East', code: 'ERD-E', districtId: 'D004', description: 'Erode East Sub-District' },
      { id: 'SD007', name: 'Madurai East', code: 'MDU-E', districtId: 'D005', description: 'Madurai East Sub-District' },
      { id: 'SD008', name: 'Madurai West', code: 'MDU-W', districtId: 'D005', description: 'Madurai West Sub-District' },
      { id: 'SD009', name: 'Salem North', code: 'SLM-N', districtId: 'D007', description: 'Salem North Sub-District' },
      { id: 'SD010', name: 'Trichy Central', code: 'TRY-C', districtId: 'D009', description: 'Trichy Central Sub-District' }
    ];
  }

  getSubDistrictsByDistrict(districtId: string): SubDistrict[] {
    return this.getSubDistricts().filter(subDistrict => subDistrict.districtId === districtId);
  }

  getLocations(): Location[] {
    return [
      { id: 'L001', name: 'T. Nagar', code: 'TNG', subDistrictId: 'SD002', districtId: 'D001', regionId: 'R1', type: 'TOWN', description: 'T. Nagar Area' },
      { id: 'L002', name: 'Anna Nagar', code: 'ANG', subDistrictId: 'SD001', districtId: 'D001', regionId: 'R1', type: 'TOWN', description: 'Anna Nagar Area' },
      { id: 'L003', name: 'Tambaram', code: 'TMB', subDistrictId: 'SD002', districtId: 'D001', regionId: 'R1', type: 'TOWN', description: 'Tambaram Area' },
      { id: 'L004', name: 'Gandhipuram', code: 'GDP', subDistrictId: 'SD004', districtId: 'D003', regionId: 'R2', type: 'TOWN', description: 'Gandhipuram Area' },
      { id: 'L005', name: 'RS Puram', code: 'RSP', subDistrictId: 'SD005', districtId: 'D003', regionId: 'R2', type: 'TOWN', description: 'RS Puram Area' },
      { id: 'L006', name: 'Periyar', code: 'PER', subDistrictId: 'SD006', districtId: 'D004', regionId: 'R2', type: 'BLOCK', description: 'Periyar Block' },
      { id: 'L007', name: 'Meenakshi Amman', code: 'MAM', subDistrictId: 'SD007', districtId: 'D005', regionId: 'R3', type: 'TOWN', description: 'Meenakshi Amman Area' },
      { id: 'L008', name: 'Anna Nagar Madurai', code: 'ANM', subDistrictId: 'SD008', districtId: 'D005', regionId: 'R3', type: 'TOWN', description: 'Anna Nagar Madurai' },
      { id: 'L009', name: 'Five Roads', code: 'FRD', subDistrictId: 'SD009', districtId: 'D007', regionId: 'R4', type: 'TOWN', description: 'Five Roads Area' },
      { id: 'L010', name: 'Cantonment', code: 'CNT', subDistrictId: 'SD010', districtId: 'D009', regionId: 'R5', type: 'TOWN', description: 'Cantonment Area' }
    ];
  }

  getLocationsBySubDistrict(subDistrictId: string): Location[] {
    return this.getLocations().filter(location => location.subDistrictId === subDistrictId);
  }

  getLocationsByDistrict(districtId: string): Location[] {
    return this.getLocations().filter(location => location.districtId === districtId);
  }

  getLocationsByRegion(regionId: string): Location[] {
    return this.getLocations().filter(location => location.regionId === regionId);
  }

  // Transfer Type Options
  getTransferTypes(): { value: string; label: string }[] {
    return [
      { value: 'PROMOTION', label: 'Promotion Transfer' },
      { value: 'ADMINISTRATIVE', label: 'Administrative Transfer' },
      { value: 'DISCIPLINARY', label: 'Disciplinary Transfer' },
      { value: 'VOLUNTARY', label: 'Voluntary Transfer' },
      { value: 'MEDICAL', label: 'Medical Grounds Transfer' },
      { value: 'FAMILY', label: 'Family Reasons Transfer' },
      { value: 'TRAINING', label: 'Training Purpose Transfer' },
      { value: 'DEPUTATION', label: 'Deputation Transfer' }
    ];
  }
}