import { inject, Injectable } from '@angular/core';
import { DataService } from '../../data.service';
import { Huller, VendorResponse } from '../../../models/x_models/vendor';

@Injectable({
    providedIn: 'root'
})
export class HullerService {
    dataService = inject(DataService)

    create(data: Huller) {
        return this.dataService.post<Response>("/crop", data)
    }

    get() {
        return this.dataService.get<VendorResponse>("/crop")
    }

    getById(id: number) {
        return this.dataService.get<Huller>(`/crop/${id}`)
    }

    delete(id: number) {
        return this.dataService.delete<Response>(`/crop/${id}`)
    }
}
