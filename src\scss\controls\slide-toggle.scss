@use '@angular/material' as mat;

:root {
  @include mat.slide-toggle-overrides((
    track-width: 32px,
    track-height: 20px,
    selected-icon-size: 0px,
    unselected-icon-size: 0px,
    track-outline-width: 0,
    selected-with-icon-handle-horizontal-margin: 0 14px,
    unselected-with-icon-handle-horizontal-margin: 0px 2px,
    selected-pressed-handle-horizontal-margin: 0 14px,
    pressed-handle-size: 16px,
    selected-pressed-state-layer-color: #fff,
    selected-hover-state-layer-color: #fff,
    selected-focus-state-layer-color: #fff
  ));
}

.mat-mdc-slide-toggle {
  --mat-switch-with-icon-handle-size: 16px;
  --mdc-switch-unselected-hover-handle-color: var(--grey-40);
  --mdc-switch-unselected-focus-handle-color: var(--grey-40);
  --mdc-switch-unselected-pressed-handle-color: var(--grey-40);

  --mdc-switch-selected-hover-handle-color: #fff;
  --mdc-switch-selected-focus-handle-color: #fff;
  --mdc-switch-selected-pressed-handle-color: #fff;

  --mdc-switch-selected-focus-track-color: var(--theme-80);
  --mdc-switch-selected-hover-track-color: var(--theme-80);
  --mdc-switch-selected-pressed-track-color: var(--theme-80);
  --mdc-switch-selected-track-color: var(--theme-80);
  --mdc-switch-disabled-selected-track-color: var(--theme-80);
  --mat-switch-disabled-unselected-track-outline-color: #e0e2ec;
  --mdc-switch-disabled-track-opacity: 0.4;
  width: 100%;
  .mdc-switch__ripple {
    display: none;
  }
  .mdc-form-field {
    //flex-direction: row-reverse !important;
    justify-content: space-between !important;
    width: 100%;
    column-gap: 12px;

    .mdc-label {
      margin-bottom: 0;
    }
  }
}
