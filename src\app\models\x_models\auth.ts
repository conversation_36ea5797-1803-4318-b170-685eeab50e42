import { Response } from "./api-response"

export interface LoginPayload {
  username: string,
  password: string,
  timeZone: string,
  isForceLogout: boolean
}

export interface TokenReponse extends Response{
    responseType: string
    responseData: string
    validationErrors:string
}

export interface LogoutPayload {
  userId: number;
}

export interface ForgotPasswordPayload {
  username: string;
}

export interface ChangePasswordPayload {
  oldPassword: string;
  newPassword: string;
}
