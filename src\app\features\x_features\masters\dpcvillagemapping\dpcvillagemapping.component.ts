import { CommonModule } from '@angular/common';
import { Component, computed, ElementRef, inject, input, OnInit, output, resource, signal, viewChild } from '@angular/core';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatError, MatFormField, MatOption, MatSelect, MatSelectModule } from '@angular/material/select';
import { LookUpResponse, mappingResponse } from '../../../../models/x_models/lookup';
import { LookupService } from '../../../../services/x_apis/lookup.service';
import { TranslateModule } from '@ngx-translate/core';
import { MatButton } from '@angular/material/button';
import { MatNativeDateModule, MatOptionModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInput } from '@angular/material/input';
import { MatRadioModule } from '@angular/material/radio';
import { MatListModule } from '@angular/material/list';
import { MatTooltipModule } from '@angular/material/tooltip';
import { AlertService } from '../../../../services/alert.service';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { DpcvillagemappingService } from '../../../../services/x_apis/masters/dpcvillagemapping.service';
import { MatToolbarModule } from '@angular/material/toolbar';

@Component({
  selector: 'app-dpcvillagemapping',
  imports: [
    MatCheckboxModule,
    FormsModule,
    MatFormField,
    MatInput,
    ReactiveFormsModule,
    MatError,
    MatOption,
    MatSelect,
    MatIconModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatRadioModule,
    MatDatepickerModule,
    TranslateModule,
    MatFormFieldModule,
    MatListModule,
    MatTooltipModule,
    CommonModule,
    MatButton,
    MatToolbarModule
      ],
  templateUrl: './dpcvillagemapping.component.html',
  styleUrls: ['./dpcvillagemapping.component.scss']
})
export class DpcvillagemappingComponent implements OnInit {

  uniqueId = input.required<number>()
  name=input.required<string>()
  closed = output<boolean>()
  fb = inject(FormBuilder)
  lookupService=inject(LookupService)
  menuService = inject(MenuService);
  dpcVillageMappingService = inject(DpcvillagemappingService);
  
  
  alertService=inject(AlertService)
  isSelectAllTransfer = signal<boolean>(false)
  select = signal<LookUpResponse[]>([])
  selected=signal<LookUpResponse[]>([])
  taluks = signal<LookUpResponse[]>([]);
  DPC = signal<LookUpResponse[]>([]);
  village = signal<mappingResponse[]>([]);
  selectedTransferList=signal<any>([])
  isSelectAllRemoveTransfer=signal<boolean>(false)
  mode = input.required<string>();
  editable = signal<boolean>(true);
  mappingDetails=signal<any>({});


  form = this.fb.group({
    id:[],
    regionId: this.fb.control<number | null>(null, Validators.required),
    talukId: this.fb.control<number | null>(null, Validators.required),
    dpcId: this.fb.control<number | null>(null, Validators.required),
    firstListDatas:[],
    secondListDatas:[],
    firstSelectedDatas:[],
    secondSelectedDatas:[]
  })
  regions = resource({ loader: () => this.lookupService.getRegion() }).value;
  search = viewChild.required<ElementRef<HTMLInputElement>>('search')
  search1 = viewChild.required<ElementRef<HTMLInputElement>>('search1')
  search2 = viewChild.required<ElementRef<HTMLInputElement>>('search2')
  search3 = viewChild.required<ElementRef<HTMLInputElement>>('search3')
  search4 = viewChild.required<ElementRef<HTMLInputElement>>('search4')


  constructor() { }

  ngOnInit() {

     if (this.mode() === "edit" || this.mode() === "view") {
      this.getMappingDetails();
    }
     this.form.get("regionId")?.valueChanges.subscribe(res => {
      this.getTaluk(res);
     
      
    })

    this.form.get("talukId")?.valueChanges.subscribe(res => {
      this.getDpc(res);
      this.getVillage(res);
    })
  
  }

   readonly regionSearch = signal('');
   readonly talukSearch = signal('');
   readonly dpcSearch = signal('');
   readonly villageSearch = signal('');
   readonly selectSearch = signal('');



  readonly filteredRegions = computed(() =>
    this.regions()?.filter(x =>
      x.value.toLowerCase().includes(this.regionSearch().toLowerCase())
    )
  );

  readonly filteredTaluks = computed(() =>
    this.taluks()?.filter(x =>
      x.value.toLowerCase().includes(this.talukSearch().toLowerCase())
    )
  );

   readonly filteredDPC = computed(() =>
    this.DPC()?.filter(x =>
      x.value.toLowerCase().includes(this.dpcSearch().toLowerCase())
    )
  );

   readonly filteredVillage = computed(() =>
    this.village()?.filter(x =>
      x.value.toLowerCase().includes(this.villageSearch().toLowerCase())
    )
  );

   readonly filteredSelect = computed(() =>
    this.selectedTransferList()?.filter((x:any) =>
      x.value.toLowerCase().includes(this.selectSearch().toLowerCase())
    )
  );


   async getTaluk(regionId:any) {
    const res = await this.lookupService.getTaluk(regionId!, 0)
    this.taluks.set(res);

    // this.form.controls['dpcId'].setValue(0)

  }

  async getDpc(talukId:any)
  {
    const res=await this.lookupService.getDPCByTaluk(talukId!)
    this.DPC.set(res)
    if(this.uniqueId()>0)
    {
      this.form.controls['dpcId'].setValue(this.mappingDetails().dpcId)
    }
  }
   async getVillage(talukId:any)
  {
    const res=await this.lookupService.getVillageByTaluk(talukId!)
    this.village.set(res)
    this.village().forEach(x=>{
      x.isSelect=false
    })
    if(this.uniqueId()>0)
    {
      debugger
    const villageList=this.village().filter(x=>!this.mappingDetails().villageId.includes(x.key));
    console.log(this.village(),this.mappingDetails().villageId,'pppppppp');
    
    let selectList=this.village().filter(x=>this.mappingDetails().villageId.includes(x.key))
    this.selectedTransferList.set(selectList)
    this.selectedTransferList().map((x:any)=>x.isSelect=true);
    this.village.set(villageList);
    console.log(this.selectedTransferList(),'iiiiii');
    }
  }

  filter(value: any,type:string) {
    if(type=='region')
    {
    this.regionSearch.set(value.target.value);
    }
    else if(type=='taluk'){
    this.talukSearch.set(value.target.value);
    }
    else if(type=='dpc'){
    this.dpcSearch.set(value.target.value);
    }
    else if(type=='village'){
    this.villageSearch.set(value.target.value);
    }
    else{
    this.selectSearch.set(value.target.value);
    }
  }


  resetSearch(type: any) {
    if (type === 'region') {
      this.search().nativeElement.value = '';
      this.regionSearch.set('');
    }  
    else if (type === 'taluk') {
      this.talukSearch.set('');
      this.search1().nativeElement.value = '';
    } 
    else if (type === 'dpc') {
      this.dpcSearch.set('');
      this.search2().nativeElement.value = '';
    } 
     else if (type === 'village') {
      this.villageSearch.set('');
      this.search3().nativeElement.value = '';
    } 
     else  {
      this.selectSearch.set('');
      this.search4().nativeElement.value = '';
    } 
  }

  async getMappingDetails()
  {
    debugger
    const res=await this.dpcVillageMappingService.getById(this.uniqueId());
    this.form.patchValue({...res});
    this.mappingDetails.set(res)  
    if (this.mode() === "view") {
      this.editable.set(false)
      this.form.disable()
    }
  }
  


  selectAllTransfer(event: any) {
        if (event.checked) {
            this.isSelectAllTransfer.set(true) ;
            this.select.set([...this.village()]);
            this.village().forEach(element => {
                element.isSelect = true;
            });
        } else {
            this.village().forEach(element => {
                element.isSelect = false;
            });
            this.select.set([])
            this.isSelectAllTransfer.set(false);
        }
  }

    onNgModelChange(event:any) {
      debugger
        let array = event.options[0]._value
        if (array.isSelect) {
            array.isSelect = false;
        } else {
            array.isSelect = true;
        }
    }

     moveToTransfer() {
      debugger
        if (this.village().length > 0) {
            let data = this.village().filter(item => item.isSelect == true);
            if (data.length > 0) {
              
              let transferList= this.selectedTransferList().concat(data);
                this.selectedTransferList.set(transferList)
                console.log(this.selectedTransferList());
                
                let list= this.village().filter(item => item.isSelect == false)
                this.village.set(list)
                this.isSelectAllTransfer.set(false) ;
            } else {
                let msg = 'Please select atleast one data to transfer'
                this.alertService.info(msg);
            }
        } else {
          
            this.select.set([...this.village()])
        }
    }

    removedToTransfer() {
      debugger
        if (this.selectedTransferList().length > 0) {
            let data = this.selectedTransferList().filter((item:any) => item.isSelect == false);
            let list= this.village().concat(data)
            this.village.set(list)
            let filter= this.selectedTransferList().filter((item:any) => item.isSelect == true);
            this.selectedTransferList.set(filter)
        } else {
            let msg = 'Please select atleast one data to transfer'
            this.alertService.info(msg);
        }
        this.isSelectAllRemoveTransfer.set(false);
    }

     selectAllRemoveTransfer(event:any) {        
        if (event.checked) {
            this.selected.set([... this.selectedTransferList()]);
            this.selectedTransferList().forEach((element: { isSelect: boolean; }) => {
                element.isSelect = false;
                this.isSelectAllRemoveTransfer.set(true);
            });
        } else {
            this.selectedTransferList().forEach((element:any) => {
                element.isSelect = true;
            });
            this.selected.set([]) ;
            this.isSelectAllRemoveTransfer.set(false);
        }
    }

     onEdit() {
    this.form.enable();
    this.editable.set(true)
  }
  goBack() {
    this.closed.emit(true)
  }

    formReset() {
    let indexId = this.form.value.id;
    this.form.reset();
    this.form.patchValue({
      id: indexId
    });
  }

  async submit()
  {
    let villageIds: any[]=[];
    this.selectedTransferList().forEach((e:any)=>{
      villageIds.push(e.key)
    })

    var payload={
      dpcId:this.form.value.dpcId,
      villageIdList:villageIds,
       id:this.uniqueId(),
    }

    const res= await this.dpcVillageMappingService.create(payload);
    this.alertService.success("DPC Mapped to Village Successfully")
    this.closed.emit(true)
  }
}
