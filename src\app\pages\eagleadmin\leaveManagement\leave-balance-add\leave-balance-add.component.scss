/* Leave Balance Entry Styles - Matching Design System */

.leave-balance-container {
    padding: 24px;
    background-color: #f5f5f5;
    min-height: 100vh;
  
    .header-card {
      background: linear-gradient(135deg, #203664 0%, darken(#203664, 10%) 100%);
      color: white;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      margin-bottom: 24px;
      transition: transform 0.3s ease;
    
      &:hover {
        transform: translateY(-4px);
      }
    
      .form-header {
        padding: 16px;
        display: flex;
        align-items: center;
    
        .header-content {
          display: flex;
          align-items: center;
          width: 100%;
        }
    
        .logo-section {
          margin-right: 16px;
    
          .tncsc-logo {
            font-size: 40px;
            width: 40px;
            height: 40px;
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
    
        .title-section {
          .form-subtitle {
            font-size: 24px;
            font-weight: 500;
            margin: 0;
          }
    
          .subtitle {
            font-size: 14px;
            opacity: 0.8;
            margin: 4px 0 0;
          }
        }
      }
    }
  
    .apply-card {
      max-width: 100%;
      width: 100%;
      margin: 0 auto;
      padding: 24px;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      border-left: 4px solid #11074e;
      background-color: white;
  
      /* Override form styles */
      form {
        width: 100% !important;
        max-width: none !important;
        margin: 0 !important;
        padding: 0 !important;
        background: transparent !important;
        box-shadow: none !important;
        border-radius: 0 !important;
  
        /* Form heading */
        h2 {
          color: #333 !important;
          font-weight: 500 !important;
          margin-bottom: 24px !important;
          font-size: 1.5rem !important;
          text-align: center;
          padding-bottom: 12px;
          border-bottom: 2px solid #11074e;
          background: transparent !important;
        }
  
        /* Form fields styling */
        .mat-form-field {
          width: 100% !important;
          margin-bottom: 20px !important;
          display: block;
  
          &.mat-form-field-appearance-fill {
            .mat-form-field-flex {
              background-color: #f8fafc !important;
              border-radius: 6px !important;
              border: 1px solid #e2e8f0 !important;
              transition: all 0.2s ease;
            }
  
            &:hover .mat-form-field-flex {
              border-color: #11074e !important;
              background-color: #ffffff !important;
            }
  
            &.mat-focused .mat-form-field-flex {
              border-color: #11074e !important;
              background-color: white !important;
              box-shadow: 0 0 0 2px rgba(17, 7, 78, 0.1) !important;
            }
  
            /* Remove default underline */
            .mat-form-field-underline::before {
              background-color: transparent !important;
            }
  
            .mat-form-field-underline .mat-form-field-ripple {
              background-color: #11074e !important;
            }
          }
  
          .mat-form-field-label {
            color: #666 !important;
            font-weight: 500 !important;
          }
  
          .mat-input-element,
          .mat-select-trigger {
            color: #333 !important;
            font-size: 1rem !important;
            padding: 12px 16px !important;
          }
  
          .mat-error {
            color: #dc2626 !important;
            font-size: 0.875rem !important;
            font-weight: 500 !important;
            margin-top: 4px !important;
          }
  
          /* Select dropdown styling */
          .mat-select-arrow {
            color: #666 !important;
          }
        }
  
        /* Button container */
        .flex.justify-center {
          display: flex !important;
          justify-content: center !important;
          margin-top: 32px !important;
        }
  
        /* Button styling */
        .mat-raised-button {
          background-color: #11074e !important;
          color: white !important;
          padding: 12px 32px !important;
          border-radius: 8px !important;
          font-weight: 500 !important;
          font-size: 1rem !important;
          border: none !important;
          cursor: pointer !important;
          transition: all 0.2s ease !important;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
          text-transform: none !important;
  
          &:hover:not([disabled]) {
            background-color: #1a0c7a !important;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
            transform: translateY(-1px) !important;
          }
  
          &[disabled] {
            background-color: #9ca3af !important;
            color: #6b7280 !important;
            cursor: not-allowed !important;
            box-shadow: none !important;
            transform: none !important;
          }
        }
  
        /* Message styling */
        .mt-4 {
          margin-top: 16px !important;
        }
  
        .text-red-600 {
          background-color: #fee2e2 !important;
          color: #dc2626 !important;
          border: 1px solid #fecaca !important;
          padding: 12px 16px !important;
          border-radius: 6px !important;
          font-weight: 500 !important;
          text-align: center !important;
        }
  
        .text-green-600 {
          background-color: #dcfce7 !important;
          color: #16a34a !important;
          border: 1px solid #bbf7d0 !important;
          padding: 12px 16px !important;
          border-radius: 6px !important;
          font-weight: 500 !important;
          text-align: center !important;
        }
  
        .text-center {
          text-align: center !important;
        }
      }
    }
  
    /* Responsive Design */
    @media (max-width: 768px) {
      padding: 16px;
  
      .header {
        .page-title {
          font-size: 2rem;
          padding-left: 16px;
          flex-direction: column;
          align-items: flex-start;
          gap: 8px;
  
          mat-icon {
            font-size: 2rem;
            width: 2rem;
            height: 2rem;
          }
        }
  
        .subtitle {
          padding: 12px 16px;
          margin: 8px 0 0 0;
        }
      }
  
      .apply-card {
        padding: 16px;
  
        form {
          h2 {
            font-size: 1.25rem !important;
          }
  
          .mat-form-field {
            margin-bottom: 16px !important;
  
            .mat-input-element,
            .mat-select-trigger {
              padding: 10px 12px !important;
              font-size: 0.875rem !important;
            }
          }
  
          .flex.justify-center {
            margin-top: 24px !important;
          }
  
          .mat-raised-button {
            padding: 10px 24px !important;
            font-size: 0.875rem !important;
            width: 100% !important;
          }
        }
      }
    }
  
    @media (max-width: 480px) {
      padding: 12px;
  
      .header {
        .page-title {
          font-size: 1.75rem;
          padding-left: 12px;
  
          mat-icon {
            font-size: 1.75rem;
            width: 1.75rem;
            height: 1.75rem;
          }
        }
  
        .subtitle {
          padding: 10px 12px;
          font-size: 1rem;
        }
      }
  
      .apply-card {
        padding: 12px;
        border-left-width: 3px;
  
        form {
          h2 {
            font-size: 1.125rem !important;
            margin-bottom: 16px !important;
            padding-bottom: 8px !important;
          }
        }
      }
    }
  }
  
  /* Global overrides for Material UI components */
  .mat-form-field-appearance-fill .mat-form-field-flex {
    background-color: #f8fafc !important;
    border: 1px solid #e2e8f0 !important;
    border-radius: 6px !important;
  }
  
  .mat-form-field-appearance-fill.mat-focused .mat-form-field-flex {
    border-color: #11074e !important;
    background-color: white !important;
  }
  
  .mat-primary .mat-option.mat-selected:not(.mat-option-disabled) {
    color: #11074e !important;
  }
  
  .mat-primary .mat-pseudo-checkbox-checked {
    background: #11074e !important;
  }
  
  /* Utility classes */
  .w-full {
    width: 100% !important;
  }
  
  .mb-4 {
    margin-bottom: 16px !important;
  }
  
  .px-6 {
    padding-left: 24px !important;
    padding-right: 24px !important;
  }
  
  .py-2 {
    padding-top: 8px !important;
    padding-bottom: 8px !important;
  }