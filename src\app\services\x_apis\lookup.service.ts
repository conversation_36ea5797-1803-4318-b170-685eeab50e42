import { inject, Injectable } from '@angular/core';
import { LookUpResponse } from '../../models/x_models/lookup';
import { DataService } from '../data.service';

@Injectable({
  providedIn: 'root'
})
export class LookupService {
  dataService = inject(DataService)

  constructor() { 
  }

  
  getRole() {
    return this.dataService.get<LookUpResponse[]>("/lookup/role");
  }

  status() {
    return this.dataService.get("/lookup/status");
  }

  getSeason() {
    return this.dataService.get<LookUpResponse[]>("/lookup/season");
  }

  getprocurementPriceCut() {
    return this.dataService.get<LookUpResponse[]>("/lookup/procurementpricecuttype");
  }

 
  getRegion() {
    const language = localStorage.getItem('language') == 'en' ? 1 : 2;
    return this.dataService.get<LookUpResponse[]>(`/lookup/region/${language}`);
  }
  
  getDelta() {
    return this.dataService.get<LookUpResponse[]>("/lookup/delta");
  }

  getUnit() {
    const language = localStorage.getItem('language') == 'en' ? 1 : 2;
    return this.dataService.get<LookUpResponse[]>(`/lookup/unit/${language}`)
  }

  getDpcType() {
    // const language = localStorage.getItem('language') == 'en' ? 1 : 2;
    // return this.dataService.get<LookUpResponse[]>(`/lookup/dpctype/${language}`)
    return this.dataService.get<LookUpResponse[]>(`/lookup/dpctype`)

  }

  getPosType() {
    // const language = localStorage.getItem('language') == 'en' ? 1 : 2;
    // return this.dataService.get<LookUpResponse[]>(`/lookup/postype/${language}`)
    return this.dataService.get<LookUpResponse[]>(`/lookup/postype`);
  }

  getTaluk(regionId: number, unitId: number) { 
    // const language = localStorage.getItem('language') == 'en' ? 1 : 2;
    return this.dataService.get<LookUpResponse[]>(`/lookup/taluk/${regionId}/${unitId}/${1}`);
  }

  getBlock(talukId:number)
  {
    const language = localStorage.getItem('language') == 'en' ? 1 : 2;
    return this.dataService.get<LookUpResponse[]>(`/lookup/block/${talukId}/${language}`)
  }

  getPaddy() {
    return this.dataService.get<LookUpResponse[]>("/lookup/paddytype");
  }

  getMrm() {
    // const language = localStorage.getItem('language') == 'en' ? 1 : 2;
    // return this.dataService.get<LookUpResponse[]>(`/lookup/mrmtype/${language}`);
    return this.dataService.get<LookUpResponse[]>(`/lookup/mrmtype`);
  }

  getMill() {
    // const language = localStorage.getItem('language') == 'en' ? 1 : 2;
    // return this.dataService.get<LookUpResponse[]>(`/lookup/milltype/${language}`);
    return this.dataService.get<LookUpResponse[]>(`/lookup/milltype`);
  }


  getVillage(blockId:number)
  {
    const language = localStorage.getItem('language') == 'en' ? 1 : 2;
    return this.dataService.get<LookUpResponse[]>(`/lookup/village/${blockId}/${language}`)
  }

  getAgency()
  {
    return this.dataService.get<LookUpResponse[]>(`/lookup/agency`)
  }

  getStorageType(){
    // const language = localStorage.getItem('language') == 'en' ? 1 : 2;
    // return this.dataService.get<LookUpResponse[]>(`/lookup/storagetype/${language}`);
    return this.dataService.get<LookUpResponse[]>(`/lookup/storagetype`);
  }

  getProvider(){
    return this.dataService.get<LookUpResponse[]>("/lookup/providertype");
  }

  getProtocol(){
    return this.dataService.get<LookUpResponse[]>("/lookup/protocol");
  }
  
  getDeviceManifacturer(){
    return this.dataService.get<LookUpResponse[]>("/lookup/devicemanufacturetype");
  }

  getUnmappedProvider(){
    return this.dataService.get<LookUpResponse[]>("/lookup/unmappedprovider");
  }

  getProduct(){
    const language = localStorage.getItem('language') == 'en' ? 1 : 2;
    return this.dataService.get<LookUpResponse[]>(`/lookup/product/${language}`);
  }

  getHullingPriceCut(){
    return this.dataService.get<LookUpResponse[]>("/lookup/hullingpricecuttype");
  }

  getVariety(){
    return this.dataService.get<LookUpResponse[]>("/lookup/variety");
  }

   getDelayedDayCut(){
    return this.dataService.get<LookUpResponse[]>("/lookup/delayeddaystype");
  }

  getVendor(){
    return this.dataService.get<LookUpResponse[]>("/lookup/vendor");
  }

  getVendorType(){
    return this.dataService.get<LookUpResponse[]>("/lookup/vendortype");
  }

  getVillageByVao(){
    return this.dataService.get<LookUpResponse[]>("/lookup/villagebyvao/1");
  }

  getGunnytype(){
    return this.dataService.get<LookUpResponse[]>("/lookup/gunnytypedetail");
  }

  getStoragelocation(id : number){
    return this.dataService.get<LookUpResponse[]>(`/lookup/storagelocation/${id}`);
  }

  
  getDPCByTaluk(talukId:number)
  {
    return this.dataService.get<LookUpResponse[]>(`/lookup/dpc/taluk/${talukId}/${1}`)
  }

  getVillageByTaluk(talukId:number)
  {
    return this.dataService.get<LookUpResponse[]>(`/lookup/village/taluk/${talukId}/${1}`)
  }
}
