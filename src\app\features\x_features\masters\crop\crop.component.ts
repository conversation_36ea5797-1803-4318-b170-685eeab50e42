import { Component, computed, inject, input, OnInit, output, resource, signal } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { <PERSON>Button } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormField, MatInput, MatError } from '@angular/material/input';
import { MatOption, MatSelect } from '@angular/material/select';
import { LookupService } from '../../../../services/x_apis/lookup.service';
import { CropService } from '../../../../services/x_apis/masters/crop.service';
import { Router } from '@angular/router';
import { Crop } from '../../../../models/x_models/masters/crop';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { provideNativeDateAdapter } from '@angular/material/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { AlertService } from '../../../../services/alert.service';
import { DatePipe } from '@angular/common';
import { InputformatService } from '../../../../services/x_apis/inputformat.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { MatToolbarModule } from '@angular/material/toolbar';

@Component({
  selector: 'app-crop',
  imports: [
    FormsModule,
    MatFormField,
    MatInput,
    ReactiveFormsModule,
    MatError,
    MatOption,
    MatSelect,
    MatButton,
    MatIconModule,
    MatDatepickerModule,
    MatFormFieldModule,
    TranslateModule,
    MatToolbarModule
  ],
  templateUrl: './crop.component.html',
  styleUrls: ['./crop.component.scss'],
  providers: [provideNativeDateAdapter(), DatePipe],
})
export class CropComponent implements OnInit {
   menuService = inject(MenuService);
  cropService = inject(CropService)
  alert = inject(AlertService);
  inputFormatService = inject(InputformatService);
  translate = inject(TranslateService);

  lookupService = inject(LookupService);
  seasons = resource({ loader: () => this.lookupService.getSeason() }).value;

  mode = input.required<string>();
  cropId = input.required<any>()
  closed = output<boolean>();
  router = inject(Router);
  fb = inject(FormBuilder);
  editable = signal<boolean>(true);
  crop = signal<Crop | null>(null);
  minToDate = signal<Date>(new Date());

  form = this.fb.group({
    id: [0],
    cropName: this.fb.control<string>('', Validators.required),
    fromDate: this.fb.control<string>('', Validators.required),
    toDate: this.fb.control<string>('', Validators.required),
    seasonId: this.fb.control<number | null>(null, Validators.required)
  });

  constructor(private datePipe: DatePipe) { }

  ngOnInit() {
    this.form.get("fromDate")?.valueChanges.subscribe((res) => {
      if (res && this.editable()) {
        const parsedFromDate = new Date(res);
        parsedFromDate.setDate(parsedFromDate.getDate() + 1);
        this.minToDate.set(parsedFromDate);
        this.form.patchValue({ toDate: '' });
      } else {
        this.minToDate.set(new Date());
      }
    });
    if (this.mode() === "edit" || this.mode() === "view") {
      this.getCrop();
    }
  }

  readonly search = signal('');

  readonly filteredTaluks = computed(() =>
    this.seasons()?.filter(x =>
      x.value.toLowerCase().includes(this.search().toLowerCase())
    )
  );

  filter(value: any) {
    this.search.set(value.target.value);
  }

  async getCrop() {
    const res = await this.cropService.getById(this.cropId());
    this.crop.set(res);
    this.form.patchValue({ ...res });
    if (this.mode() === "view") {
      this.editable.set(false)
      this.form.disable()
    }
  }

  onEdit() {
    this.form.enable();
    this.editable.set(true)
  }

  async onSubmit() {
    if (this.form.valid) {
      const formValue = { ...this.form.value };
      formValue.fromDate = this.datePipe.transform(this.form.value.fromDate, 'yyyy-MM-dd');
      formValue.toDate = this.datePipe.transform(this.form.value.toDate, 'yyyy-MM-dd');
      const res = await this.cropService.create(formValue as Crop)
      if (res.isSuccess) {
        this.closed.emit(true)
        if (formValue.id == 0) {
          this.alert.success(`Crop ${formValue.cropName} Created Successfully.`)
        }
        else {
          this.alert.success(`Crop ${formValue.cropName} Updated Successfully.`)
        }
      }
    }
    else {
      this.form.markAllAsTouched();
    }
  }

  goBack() {
    this.closed.emit(true)
  }

  formReset() {
    let indexId = this.form.value.id;
    this.form.reset();
    this.form.patchValue({
      id: indexId
    });
  }

  onInput(event: any, regexPattern: any): void {
    this.inputFormatService.isNumericOrHyphenOrSpace(event, regexPattern);
  }
}
