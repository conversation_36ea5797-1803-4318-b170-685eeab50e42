:host {
  padding: 0px;
  display: block;
  color: var(--grey-100);
  height: 100%;
  overflow-x: clip;
}

.sidebar {
  display: flex;
  flex-direction: column;
  // min-height: 100%;
  position: relative;
  padding-inline: 12px;
  height: 100%;
  padding-inline: 0;
  background: white;
}

header {
  background: var(--logo-bg);
  position: sticky;
  top: 0;
  z-index: 1;
  height: 72px;
  display: flex;
  align-items: center;
  padding-inline: 12px;
  margin-bottom: 10px;

  img {
    &.logo-collapse {
      width: 46px;
    }

    &.logo-expand {
      margin-top: 30px;
      margin-left: 17.65px;
      height: 38px;
      position: relative;
      left: -2px;
    }
  }
}

nav{
  height: calc(100% - 5px);
  overflow: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none;  /* IE 10+ */
  // padding-inline: 12px;
}

nav::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Edge */
}

.label {
  white-space: nowrap;
}

.menus {
  // margin-top: 21.5px;
  // margin-top: 11.5px;
  padding: 0;
  list-style: none;
  display: flex;
  flex-direction: column;
  row-gap: 6px;
  
  a,
  .btn-link {
    text-decoration: none;
    color: inherit;
    font-weight: 300;
    font-size: 14px;
    width: 100%;
    color: var(--sidebar-fg);

    &.active {
      color: var(--active-menu-fg);
    }

    &:focus-visible {
      outline: 2px solid var(--theme-30);
      outline: 0px solid var(--theme-30);
      position: relative;
      &::after {
        content: "";
        position: absolute;
        width: 100%;
        height: 100%;
        border: 2px solid var(--theme-30);
        translate: -5px;
        border-radius: 2px;
      }
      // outline: none;
      // background: rgba(#fff, 0.5);
      // border: 1px solid black;
    }
  }

  .item {
    // margin-bottom: 5px;
    // height: 24px;
    overflow: visible;
    padding: 0px;
    will-change: height;

    a,
    .btn-link {
      height: 35px;
      padding: 18px;
      border-radius: 0px;

      .link-content {
        display: flex;
        align-items: center;
        align-items: center;
        gap: 5px;
        width: 100%;
        font-weight: 500;
        // font-size: 15px;

        .link-icon-wrapper {
          min-width: 35px;
          height: 35px;
          display: flex;
          justify-content: center;
          align-items: center;
        }

        .link-label {
          flex-grow: 1;
          display: flex;
          align-items: center;

          .label {
            display: block;
          }
        }

        .link-expand-wrapper {
          width: 25px;
          height: 25px;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }

    a,
    .btn-link {
      display: flex;
      align-items: center;
      position: relative;

      .icon {
        // margin-right: 19px;

        &.dashboard {
          width: 18.6px;
          height: 17.8px;
        }

        &.quick-card-search {
          width: 21px;
          height: 18.9px;
        }

        &.card-actions {
          width: 22px;
          height: 20px;
        }

        &.multi-card-actions {
          width: 25px;
          height: 20px;
        }

        &.reports {
          width: 19px;
          height: 21px;
        }
      }

      .expand {
        // margin-left: auto;
        // width: 16.4px;
        // height: 16.4px;
        transition: transform 0.5s ease;
      }

      &.active {
        background-color: var(--active-menu-bg);
      }
    }

    &.closing {
      animation: collapse 0.3s ease-out;
      overflow: hidden;
    }

    &.active {
      height: calc(40px * var(--submenuCount) + 35px);
      animation: expand 0.3s ease-out;

      &.collapsed {
        background-color: #fff;
        border-radius: 8px;
      }
    }

    .submenus {
      list-style: none;
      padding-left: 0px;

      .sub-item {
        // padding: 17px 2px 3px 0px;

        a {
          // margin-top: 5px;
        }

        .link-icon-wrapper .icon {
          color: var(--theme-80);
        }

        .active {
          background-color: var(--darkblue-30);

          .link-icon-wrapper.collapsed {
            background-color: var(--theme-30);
            border-radius: 5px;
          }
        }

        .sub-item-dot {
          width: 6px;
          height: 6px;
          background-color: var(--theme-50);
          border-radius: 100px;
        }

        .active .sub-item-dot {
          background-color: var(--theme-50);
        }
      }
    }

    .btn-link,
    a {
      .link-label .label {
        transition: translate .2s ease-out;
        will-change: translate;
      }

      &:hover {
        .link-label .label {
          translate: 2px 0px;
        }
      }
    }
  }

  .active {
    font-weight: 500;
  }
}

.actions {
  display: flex;
  flex-direction: column;
  row-gap: 17px;
  margin-top: auto;
  padding-inline: 7.65px;
  // padding-inline: 7.65px + 12.35px;
  padding-block: 30px;
  background: linear-gradient(31deg, #B3DDFB, #BEE5F9);
  // position: absolute;
  // bottom: 0;
  // left: 0;
  // width: 100%;
  padding-inline: 17px;
  background: transparent;


  .btn {
    // width: 177px;
    width: 100%;
    font-size: 14px;
    font-weight: 600;
    height: 35px;

    .icon-gear {
      color: var(--theme-80);
    }

    .icon-support {
      color: var(--grey-100);
    }
  }

  &.tab {
    .btn {
      width: 35px;
      min-width: auto;
      display: flex;
      justify-content: left;
      padding-inline: 5px;
      overflow: clip;
    }

    mat-icon {
      min-height: 15px;
      min-width: 15px;
      margin: 5px 12px 5px 5px;
    }
  }
}

.material-symbols-outlined {
  font-size: 18px;
}

:host ::ng-deep .item.active .expand {
  transform: rotateX(-180deg);

  circle {
    fill: var(--theme-40)
  }

  path {
    // stroke: #ffffff
  }
}

@keyframes expand {
  0% {
    height: 35px;
    overflow: hidden;
  }

  100% {
    height: calc(40px * var(--submenuCount) + 35px);
    overflow: hidden;
  }
}

@keyframes collapse {
  0% {
    height: calc(40px * var(--submenuCount) + 35px);
  }

  100% {
    height: 35px;
  }
}
