// Transfer Module Shared Styles
// This file contains common styles used across all transfer components

// Variables
$primary-color: #203664;
$accent-color: #203664;
$success-color: #4caf50;
$error-color: #f44336;
$warning-color: #ff9800;
$info-color: #2196f3;
$light-gray: #f5f5f5;
$border-radius: 8px;
$box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

// Mixins
@mixin card-style {
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  overflow: hidden;
}

@mixin header-gradient {
  background: linear-gradient(135deg, $primary-color 0%, lighten($primary-color, 10%) 100%);
  color: white;
}

@mixin button-style($color) {
  background: linear-gradient(135deg, $color 0%, lighten($color, 10%) 100%);
  color: white;
  box-shadow: 0 2px 4px rgba($color, 0.3);
  
  &:hover:not(:disabled) {
    box-shadow: 0 4px 8px rgba($color, 0.4);
    transform: translateY(-1px);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

@mixin status-badge($color) {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  background-color: lighten($color, 40%);
  color: $color;
}

@mixin priority-badge($color) {
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  background-color: lighten($color, 40%);
  color: $color;
}

// Common Classes
.transfer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  min-height: calc(100vh - 100px);
}

.transfer-card {
  @include card-style;
  
  .card-header {
    @include header-gradient;
    padding: 24px;
    
    mat-card-title {
      display: flex;
      align-items: center;
      gap: 12px;
      margin: 0;
      
      .header-icon {
        font-size: 28px;
        width: 28px;
        height: 28px;
      }
      
      h1 {
        margin: 0;
        font-size: 24px;
        font-weight: 500;
      }
    }
    
    mat-card-subtitle {
      color: rgba(255, 255, 255, 0.8);
      margin-top: 8px;
      font-size: 14px;
    }
  }
  
  .progress-bar {
    height: 3px;
    
    ::ng-deep .mat-progress-bar-fill::after {
      background-color: $accent-color;
    }
  }
  
  .card-content {
    padding: 0;
  }
}

.transfer-form {
  padding: 32px;
  
  .section {
    margin-bottom: 40px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .section-title {
      display: flex;
      align-items: center;
      gap: 8px;
      color: $primary-color;
      font-size: 18px;
      font-weight: 500;
      margin: 0 0 16px 0;
      
      mat-icon {
        font-size: 20px;
        width: 20px;
        height: 20px;
      }
    }
    
    mat-divider {
      margin-bottom: 24px;
      border-color: lighten($primary-color, 60%);
    }
  }
  
  .form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 16px;
    }

    .form-field {
      width: 100%;
    }

    .full-width {
      grid-column: 1 / -1;
    }
  }

  .form-grid-3-columns {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;

    @media (max-width: 1200px) {
      grid-template-columns: repeat(2, 1fr);
    }

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 16px;
    }

    .form-field {
      width: 100%;
    }

    .full-width {
      grid-column: 1 / -1;
    }
  }
  
  .form-actions {
    display: flex;
    gap: 16px;
    justify-content: flex-start;
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid lighten($primary-color, 60%);
    
    @media (max-width: 768px) {
      flex-direction: column;
      gap: 12px;
    }
    
    button {
      min-width: 160px;
      height: 44px;
      border-radius: $border-radius;
      font-weight: 500;
      text-transform: none;
      
      @media (max-width: 768px) {
        min-width: 100%;
      }
      
      mat-icon {
        margin-right: 8px;
      }
      
      mat-spinner {
        margin-right: 8px;
      }
      
      &.submit-btn {
        @include button-style($primary-color);
      }
      
      &.draft-btn {
        border-color: $accent-color;
        color: $accent-color;
        
        &:hover:not(:disabled) {
          background-color: lighten($accent-color, 45%);
        }
      }
      
      &.reset-btn {
        color: $error-color;
        
        &:hover:not(:disabled) {
          background-color: lighten($error-color, 45%);
        }
      }
    }
  }
}

// Status Classes
.status-pending {
  @include status-badge($warning-color);
}

.status-approved {
  @include status-badge($info-color);
}

.status-completed {
  @include status-badge($success-color);
}

.status-rejected {
  @include status-badge($error-color);
}

.status-cancelled {
  @include status-badge(lighten($primary-color, 20%));
}

// Priority Classes
.priority-low {
  @include priority-badge($info-color);
}

.priority-medium {
  @include priority-badge($warning-color);
}

.priority-high {
  @include priority-badge($error-color);
}

.priority-urgent {
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  background-color: $error-color;
  color: white;
}

// Form Field Styles
.transfer-form-field {
  width: 100%;
  
  &.mat-form-field-appearance-outline {
    .mat-form-field-outline {
      color: lighten($primary-color, 40%);
    }
    
    &.mat-focused .mat-form-field-outline-thick {
      color: $primary-color;
    }
    
    .mat-form-field-label {
      color: $primary-color;
    }
    
    &.mat-focused .mat-form-field-label {
      color: $primary-color;
    }
  }
  
  .mat-form-field-suffix mat-icon {
    color: lighten($primary-color, 20%);
  }
  
  .mat-hint {
    color: lighten($primary-color, 30%);
    font-size: 12px;
  }
  
  .mat-error {
    color: $error-color;
    font-size: 12px;
  }
}

// Required Field Asterisk
.required-asterisk {
  color: $error-color;
  font-weight: bold;
  margin-left: 2px;
}

// Global override to prevent any automatic asterisks in transfer module
.transfer-form {
  ::ng-deep {
    .mat-mdc-form-field .mat-mdc-floating-label::after,
    .mat-form-field-label::after,
    mat-label::after,
    .mdc-floating-label::after,
    .mat-form-field .mat-form-field-label::after,
    .mat-form-field-required-marker::after,
    [required]::after,
    .ng-invalid::after,
    .mat-mdc-form-field[ng-reflect-required="true"] .mat-mdc-floating-label::after,
    .mat-form-field[ng-reflect-required="true"] .mat-form-field-label::after {
      content: none !important;
      display: none !important;
    }
  }
}

// Override global required-label asterisk to prevent duplicates
.mat-mdc-form-field .mat-mdc-floating-label.required-label::after,
.mat-form-field-label.required-label::after,
.required-label::after,
mat-label.required-label::after,
.mat-mdc-form-field mat-label::after,
mat-form-field mat-label::after,
.mat-mdc-form-field .mat-mdc-floating-label::after,
.mat-form-field-label::after,
mat-label::after,
.mat-mdc-floating-label::after,
.mdc-floating-label::after,
.mat-form-field .mat-form-field-label::after,
.mat-form-field-required-marker,
.mat-form-field-required-marker::after {
  content: none !important;
  display: none !important;
}

// Animation Classes
.transfer-fade-in {
  animation: transferFadeIn 0.3s ease-out;
}

@keyframes transferFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive Utilities
@media (max-width: 1024px) {
  .transfer-container {
    padding: 16px;
  }
  
  .transfer-form {
    padding: 24px;
  }
}

@media (max-width: 768px) {
  .transfer-container {
    padding: 12px;
  }
  
  .transfer-form {
    padding: 20px;
  }
  
  .transfer-card .card-header {
    padding: 20px;
    
    mat-card-title h1 {
      font-size: 20px;
    }
  }
  
  .transfer-form .section .section-title {
    font-size: 16px;
  }
}

// Snackbar Styles (Global)
::ng-deep {
  .success-snackbar {
    background-color: $success-color !important;
    color: white !important;
  }
  
  .error-snackbar {
    background-color: $error-color !important;
    color: white !important;
  }
  
  .warning-snackbar {
    background-color: $warning-color !important;
    color: white !important;
  }
  
  .info-snackbar {
    background-color: $info-color !important;
    color: white !important;
  }
}
