import { CommonModule } from '@angular/common';
import { Component, Inject } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { MatOptionModule, MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatGridListModule } from '@angular/material/grid-list';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatSelectModule } from '@angular/material/select';
import { MatTableModule } from '@angular/material/table';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatTooltipModule } from '@angular/material/tooltip';
interface Holiday {
  date: string;
  day: string;
  name: string;
}
@Component({
  selector: 'app-month-calender-dialog',
  imports: [CommonModule, MatCardModule, MatGridListModule,
    MatDialogModule, MatTableModule,MatOptionModule, MatNativeDateModule, MatDatepickerModule, MatFormFieldModule, MatIconModule, MatInputModule, MatProgressBarModule, MatSelectModule, MatToolbarModule, MatTooltipModule],
  templateUrl: './month-calender-dialog.component.html',
  styleUrl: './month-calender-dialog.component.scss'
})
export class MonthCalenderDialogComponent {
  startDate: Date;
  holidayDates: number[];

  constructor(
    public dialogRef: MatDialogRef<MonthCalenderDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { month: number, year: number, holidays: Holiday[], monthName: string }
  ) {
    this.startDate = new Date(data.year, data.month, 1);
    this.holidayDates = data.holidays.map(h => parseInt(h.date, 10));
    data.monthName = new Date(data.year, data.month, 1).toLocaleString('default', { month: 'long' });
  }

  dateClass() {
    return (date: Date): string => {
      if (date.getMonth() === this.data.month && this.holidayDates.includes(date.getDate())) {
        return 'holiday-date';
      }
      return '';
    };
  }

  closeDialog(): void {
    this.dialogRef.close();
  }
}
