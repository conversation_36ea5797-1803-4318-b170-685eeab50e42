/* Import Angular Material theme */

// Define custom theme colors


/* General container styles */
.pending-leaves-container {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
  font-family: 'Roboto', sans-serif;
}

/* Header Card */
.header-card {
  background: linear-gradient(135deg, #203664 0%, darken(#203664, 10%) 100%);
  color: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
  transition: transform 0.3s ease;

  &:hover {
    transform: translateY(-4px);
  }

  .form-header {
    padding: 16px;
    display: flex;
    align-items: center;

    .header-content {
      display: flex;
      align-items: center;
      width: 100%;
    }

    .logo-section {
      margin-right: 16px;

      .tncsc-logo {
        font-size: 40px;
        width: 40px;
        height: 40px;
        color: white;
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .title-section {
      .form-subtitle {
        font-size: 24px;
        font-weight: 500;
        margin: 0;
      }

      .subtitle {
        font-size: 14px;
        opacity: 0.8;
        margin: 4px 0 0;
      }
    }
  }
}

/* Table Card */
.table-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  background-color: white;
  padding: 16px;

  .search-field {
    width: 100%;
    max-width: 400px;
    margin-bottom: 16px;

    .mat-form-field-outline {
      background-color: white;
      border-radius: 8px;
    }

    input {
      font-size: 16px;
    }

    mat-icon {
      color: #203664;
    }
  }

  /* Loading Spinner */
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 32px;
    color: #203664;

    mat-spinner {
      margin-bottom: 16px;
    }

    p {
      font-size: 16px;
      font-weight: 500;
    }
  }

  /* Table Styles */
  .table-container {
    overflow-x: auto;

    .leave-table {
      width: 100%;
      border-collapse: collapse;

      th {
        background-color: #203664;
        color: white;
        font-weight: 600;
        padding: 12px;
        text-align: left;
      }

      td {
        padding: 12px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        color: #203664;
      }

      /* Row hover effect */
    //   tr {
    //     transition: background-color 0.2s ease;

    //     &.row-pending {
    //       background-color: lighten(#ff9800, 45%);
    //     }

    //     &.row-approved {
    //       background-color: lighten(#4caf50, 45%);
    //     }

    //     &.row-rejected {
    //       background-color: lighten(#f44336, 45%);
    //     }

    //     &:hover {
    //       background-color: rgba(0, 0, 0, 0.05);
    //     }
    //   }

      /* Leave Type Chip */
    //   .leave-type-chip {
    //     background-color: #3f51b5;
    //     color: white;
    //     font-size: 12px;
    //     padding: 4px 12px;
    //     border-radius: 16px;
    //   }

      /* Date Cell */
      .date-cell {
        display: flex;
        align-items: center;
        gap: 8px;

        .date-icon {
          color: #3f51b5;
        }

        .session-info {
          color: #333333;
          font-size: 12px;
        }
      }

      /* Reason Text */
      .reason-text {
        display: inline-block;
        max-width: 200px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      /* Status Chip */
      .mat-chip {
        font-size: 12px;
        padding: 10px 24px;
        border-radius: 26px;

        &.PENDING {
          background-color: #ff9800;
          color: white;
        }

        &.APPROVED {
          background-color: #4caf50;
          color: white;
        }

        &.REJECTED {
          background-color: #f44336;
          color: white;
        }

        mat-icon {
          font-size: 16px;
          width: 16px;
          height: 16px;
          margin-right: 4px;
        }
      }

      /* Action Buttons */
      .action-buttons {
        display: flex;
        gap: 8px;

        button {
          transition: background-color 0.2s ease;

          &:hover {
            background-color: rgba(#3f51b5, 0.1);
          }
        }
      }
    }
  }

  /* No Data Message */
  .no-data {
    text-align: center;
    padding: 32px;
    color: #203664;

    mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      color: #3f51b5;
      margin-bottom: 16px;
    }

    h3 {
      font-size: 20px;
      margin: 0 0 8px;
    }

    p {
      font-size: 14px;
      opacity: 0.7;
    }
  }
}

/* Summary Card */
.summary-card {
  margin-top: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  background-color: white;

  .summary-content {
    display: flex;
    gap: 24px;
    padding: 16px;

    .summary-item {
      display: flex;
      align-items: center;
      gap: 12px;

      mat-icon {
        font-size: 32px;
        width: 32px;
        height: 32px;
      }

      .summary-text {
        h4 {
          font-size: 20px;
          font-weight: 600;
          margin: 0;
          color: #333333;
        }

        p {
          font-size: 14px;
          margin: 4px 0 0;
          color: rgba(#333333, 0.7);
        }
      }
    }
  }
}

/* Responsive Design */
@media (max-width: 600px) {
  .pending-leaves-container {
    padding: 16px;
  }

  .header-card {
    .form-header {
      flex-direction: column;
      align-items: flex-start;

      .logo-section {
        margin-bottom: 12px;
      }
    }
  }

  .table-card {
    .search-field {
      max-width: 100%;
    }

    .leave-table {
      th, td {
        padding: 8px;
        font-size: 14px;
      }
    }

    .summary-content {
      flex-direction: column;
      gap: 16px;
    }
  }
}