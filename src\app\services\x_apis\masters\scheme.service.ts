import { inject, Injectable } from '@angular/core';
import { DataService } from '../../data.service';
import { Response } from '../../../models/x_models/api-response';
import { Scheme, SchemeResponse } from '../../../models/x_models/masters/scheme';

@Injectable({
    providedIn: 'root'
})
export class SchemeService {
    dataService = inject(DataService)

    create(data: Scheme) {
        return this.dataService.post<Response>("/scheme", data)
    }

    get() {
        return this.dataService.get<SchemeResponse>("/scheme")
    }

    getById(id: number) {
        return this.dataService.get<Scheme>(`/scheme/${id}`)
    }

    delete(id: number) {
        return this.dataService.delete<Response>(`/scheme/${id}`)
    }
}
