<mat-toolbar color="primary" class="navbar">
  <nav aria-label="Breadcrumb navigation" style="display: flex; align-items: center; flex-wrap: wrap;">
    <a routerLink="/" class="crumb">{{'Master' | translate}}</a>
    <mat-icon>chevron_right</mat-icon>
    <a class="crumb" (click)="goBack()">{{menuService.activeMenu()?.title}}</a>
    <mat-icon>chevron_right</mat-icon>
    <span aria-current="page" class="nav-active">
      @if(uniqueId() == 0) {
      {{'New' | translate}} {{menuService.activeMenu()?.title}}
      } @else {
        {{procurementRate()?.productName}}
      }
    </span>
    @if(mode() === "view") {
    <button (click)="onEdit()" title="edit" class="btn-edit btn-icon-unstyled">
      <mat-icon class="material-symbols-rounded">edit_square</mat-icon>
    </button>
    }
  </nav>
  <button mat-flat-button color="primary" (click)="goBack()" aria-label="Go back" class="back-btn">
    <mat-icon>arrow_back_ios</mat-icon>
    Back
  </button>
</mat-toolbar>


  <div class="card component">
  
    <form [formGroup]="form" (ngSubmit)="onSubmit()">
      <div class="form">

        <div class="field">
          <label for="productId" class="required-label">{{'Product' | translate}}</label>
          <mat-form-field appearance="outline">
            <mat-select id="productId" formControlName="productId" (openedChange)="resetSearch('product')">
              <mat-form-field class="select-search hide-subscript" appearance="outline">
                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                <input #search1 autocomplete="off" matInput placeholder="Search" (input)="filter($event,'product')">
              </mat-form-field>
              @for (role of filteredProducts(); track role) {
              <mat-option [value]="role.key">{{role.value}}</mat-option>
              }
            </mat-select>
            <mat-error>
              @if(form.controls.productId.errors?.['required']) {
              <!-- Product is required -->
              {{'Product' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>


        <div class="field">
          <label for="regionId" class="required-label">{{'Variety' | translate}}</label>
          <mat-form-field appearance="outline">
            <mat-select id="regionId" formControlName="varietyId" (openedChange)="resetSearch('variety')">
              <mat-form-field class="select-search hide-subscript" appearance="outline">
                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                <input #search2 autocomplete="off" matInput placeholder="Search" (input)="filter($event,'variety')">
              </mat-form-field>
              @for (variety of filteredVarieties(); track variety) {
              <mat-option [value]="variety.key">{{variety.value}}</mat-option>
              }
            </mat-select>
            <mat-error>
              @if(form.controls.varietyId.errors?.['required']) {
              <!-- Variety is required -->
              {{'Variety' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>


        <div class="field">
          <label for="minRange" class="required-label">{{'Price' | translate}}</label>
          <mat-form-field>
            <input id="minRange" formControlName="price" matInput (input)="onInput($event,'/^[0-9.]*$/')"
              maxlength="10">
            <mat-error>
              @if(form.controls.price.errors?.['required']) {
              <!-- Price is required -->
              {{'Price' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="maxRange" class="required-label">{{'BonusPrice' | translate}}</label>
          <mat-form-field>
            <input id="maxRange" formControlName="bonusPrice" matInput (input)="onInput($event,'/^[0-9.]*$/')"
              maxlength="10">
            <mat-error>
              @if(form.controls.bonusPrice.errors?.['required']) {
              <!-- Bonus Price is required -->
              {{'BonusPrice' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="priceCutAmount" class="required-label">{{'GradeCut' | translate}}</label>
          <mat-form-field>
            <input id="priceCutAmount" formControlName="gradeCut" matInput (input)="onInput($event,'/^[0-9.]*$/')"
              maxlength="10">
            <mat-error>
              @if(form.controls.gradeCut.errors?.['required']) {
              <!-- Grade cut amount is required -->
              {{'GradeCut' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="fromDate" class="required-label">{{'FromDate' | translate}}</label>
          <mat-form-field>
            <input matInput [matDatepicker]="picker1" formControlName="fromDate">
            <mat-datepicker-toggle matIconSuffix [for]="picker1"></mat-datepicker-toggle>
            <mat-datepicker #picker1></mat-datepicker>
            <mat-error>
              @if(form.controls.fromDate.errors?.['required']) {
              <!-- From date is required -->
              {{'FromDate' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="toDate" class="required-label">{{'ToDate' | translate}}</label>
          <mat-form-field>
            <input matInput [matDatepicker]="picker2" formControlName="toDate" [min]="minToDate()">
            <mat-datepicker-toggle matIconSuffix [for]="picker2"></mat-datepicker-toggle>
            <mat-datepicker #picker2></mat-datepicker>
            <mat-error>
              @if(form.controls.toDate.errors?.['required']) {
              <!-- To date is required -->
              {{'ToDate' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="seasonId" class="required-label">{{'Season' | translate}}</label>
          <mat-form-field appearance="outline">
            <mat-select id="seasonId" formControlName="seasonId" (openedChange)="resetSearch('season')">
              <mat-form-field class="select-search hide-subscript" appearance="outline">
                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                <input #search3 autocomplete="off" matInput placeholder="Search" (input)="filter($event,'season')">
              </mat-form-field>
              @for (role of filteredSeasons(); track role) {
              <mat-option [value]="role.key">{{role.value}}</mat-option>
              }
            </mat-select>
            <mat-error>
              @if(form.controls.seasonId.errors?.['required']) {
              <!-- Season is required -->
              {{'Season' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>






      </div>

      <div class="actions">
       
        @if(editable() === true) {
        @if(mode() === 'add') {
        <button type="button" mat-flat-button (click)="formReset()" class="btn btn-danger">{{'Reset' |
          translate}}</button>
        }
        <button [disabled]="editable() === false" mat-flat-button class="btn btn-theme" type="submit">{{uniqueId() == 0
          ? ('Create' | translate) : ('Update' | translate)}}</button>
        }
      </div>


    </form>
  </div>
