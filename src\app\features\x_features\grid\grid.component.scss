.component {
  padding: 20px 20px 0 20px;
  // border-radius: 10px;
  // margin-top: 12px;
  // min-height: calc(100% - 145px);
  // background-color: #FFFFFF;
  max-width: max-content;

  @media (max-width: 700px) {
    // background-color: #F6F6F6;
    // padding: 20px;
    max-width: 100%;
    min-height: 100%;
  }

  .page-header {
    display: flex;
    flex-direction: row;
    gap: 10px;
    justify-content: space-between;
    align-items: center;
    padding: 10px 10px;
    flex-wrap: wrap;
    h1 {
        display: flex;
        margin-right: auto;
    }

    @media (max-width: 700px) {
        h1 {
            order: 0;
        }
    }
}

  .header {
    padding-left: 9px;
    @media (max-width: 700px) {
      padding-left: 0;
    }

    .filters-wrapper {
      // max-width: 615px;
      display: flex;
      align-items: center;



      .search {
        max-width: calc(100% - 26px);
        width: 100%;
        //margin-top: 20px;
        --form-field-bg: #fff;

        .icon-search {
          width: 15px;
          height: 22px;
          padding-inline: 17px 13px;
          color: var(--theme-80);
        }
      }

      .btn-info {
        margin-left: 10px;
      }

      .filters-more {
        position: relative;
        display: flex;

        .btn-filter {
          background-color: #fff;
          border: 1.5px solid var(--grey-20);
          border-radius: 8px;
          margin-left: 15px;
          width: fit-content;
          font-size: 12px;
          display: flex;
          gap: 30px;
          --btn-icon-radius: 8px;

          span {
            z-index: 1;

            @media (max-width: 700px) {
              display: none;
            }
          }

          .icon-filter {
            display: flex;
            justify-content: center;
            align-items: center;
            //padding-inline: 17px 13px;
            color: var(--theme-80);
          }
        }
      }

    }
  }

  .content {
    width: max-content;
    max-width: 100%;
    margin-top: 10px;

    @media (max-width: 700px) {
      width: 100%;
      margin-top: 20px;
    }

    .table-wrapper, .mobile-wrapper {
      background-color: #FFFFFF;
      border-radius: 8px;
    }

    .table-wrapper {
      @media (max-width: 700px) {
        display: none;
      }
    }

    .mobile-wrapper {
      display: none;
      padding: 17px 20px 0px 20px;
      --mdc-checkbox-state-layer-size: 20px;

      @media (max-width: 700px) {
        display: block
      }

      .mobile-header {
        display: flex;
        justify-content: space-between;
        padding-bottom: 2px;

        .col-left {
          .checkbox-label {
            font-size: 10px;
            font-weight: 700;
            color: #000;
            padding-left: 15px;
          }
        }

        .col-right {
          display: flex;
          gap: 10px;
          align-items: center;

          .btn-add {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 24px;
            width: 24px;
            border-radius: 8px;
            // background-color: var(--theme-10);
            color: var(--grey-100);
            cursor: pointer;
            padding: 0;
            border: 1.6px solid var(--theme-80);
            background: transparent;

            mat-icon {
              height: 12px;
            }
          }

          label {
            font-size: 14px;
            margin-bottom: 0;
            cursor: pointer;
          }
        }
      }


      .un-card {
        display: grid;
        grid-template-columns: 39px 1fr 36px;
        padding-block: 8px 15px;
        border-bottom: 1px solid #e9e9e9;

        .select {
          mat-checkbox {
            margin-top: 7px;
          }
        }

        .desc {
          .quote-no {
            font-size: 12px;
            margin-top: 12px;
            line-height: 16px;
            display: flex;
            justify-content: space-between;
            width: 100%;

            .cost {
              font-weight: 700;
            }
          }

          .tracking-no {
            font-size: 10px;
            font-weight: 500;
            margin-top: 8px;
          }
        }

        .actions {
          .btn {
            margin-top: 2px;
          }
        }
      }
    }

  }
}

table {
  tr {
    grid-template-columns: 42px 146px 178px 130px 154px 127px;
    min-width: 100%;
  }

  th:first-child, td:first-child {
    padding: 0px;
  }

  .pill {
    width: 99px;
    height: 23px;
    padding: 0px 31px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 25px;
    color: #000;

    &.quote {
      background-color: var(--grey-10);
    }

    &.invoiced {
      background-color: var(--theme-40);
    }
  }

  .actions {
    padding-inline: 0;

    .btn {
      --mdc-icon-button-state-layer-size: 24px;
      --mat-icon-button-state-layer-color: var(--theme-40);
      display: flex;
      justify-content: center;
      padding-inline: 0;

      &:has(.file-lines) {
        margin-right: 3px;
      }

      mat-icon {
        width: 13.5px;
      }

      mat-icon.file-lines {
        width: 15px;
      }
    }

    .btn-theme {
      color: var(--theme-80);

      &[disabled] {
        color: #E7E8E9;
      }
    }
  }
}

:host ::ng-deep .btn-assign-card svg {
  width: 15px;
  height: 13px;
}

.filters {
  position: absolute;
  top: 53px;
  right: -10px;
  //max-width: 89%;
  background: white;
  width: 300px;
  box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.25), 0 6px 14px -6px rgba(19, 25, 39, 0.12);
  border-radius: 8px;
  overflow: clip;
  animation: fade-in-up .1s ease-out;
  z-index: 1;

  .filters-header {
    display: flex;
    justify-content: space-between;
    padding: 10px 10px 10px 20px;
    align-items: center;
    background: var(--theme-10);

    h3 {
      font-size: 14px;
      font-weight: 700;
      line-height: 20px;
      // color: #fff;
    }

    .btn-close {
      padding: 0;
      width: 24px;
      height: 25px;
    }
  }

  .filters-body {
    padding: 10px 20px 10px 20px;
  }

  .filters-actions {
    padding: 20px 20px 20px 20px;
    display: flex;
    justify-content: space-between;

    .btn {
      height: 35px;
    }
  }
}

.mobile-wrapper .status {
  font-size: 10px;
  font-weight: 700;
  line-height: 14px;
  text-transform: uppercase;
  margin-top: 4px;

  &[data-status="Invoiced"] {
    color: var(--theme-70);
  }
}

:host ::ng-deep .btn-close svg {
  width: 16px;
  height: 16px;
}

:host ::ng-deep .icon-filter svg {
  width: 15px;
  height: 9px;
}
