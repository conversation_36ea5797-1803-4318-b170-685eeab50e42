import { inject, Injectable } from '@angular/core';
import { DataService } from '../../data.service';
import { Response } from '../../../models/x_models/api-response';
import { FarmerResponse, Farmers } from '../../../models/x_models/farmer/farmer';
import { LandDetails } from '../../../models/x_models/farmer/land';

@Injectable({
    providedIn: 'root'
})
export class LandService {
    dataService = inject(DataService)

    create(data: Farmers) {
        return this.dataService.post<Response>("/land", data)
    }

    get() {
        return this.dataService.get<FarmerResponse>("/land")
    }

    getById(id: number) {
        return this.dataService.get<Farmers>(`/land/${id}`)
    }

    delete(id: number) {
        return this.dataService.delete<Response>(`/land/${id}`)
    }

    LandDetailsApproval(farmerId:number,villageId:number)
    {
        return this.dataService.get<LandDetails>(`/land/detail/${farmerId}/${villageId}`)
    }

    VaoApproval(data:any)
    {
        return this.dataService.post('/land/saveapproval',data)
    }

    getBase64(data:any)
    {
        return this.dataService.get(`/land/attachment/${data}`)
    }
}
