import { NgTemplateOutlet, DatePipe } from '@angular/common';
import { Component, computed, CUSTOM_ELEMENTS_SCHEMA, effect, inject, linkedSignal, OnInit, resource, signal, viewChild } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInput, MatPrefix } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { StatusType } from '../../../../enums/x_enums/dotnet/status-type';
import { AppRoutes } from '../../../../enums/app-routes';
import { confirmAndDelete } from '../../../../models/x_models/masters/shared';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { ProcuretokenService } from '../../../../services/x_apis/procurement/procuretoken.service';
import { Procuretokens } from '../../../../models/x_models/procurement/procuetoken';
import { MatDialog } from '@angular/material/dialog';
import { TokendetailsComponent } from './tokendetails.component';

@Component({
  selector: 'app-procuretoken',
  imports: [MatIcon,
    MatInput,
    MatPrefix,
    ReactiveFormsModule,
    MatPaginatorModule,
    MatTableModule,
    NgTemplateOutlet,
    MatButtonModule,
    MatMenuModule,
    DatePipe,
    MatSortModule,
    MatFormFieldModule,
    TokendetailsComponent,
    TranslateModule],
  providers: [DatePipe],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './procuretoken.component.html',
  styleUrl: './procuretoken.component.scss'
})
export class ProcuretokenComponent {
  menuService = inject(MenuService);
  access = computed(() => this.menuService.activeMenu()?.controlAccess);
  translate = inject(TranslateService);

  paginator = viewChild<MatPaginator>(MatPaginator);
  sort = viewChild<MatSort>(MatSort);

  procuretokenService = inject(ProcuretokenService)
  procuretokenResource = resource({ loader: () => this.procuretokenService.get() });
  dataSource = linkedSignal(() => new MatTableDataSource(this.procuretokenResource.value()));

  loading = signal(false);
  mode = signal<'view' | 'edit' | 'add'>('add');
  search = new FormControl("");
  showGrid = signal(false);
  isPopupVisible = signal(false);
  isModalVisible = signal(false);
  status = StatusType;

  procureToken = signal<Procuretokens>({
    id: 0,
    tokenNo: '',
    tokenDate: '',
    dpcNameCode: '',
    farmerNameCode: '',
    productName: '',
    noofBags: 0,
    requestedTypeName: '',
    noofProcuredBags: 0,
    procurementStatusTypeName: ''
  });

  displayedColumns: string[] = [
    'dpcNameCode',
    'tokenNo',
    'tokenDate',
    'farmerNameCode',
    'productName',
    'noofBags',
    'actions'
  ];
  dialog = inject(MatDialog);

  protected readonly AppRoutes = AppRoutes;  // Need clarification

  constructor(private datePipe: DatePipe) {
    effect(() => {
      if (this.paginator()) {
        this.dataSource().sort = this.sort()!;
        this.dataSource().paginator = this.paginator()!

        this.dataSource().filterPredicate = (data: Procuretokens, filter: string) => {
          const formattedFromDate = this.datePipe.transform(data.tokenDate, 'dd-MM-yyyy') ?? '';
          const combinedData = `${data.dpcNameCode} ${formattedFromDate} ${data.tokenNo} ${data.farmerNameCode} ${data.productName} ${data.noofBags}`.toLowerCase();
          return combinedData.includes(filter);
        };
      }
    });
  }

  ngOnInit(): void {
  }

  onAdd() {
    this.procureToken.set({
      id: 0,
      tokenNo: '',
      tokenDate: '',
      dpcNameCode: '',
      farmerNameCode: '',
      productName: '',
      noofBags: 0,
      requestedTypeName: '',
      noofProcuredBags: 0,
      procurementStatusTypeName: ''
    });
    this.mode.set("add")
    this.showGrid.set(true)
  }

  onView(data: Procuretokens) {
    // this.showPopup();
    // this.openDialog();
    this.procureToken.set(data);
    this.mode.set('view');
    this.showGrid.set(true)
  }

  onEdit(data: Procuretokens) {
    this.procureToken.set(data);
    this.mode.set('edit');
    this.showGrid.set(true)
  }

  onClose() {
    this.showGrid.set(false)
    this.procuretokenResource.reload();
    this.search.setValue("");
  }

  onRefresh() {
    this.procuretokenResource.reload();
    this.search.setValue("");
  }

  async onDelete(vendor: Procuretokens) {
    await confirmAndDelete(vendor, vendor.tokenNo, 'Procurement Token', this.procuretokenService, () => this.procuretokenResource.reload());
  }

  onSearch() {
    const filterValue = this.search.value?.trim().toLowerCase() || '';
    this.dataSource().filter = filterValue;
  }

  showPopup() {
    debugger
    this.isPopupVisible.set(true);
  }

  hidePopup() {
    this.isPopupVisible.set(false);
  }

  showModal() {
    this.isModalVisible.set(true);
  }

  hideModal() {
    this.isModalVisible.set(false);
  }


  openDialog() {
    const dialogRef = this.dialog.open(TokendetailsComponent);

    dialogRef.afterClosed().subscribe(result => {
      console.log(`Dialog result: ${result}`);
    });
  }

}
