import { Component } from '@angular/core';
import { FormGroup, FormBuilder, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { LeaveManagementService } from '../../../../services/m_apis/service/leave-management.service';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatOptionModule, MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTableModule } from '@angular/material/table';

@Component({
  selector: 'app-leave-balance-add',
  imports: [CommonModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatTableModule,
    MatSelectModule,
    MatOptionModule,
    MatDatepickerModule,
    MatNativeDateModule,
    FormsModule,
    ReactiveFormsModule,
    MatProgressSpinnerModule,
    MatProgressBarModule,
    MatIconModule,
    MatSnackBarModule],
  templateUrl: './leave-balance-add.component.html',
  styleUrl: './leave-balance-add.component.scss'
})
export class LeaveBalanceAddComponent {
  leaveBalanceForm: FormGroup;
  leaveTypes: any[] = [];
  errorMessage: string = '';
  successMessage: string = '';

  constructor(private fb: FormBuilder, private leaveService: LeaveManagementService) {
    this.leaveBalanceForm = this.fb.group({
      employeeId: ['', Validators.required],
      leaveTypeId: ['', Validators.required],
      year: [2025, [Validators.required, Validators.min(2020)]],
      initialBalance: [0, [Validators.required, Validators.min(0)]]
    });
  }

  ngOnInit(): void {
    this.getLeaveTypes();
  }

  getLeaveTypes(): void {
    this.leaveService.getAllLeaveTypes().subscribe({
      next: (data) => {
        this.leaveTypes = data;
      },
      error: (err) => {
        console.error(err);
      }
    });
  }

  onSubmit(): void {
    if (this.leaveBalanceForm.valid) {
      this.leaveService.createLeaveBalance(this.leaveBalanceForm.value).subscribe({
        next: () => {
        
          this.successMessage = 'Leave balance created successfully';
          this.errorMessage = '';
          // this.leaveBalanceForm.reset({ year: 2025, initialBalance: 0 });
          this.leaveBalanceForm.reset();
        },
        error: (err) => {
          this.errorMessage = err.error?.message || 'Failed to create leave balance';
          this.successMessage = '';
        }
      });
    }
  }
}
