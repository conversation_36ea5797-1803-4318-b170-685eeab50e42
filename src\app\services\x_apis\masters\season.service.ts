import { inject, Injectable } from '@angular/core';
import { DataService } from '../../data.service';
import { CreateUserPayload, UserResponse, UserByIdResponse, DeleteUserPayload } from '../../../models/x_models/user';
import { Season, Seasons } from '../../../models/x_models/masters/season';
import { Response } from '../../../models/x_models/api-response';

@Injectable({
  providedIn: 'root'
})
export class SeasonService {

  dataService = inject(DataService)

  create(data: Season) {
    return this.dataService.post<Response>("/season", data)
  }

  get() {
    return this.dataService.get<Seasons>("/season")
  }

  getById(id: number) {
    return this.dataService.get<Season>(`/season/${id}`)
  }

  delete(id: number) {
    return this.dataService.delete<Response>(`/season/${id}`)
  }

}
