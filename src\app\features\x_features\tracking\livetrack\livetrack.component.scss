table {
  tr {
    grid-template-columns: minmax(150px, 200px) minmax(200px, 250px) minmax(200px, 250px) minmax(145px, 160px) minmax(80px, 110px) 164px;
    min-width: 100%;
  }
}

mat-form-field {
  max-width: 100%;
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  gap: 1rem;
}

.row-8 {
  grid-column: span 9;
}

.row-4 {
  grid-column: span 3;
}


.mat-mdc-form-field {
  width: 100%;
}

.actions {
  flex: 8;
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.card-container {
  background-color: white;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.card-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.text-content {
  flex: 1;
  padding-right: 16px;
}

@media (max-width: 768px) {
  .grid-container {
    grid-template-columns: repeat(12, 1fr);
  }

  .row-8 {
    grid-column: span 12;
    text-align: left;
  }

  .row-4 {
    grid-column: span 12;
  }

  .actions {
    justify-content: flex-start;
  }

  .card-container {
    grid-template-columns: 1fr;
  }
}



.field mat-form-field {
  width: 100%;
  max-width: 100%;
}


mat-card {
  display: flex;
  flex-direction: column;
}



.card-image {
  width: 100px;
  height: auto;
  object-fit: cover;
}

.form-container1 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.field1 {
  flex: 5;
}



.status-container {
  display: inline-flex;
  align-items: center;
}

.status-icon {
  width: 30px;
  height: 30px;
  margin-right: 8px;
}

.tracking {
  width: 30px;
  height: 30px;
}