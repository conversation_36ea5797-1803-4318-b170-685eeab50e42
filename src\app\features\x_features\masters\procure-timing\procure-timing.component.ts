import { Component, inject, input, output, resource, signal } from '@angular/core';
import { Form<PERSON><PERSON>er, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButton } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormField, MatInput, MatError } from '@angular/material/input';
import { MatOption, MatSelect } from '@angular/material/select';
import { Router } from '@angular/router';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { DateInputDirective } from '../../../../directives/date-input.directive';
import { MatNativeDateModule } from '@angular/material/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { LookupService } from '../../../../services/x_apis/lookup.service';
import { AlertService } from '../../../../services/alert.service';
import { InputformatService } from '../../../../services/x_apis/inputformat.service';
import { ProcureTimingService } from '../../../../services/x_apis/masters/procure-timing.service';
import { ProcureTiming } from '../../../../models/x_models/masters/procure-timing';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import {MatTimepickerModule} from '@angular/material/timepicker';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { MatToolbarModule } from '@angular/material/toolbar';

@Component({
  selector: 'app-procure-timing',
  imports: [FormsModule,
    MatFormField,
    MatInput,
    ReactiveFormsModule,
    MatError,
    MatOption,
    MatSelect,
    MatButton,
    MatIconModule,
    MatDatepickerModule,
    DateInputDirective,
    MatNativeDateModule,
    MatTimepickerModule,
    MatToolbarModule,
    TranslateModule,
    MatFormFieldModule],
  templateUrl: './procure-timing.component.html',
  styleUrl: './procure-timing.component.scss'
})
export class ProcureTimingComponent {
   menuService = inject(MenuService);
  lookupService = inject(LookupService);
  procureTimingService = inject(ProcureTimingService);
  alertService = inject(AlertService);
  inputFormatService = inject(InputformatService);
  translate = inject(TranslateService);

  seasons = resource({ loader: () => this.lookupService.getSeason() }).value;

  mode = input.required<string>();
  closed = output<boolean>();
  fb = inject(FormBuilder);
  editable = signal<boolean>(true);
  router = inject(Router);

  uniqueId = input.required<any>();
  procuretiming = signal<ProcureTiming | null>(null);

  form = this.fb.group({
    id: this.fb.control<number>(0),
    noOfBags: this.fb.control<number | null>(null, Validators.required),
    startTime:this.fb.control<string | null>(null, Validators.required),
    endTime:this.fb.control<string | null>(null, Validators.required),
  });

  ngOnInit() {
    if (this.mode() === "edit" || this.mode() === "view") {
      this.getFormData()
    }
  }

  async getFormData() {
    const res = await this.procureTimingService.getById(this.uniqueId());
    this.procuretiming.set(res)
    this.form.patchValue({ ...res });
    if (this.mode() === "view") {
      this.editable.set(false)
      this.form.disable()
    }
  }

  onEdit() {
    this.form.enable();
    this.editable.set(true)
  }

  async onSubmit() {
    if (this.form.valid) {
      const formValue = { ...this.form.value };
      const res = await this.procureTimingService.create(formValue as ProcureTiming)
      if (res.isSuccess) {
        this.closed.emit(true)
        if (formValue.id == 0) {
          this.alertService.success(`Procurement Timing Created successfully.`)
        }
        else {
          this.alertService.success(`Procurement Timing Updated successfully.`)
        }
      }
    } else {
      this.form.markAllAsTouched();
    }
  }

  goBack() {
    this.closed.emit(true)
  }

  formReset() {
    let indexId = this.form.value.id;
    this.form.reset();
    this.form.patchValue({
      id: indexId
    });
  }

  onInput(event: any, regexPattern: any): void {
    this.inputFormatService.isNumericOrHyphenOrSpace(event, regexPattern);
  }

}
