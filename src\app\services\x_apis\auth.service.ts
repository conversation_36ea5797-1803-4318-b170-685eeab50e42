import {inject, Injectable} from '@angular/core';
import {DataService} from '../data.service';
import {LoginPayload, LogoutPayload, TokenReponse} from '../../models/x_models/auth';
import {CryptoService} from '../crypto.service';
import {SessionService} from '../session.service';
import { UserResponse } from '../../models/x_models/user';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  dataService = inject(DataService)
  cryptoService = inject(CryptoService);
  sessionService = inject(SessionService)

  constructor() { }

  async login(data: LoginPayload) {
    debugger
    data.password = this.cryptoService.encryptEncode(data.password);
    const res = await this.dataService.post<TokenReponse>("/auth/token", data)
    this.sessionService.create(res.responseData )
    return res
  }

  changePassword(data: any) {
    return this.dataService.post<any>("/auth/changepassword", data)
  }

  forgotPassword(data: any) {
    return this.dataService.post<any>("/auth/forgotpassword", data)
  }

  async userDetails()
  {
    debugger
   const res= this.dataService.get<UserResponse>("/user/detail");
   return res;
  }

  logout(data: any) {
    return this.dataService.post<any>("/auth/logout", data)
  }

  async logoutIfTokenExpired() {
    if (this.sessionService.isTokenExpired()) {
      let payload: LogoutPayload = {
        userId: this.sessionService.session()?.userId!
      }
      void this.logout(payload)
    }
  }
}
