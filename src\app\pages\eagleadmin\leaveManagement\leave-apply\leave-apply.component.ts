import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatOptionModule, MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { MatTableModule } from '@angular/material/table';
import { LeaveManagementService } from '../../../../services/m_apis/service/leave-management.service';
import { ActivatedRoute, Router } from '@angular/router';
import { LeaveApplicationRequest, LeaveBalanceDTO } from '../../../../enums/m_enums/leave.model';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
interface Approver {
  id: string;
  name: string;
}
interface Leave {
  leaveType: string;
  fromDate: Date;
  toDate: Date;
  fromSession: string;
  toSession: string;
  reason: string;
  approver: string;
  status: string;
}

@Component({
  selector: 'app-leave-apply',
  imports: [CommonModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatTableModule,
    MatSelectModule,
    MatOptionModule,
    MatDatepickerModule,
    MatNativeDateModule,
    FormsModule,
    ReactiveFormsModule,
    MatProgressSpinnerModule,
    MatProgressBarModule,
    MatIconModule,
    MatSnackBarModule],
  templateUrl: './leave-apply.component.html',
  styleUrl: './leave-apply.component.scss'
})
export class LeaveApplyComponent implements OnInit {
  leaveBalances: LeaveBalanceDTO[] = [];
  leaveTypes: any[] = [];
  calculatedDays: number | null = null;
  leaveForm: FormGroup;
  errorMessage: string  = '';
  successMessage: string  = '';
  activeView: 'apply' | 'pending' | 'history' = 'apply';
  approvers: Approver[] = [
    { id: '1', name: 'RM' },
  ];
  selectedFileName: string | null = null;
  selectedFile: File | null = null;

  displayedColumns: string[] = ['leaveType', 'fromDate', 'toDate', 'reason', 'status'];
  pendingLeaves:any[] = [];
  leaveHistory: any[] = [];
  employeeId: string | null = '';

  constructor(private fb: FormBuilder,private leaveService:LeaveManagementService,
    private route:ActivatedRoute, private router:Router,private snackBar: MatSnackBar

  ) {
    this.leaveForm = this.fb.group({
      leaveType: ['', Validators.required],
      fromDate: ['', Validators.required],
      toDate: ['', Validators.required],
      fromSession: [''],
      toSession: [''],
      approver: ['', Validators.required],
      reason: ['', Validators.required],
      attachment: [null]
    });
  }

  ngOnInit(): void {
    this.employeeId = 'EMP040';
    // this.employeeId = this.route.snapshot.paramMap.get('id');
    this.getLeaveTypes();
    this.getLeaveBalances();
    this.getLeaveHistory();
    this.getPendingLeaveByEmpId();
  }

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      this.selectedFile = input.files[0];
      this.selectedFileName = this.selectedFile.name;
      this.leaveForm.patchValue({ attachment: this.selectedFile });
    }
  }


  setView(view: 'apply' | 'pending' | 'history'): void {
    this.activeView = view;
  }

  // onFileSelected(event: Event): void {
  //   const input = event.target as HTMLInputElement;
  //   if (input.files && input.files.length > 0) {
  //     this.selectedFileName = input.files[0].name;
  //     this.leaveForm.patchValue({ attachment: input.files[0] });
  //   }
  // }

  getLeaveBalances(): void {
    this.leaveService.getLeaveBalances(this.employeeId, 2025).subscribe({
      next: (response) => {
        this.leaveBalances = response;
      },
      error: (error) => {
        console.error('Error fetching leave balances:', error);
      }
    });
  }

  getIconForLeaveType(leaveTypeCode: string): string {
    switch (leaveTypeCode) {
      case 'CL':
        return 'event_available';
      case 'EL':
        return 'beach_access';
      case 'HL':
        return 'local_hospital';
      case 'RH':
        return 'event_busy';
      case 'ULMG':
        return 'medical_services';
      case 'ULPA':
        return 'person';
      case 'COMP':
        return 'work';
      case 'ML':
        return 'pregnant_woman';
      case 'SDL':
        return 'accessible';
      case 'SCL':
        return 'star';
      case 'EL_NO_PAY':
        return 'do_not_disturb';
      default:
        return 'event';
    }
  }

  getLeaveTypeLabel(leaveType: string): string {
    const leaveTypeMap: { [key: string]: string } = {
      earned_leave: 'Earned Leave',
      unearned_medical: 'Unearned Leave (Medical)',
      unearned_private: 'Unearned Leave (Private)',
      maternity: 'Maternity Leave',
      hospital: 'Hospital Leave',
      special_disability: 'Special Disability Leave',
      extraordinary: 'Extraordinary Leave',
      casual: 'Casual Leave',
      special_casual: 'Special Casual Leave'
    };
    return leaveTypeMap[leaveType] || leaveType;
  }


  onHistoryColor(status: string): string {
    switch (status) {
      case 'PENDING':
        return 'orange';
      case 'APPROVED':
        return 'green';
      case 'REJECTED':
        return 'red';
      default:
        return 'gray';
    }
  }
  updateDaysCalculation(): void {
    if (this.leaveForm.valid) {
      const { fromDate, toDate, fromSession, toSession } = this.leaveForm.value;
      if (fromDate && toDate) {
        const start = new Date(fromDate);
        const end = new Date(toDate);
        let days = (end.getTime() - start.getTime()) / (1000 * 3600 * 24) + 1;

        if (fromSession === 'SECOND_HALF') days -= 0.5;
        if (toSession === 'FIRST_HALF') days -= 0.5;
        if (start.getTime() === end.getTime() && fromSession === 'SECOND_HALF' && toSession === 'FIRST_HALF') {
          days = 0.5;
        }

        this.calculatedDays = days >= 0 ? days : null;
      } else {
        this.calculatedDays = null;
      }
    }
  }

  getLeaveTypes(): void {
    this.leaveService.getAllLeaveTypes().subscribe({
      next: (response) => {
        this.leaveTypes = response;
      },
      error: (error) => {
        console.error('Error fetching leave types:', error);
      }
    });
  }

 

  resetForm(): void {
    this.leaveForm.reset();
    this.calculatedDays = null;
    this.selectedFileName = null;
    this.selectedFile = null;
    // Reset file input manually
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
  }

  onSubmit():void{
    if(this.leaveForm.valid){
      const formData = new FormData();
      const request: LeaveApplicationRequest = {
        employeeId: this.employeeId,
        leaveTypeId: this.leaveForm.get('leaveType')?.value,
        fromDate: this.leaveForm.get('fromDate')?.value,
        toDate: this.leaveForm.get('toDate')?.value,
        fromSession: this.leaveForm.get('fromSession')?.value,
        toSession: this.leaveForm.get('toSession')?.value,
        approverId: this.leaveForm.get('approver')?.value,
        reason: this.leaveForm.get('reason')?.value
      };
      console.log('FormData request:', request); // Debug payload
      formData.append('request', new Blob([JSON.stringify(request)], { type: 'application/json' }));
      if (this.selectedFile) {
        formData.append('document', this.selectedFile);
        console.log('FormData file:', this.selectedFile.name); // Debug file
      }
      for (const pair of (formData as any).entries()) {
        console.log(`FormData entry: ${pair[0]}, ${pair[1]}`);
      }
      this.leaveService.applyForLeave(formData).subscribe({
        next: (response) => {
          this.successMessage = response.message;
          this.errorMessage = '';
          this.showSuccess('Leave application submitted successfully');

          this.leaveForm.reset();
          this.calculatedDays = null;
          this.selectedFileName = null;
          this.selectedFile = null;
          // this.loadLeaveBalances();
        },
        error: (error) => {
          this.showError(error.error?.message || 'Failed to submit leave application');
          console.error('Error submitting leave:', error);
        }
      });
    }
  }

  getPendingLeaveByEmpId(){
    this.leaveService.getPendingLeavesByEmpId(this.employeeId).subscribe({
      next: (response) => {
        this.pendingLeaves = response.data;
        console.log('Pending Leave Data:', this.pendingLeaves); // Add this line
      },
      error: (error) => {
        console.error('Error fetching pending leave:', error);
      }
    });
  }

  getLeaveHistory():void{
    this.leaveService.getLeaveHistory(this.employeeId).subscribe({
      next: (response) => {
        this.leaveHistory = response.data;
        console.log('Leave History Data:', this.leaveHistory); // Add this line

      },
      error: (error) => {
        console.error('Error fetching leave history:', error);
      }
    });
  }


showSuccess(message: string): void {
  this.snackBar.open(message, 'Close', {
    duration: 3000,
    panelClass: ['success-snackbar']
  });
}

showError(message: string): void {
  this.snackBar.open(message, 'Close', {
    duration: 5000,
    panelClass: ['error-snackbar']
  });
}
  
}
