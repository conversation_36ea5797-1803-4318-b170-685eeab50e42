.main-content {
  // Adjust based on your sidebar width
  padding: 40px;
  
  min-height: calc(100vh - 60px); // Adjust based on your header height
  background-color: #f5f5f5;
}

.container-fluid {
  max-width: 100%;
  padding: 0;
}

.payslip-card {
  max-width: 800px;
  margin: 20px auto;
  padding: 20px;
  background-color: #f5f5f5;
}

.payslip-title {
  font-size: 24px;
  font-weight: 500;
  color: #203664;
}

.payslip-info {
  margin-bottom: 20px;
}

.payslip-tables {
  display: flex;
  justify-content: space-between;
  gap: 20px;
  margin-bottom: 20px;
}

.table-container {
  flex: 1;
}

table {
  width: 100%;
  border-collapse: collapse;
}

th, td {
  padding: 12px;
  text-align: left;
}

th {
  background-color: #203664;
  color: white;
}

tr:nth-child(even) {
  background-color: #f2f2f2;
}

.payslip-summary {
  display: flex;
  justify-content: space-between;
  padding: 20px;
  background-color: #e8eaf6;
  border-radius: 4px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  width: 30%;
  font-weight: 500;
}

.net-salary {
  font-size: 18px;
  color: #3f51b5;
}

@media (max-width: 600px) {
  .payslip-tables {
    flex-direction: column;
  }
}