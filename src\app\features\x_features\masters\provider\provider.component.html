<mat-toolbar color="primary" class="navbar">
  <nav aria-label="Breadcrumb navigation" style="display: flex; align-items: center; flex-wrap: wrap;">
    <a routerLink="/" class="crumb">{{'Master' | translate}}</a>
    <mat-icon>chevron_right</mat-icon>
    <a class="crumb" (click)="goBack()">{{menuService.activeMenu()?.title}}</a>
    <mat-icon>chevron_right</mat-icon>
    <span aria-current="page" class="nav-active">
      @if(uniqueId() == 0) {
      {{'New' | translate}} {{menuService.activeMenu()?.title}}
      } @else {
        {{'UpdateProvider' | translate}}
      }
    </span>
    @if(mode() === "view") {
    <button (click)="onEdit()" title="edit" class="btn-edit btn-icon-unstyled">
      <mat-icon class="material-symbols-rounded">edit_square</mat-icon>
    </button>
    }
  </nav>
  <button mat-flat-button color="primary" (click)="goBack()" aria-label="Go back" class="back-btn">
    <mat-icon>arrow_back_ios</mat-icon>
    Back
  </button>
</mat-toolbar>


  <div class="card component">
 
    <form [formGroup]="form" (ngSubmit)="onSubmit()">
      <div class="form">


        <div class="field">
          <label for="simNo1" class="required-label">{{'PrimarySIM' | translate}}</label>
          <mat-form-field>
            <input id="simNo1" formControlName="simNo1" matInput (input)="onInput($event,'/^[0-9.]*$/')" maxlength="20">
            <mat-error>
              @if(form.controls.simNo1.errors?.['required']) {
              <!-- Primary SIM is required -->
              {{'PrimarySIM' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="mobileNo1" class="required-label">{{'PrimaryMobileNo' | translate}}</label>
          <mat-form-field>
            <input id="mobileNo1" formControlName="mobileNo1" matInput (input)="onInput($event,'/^[0-9.]*$/')"
              maxlength="10">
            <mat-error>
              @if(form.controls.mobileNo1.errors?.['required']) {
              {{'PrimaryMobileNo' | translate}} {{'IsRequired' | translate}}
              }
              @else if (form.controls.mobileNo1.errors?.['pattern']) {
                {{'InvalidFormat' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="providerType1" class="required-label">{{'PrimaryProvider' | translate}}</label>
          <mat-form-field appearance="outline">
            <mat-select id="providerType1" formControlName="providerType1">
              <!-- <mat-option value="null" disabled selected>Select Primary Provider</mat-option> -->
              @for (role of providers(); track role) {
              <mat-option [value]="role.key">{{role.value}}</mat-option>
              }
            </mat-select>
            <mat-error>
              @if(form.controls.providerType1.errors?.['required']) {
                <!-- Primary provider is required -->
                {{'PrimaryProvider' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="providerPlan1" class="required-label">{{'PrimaryPlan' | translate}}</label>
          <mat-form-field>
            <input id="providerPlan1" formControlName="providerPlan1" matInput
              (input)="onInput($event,'/^[a-zA-Z0-9 ]*$/')" maxlength="100">
            <mat-error>
              @if(form.controls.providerPlan1.errors?.['required']) {
              <!-- Primary plan is required -->
              {{'PrimaryPlan' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>


        <div class="field">
          <label for="simNo2" class="">{{'SecondarySIM' | translate}}</label>
          <mat-form-field>
            <input id="simNo2" formControlName="simNo2" matInput (input)="onInput($event,'/^[0-9.]*$/')" maxlength="20">
            <mat-error>
              @if(form.controls.simNo2.errors?.['required']) {
              <!-- Secondary SIM is required -->
              {{'SecondarySIM' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="mobileNo2" class="">{{'SecondaryMobileNo' | translate}}</label>
          <mat-form-field>
            <input id="mobileNo2" formControlName="mobileNo2" matInput (input)="onInput($event,'/^[0-9.]*$/')"
              maxlength="10">
            <mat-error>
              @if(form.controls.mobileNo2.errors?.['required']) {
              <!-- Secondary mobile no is required -->
              {{'SecondaryMobileNo' | translate}} {{'IsRequired' | translate}}
              }
              @else if (form.controls.mobileNo2.errors?.['pattern']) {
                {{'InvalidFormat' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="providerType2" class="">{{'SecondaryProvider' | translate}}</label>
          <mat-form-field appearance="outline">
            <mat-select id="providerType2" formControlName="providerType2">
              <!-- <mat-option value="null" disabled selected>Select Secondary Provider</mat-option> -->
              @for (role of providers(); track role) {
              <mat-option [value]="role.key">{{role.value}}</mat-option>
              }
            </mat-select>
            <mat-error>
              @if(form.controls.providerType2.errors?.['required']) {
                <!-- Primary provider is required -->
                {{'SecondaryProvider' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="providerPlan2" class="">{{'SecondaryPlan' | translate}}</label>
          <mat-form-field>
            <input id="providerPlan2" formControlName="providerPlan2" matInput
              (input)="onInput($event,'/^[a-zA-Z0-9 ]*$/')" maxlength="100">
            <mat-error>
              @if(form.controls.providerPlan2.errors?.['required']) {
              <!-- Secondary plan is required -->
              {{'SecondaryPlan' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>




      </div>

      <div class="actions">
        
        @if(editable() === true) {
          @if(mode() === 'add') {
            <button type="button" mat-flat-button (click)="formReset()" class="btn btn-danger">{{'Reset' | translate}}</button>
            }
        <button [disabled]="editable() === false" mat-flat-button class="btn btn-theme" type="submit">{{uniqueId() == 0
          ? ('Create' | translate) : ('Update' | translate)}}</button>
        }
      </div>


    </form>
  </div>
