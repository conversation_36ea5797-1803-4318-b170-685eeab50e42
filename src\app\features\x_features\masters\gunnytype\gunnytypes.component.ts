import { Component, computed, effect, inject, linkedSignal, OnInit, resource, signal, viewChild } from '@angular/core';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { confirmAndDelete } from '../../../../models/x_models/masters/shared';
import { NgTemplateOutlet, DatePipe } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInput, MatPrefix } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { AlertService } from '../../../../services/alert.service';
import { ExcelService } from '../../../../services/x_apis/excel.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { GunnytypeService } from '../../../../services/x_apis/masters/gunnytype.service';
import { GunnytypeComponent } from './gunnytype.component';
import { Gunnies, Gunny } from '../../../../models/x_models/masters/gunnytype';
import { MatToolbarModule } from '@angular/material/toolbar';

@Component({
  selector: 'app-gunnytypes',
  imports: [MatIcon,
    MatInput,
    MatPrefix,
    ReactiveFormsModule,
    MatPaginatorModule,
    MatTableModule,
    NgTemplateOutlet,
    MatButtonModule,
    MatMenuModule,
    MatSortModule,
    MatFormFieldModule,
    TranslateModule,
    MatToolbarModule,
    GunnytypeComponent],
  templateUrl: './gunnytypes.component.html',
  styleUrls: ['./gunnytypes.component.scss']
})

export class GunnytypesComponent implements OnInit {
  menuService = inject(MenuService);
  access = computed(() => this.menuService.activeMenu()?.controlAccess);
  translate = inject(TranslateService);

  paginator = viewChild<MatPaginator>(MatPaginator);
  sort = viewChild<MatSort>(MatSort);
  excelService=inject(ExcelService)
  gunnytypeService = inject(GunnytypeService)
  alertService=inject(AlertService)
  gunnytypeResource = resource({ loader: () => this.gunnytypeService.get() });
  dataSource = linkedSignal(() => new MatTableDataSource(this.gunnytypeResource.value()));

  loading = signal(false);
  mode = signal<'view' | 'edit' | 'add'>('add');
  search = new FormControl("");
  showGrid = signal(false);
  excelColumns:any[]=[]
  // status = StatusType;

  gunny = signal<Gunny>({
    id: 0,
    gunnyCode:"",
    gunnyName:"",
    gunnyShortName:""
   
  });

  displayedColumns: string[] = [
    'code',
    'name',
    'shortcode',
    'actions',
  ];
  gunnies = signal<Gunnies[]>([]);

  constructor() {
    effect(() => {
      if (this.paginator()) {
        this.dataSource().sort = this.sort()!;
        this.dataSource().paginator = this.paginator()!
      }
    });
  }

  ngOnInit(): void {
  }

  onAdd() {
    this.gunny.set({
      id: 0,
      gunnyCode:"",
      gunnyName:"",
      gunnyShortName:""
    });
    this.mode.set("add")
    this.showGrid.set(true)
  }

  onView(gunny: Gunny) {
    this.gunny.set(gunny);
    this.mode.set('view');
    this.showGrid.set(true)
  }

  onEdit(gunny: Gunny) {
    this.gunny.set(gunny);
    this.mode.set('edit');
    this.showGrid.set(true)
  }

  onClose() {
    this.showGrid.set(false)
    this.gunnytypeResource.reload();
    this.search.setValue("");
  }

  onRefresh() {
    this.gunnytypeResource.reload();
    this.search.setValue("");
  }

  async onDelete(gunny: Gunny) {
    await confirmAndDelete(gunny, gunny.gunnyName, 'Gunny', this.gunnytypeService, () => this.gunnytypeResource.reload());
  }

  onSearch() {
    debugger
    const filterValue = this.search.value?.trim().toLowerCase() || '';
    this.dataSource().filter = filterValue;
  }

  exportAsXLSX(): void {    
    // this.loading = true;
    let resultdata = this.gunnytypeResource.value;
    setTimeout(() => {      
      var exportData:any= resultdata;
      if (!exportData || exportData.length === 0) {
        this.alertService.info("No data available to export");
        // this.loading = false;
        return;
      }      
      const excelList:any[] = [];
      this.excelColumns = [
        "S.NO",
        "Gunny Name",
        "Gunny Code",
        "Gunny Short Code"
      ]
   
      exportData.forEach((e:any, index:any) => {
          excelList.push({
            "sno": ++index,
            "Gunny Name": e.gunnyName,
            "Gunny Code": e.gunnyCode,
            "Gunny Short Name": e.gunnyShortName,
                    })
      });
      this.excelService.exportAsExcelFile(excelList, "Gunny Type", this.excelColumns);
      // this.loading = false;
    }, 500);

  }

}