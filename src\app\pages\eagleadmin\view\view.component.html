<main class="content">
  <div class="page-header">
    <div class="header-content">
      <div class="header-icon">
        <mat-icon>receipt_long</mat-icon>
      </div>
      <div class="header-text">
        <h2>Payslip Approval</h2>
        <p>Review and approve employee payslip details</p>
      </div>
    </div>
  </div>

  <mat-card class="payslip-card mat-elevation-z4">
    <!-- Employee Header Card -->
    <div class="employee-header">
      <div class="employee-avatar">
        <mat-icon>person</mat-icon>
      </div>
      <div class="employee-basic-info">
        <h3>{{ empObj?.employeeName }}</h3>
        <p class="employee-id">ID: {{ empObj?.empId }}</p>
        <div class="employee-tags">
          <span class="tag department">{{ empObj?.department }}</span>
          <span class="tag role">{{ empObj?.employeeRole }}</span>
        </div>
      </div>
      <div class="status-badge">
        <span class="badge pending">Pending Approval</span>
      </div>
    </div>

    <!-- Employee Details Grid -->
    <div class="details-section">
      <h4 class="section-title">
        <mat-icon>info</mat-icon>
        Employee Information
      </h4>
      
      <div class="employee-info-grid">
        <div class="info-card">
          <div class="info-icon">
            <mat-icon>badge</mat-icon>
          </div>
          <div class="info-content">
            <label>Employee ID</label>
            <span>{{ empObj?.empId }}</span>
          </div>
        </div>

        <div class="info-card">
          <div class="info-icon">
            <mat-icon>person</mat-icon>
          </div>
          <div class="info-content">
            <label>Full Name</label>
            <span>{{ empObj?.employeeName }}</span>
          </div>
        </div>

        <div class="info-card">
          <div class="info-icon">
            <mat-icon>business</mat-icon>
          </div>
          <div class="info-content">
            <label>Department</label>
            <span>{{ empObj?.department }}</span>
          </div>
        </div>

        <div class="info-card">
          <div class="info-icon">
            <mat-icon>work</mat-icon>
          </div>
          <div class="info-content">
            <label>Role</label>
            <span>{{ empObj?.employeeRole }}</span>
          </div>
        </div>

        <div class="info-card">
          <div class="info-icon">
            <mat-icon>trending_up</mat-icon>
          </div>
          <div class="info-content">
            <label>Grade Level</label>
            <span>{{ empObj?.grade }}</span>
          </div>
        </div>

        <div class="info-card">
          <div class="info-icon">
            <mat-icon>credit_card</mat-icon>
          </div>
          <div class="info-content">
            <label>PAN Number</label>
            <span>{{ empObj?.panNumber }}</span>
          </div>
        </div>

        <div class="info-card">
          <div class="info-icon">
            <mat-icon>account_balance</mat-icon>
          </div>
          <div class="info-content">
            <label>PF Number</label>
            <span>{{ empObj?.pfNumber }}</span>
          </div>
        </div>

        <div class="info-card">
          <div class="info-icon">
            <mat-icon>star</mat-icon>
          </div>
          <div class="info-content">
            <label>Special Pay Type</label>
            <span>{{ empObj?.specialPayType || 'N/A' }}</span>
          </div>
        </div>

        <div class="info-card">
          <div class="info-icon">
            <mat-icon>accessible</mat-icon>
          </div>
          <div class="info-content">
            <label>Accessibility</label>
            <span>{{ empObj?.handicapped ? 'Yes' : 'No' }}</span>
          </div>
        </div>

        <div class="info-card highlight-card">
          <div class="info-icon">
            <mat-icon>payments</mat-icon>
          </div>
          <div class="info-content">
            <label>Basic Pay</label>
            <span class="pay-amount">{{ empObj?.basicPay | currency:'INR':'symbol':'1.2-2' }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Action Section -->
    <div class="action-section">
      <div class="action-buttons">
        <button mat-stroked-button color="warn" class="reject-btn" (click)="navigateToPayroll()">
          <mat-icon>close</mat-icon>
          Reject
        </button>
        <button mat-raised-button color="primary" class="approve-btn" (click)="submit()">
          <mat-icon>check</mat-icon>
          Approve Payslip
        </button>
      </div>
    </div>

    <!-- Success Notification -->
    <div *ngIf="showNotification" class="notification-overlay">
      <div class="notification success" [@slideIn]>
        <mat-icon>check_circle</mat-icon>
        <span>Payslip Approved Successfully!</span>
      </div>
    </div>
  </mat-card>
</main>