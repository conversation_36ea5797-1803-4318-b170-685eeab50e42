.component {
  padding: 20px 20px 0 20px;
  // border-radius: 10px;
  // margin-top: 12px;
  // min-height: calc(100% - 145px);
  // background-color: #FFFFFF;
  max-width: max-content;

  @media (max-width: 700px) {
    // background-color: #F6F6F6;
    // padding: 20px;
    max-width: 100%;
    min-height: 100%;
  }

  .page-header {
    display: flex;
    flex-direction: row;
    gap: 10px;
    justify-content: space-between;
    align-items: center;
    padding: 10px 10px;
    flex-wrap: wrap;
    h1 {
        display: flex;
        margin-right: auto;
    }

    @media (max-width: 700px) {
        h1 {
            order: 0;
        }
    }
}

  .header {
    padding-inline: 10px;
    @media (max-width: 700px) {
      padding-left: 0;
    }

    .filters-wrapper {
      // max-width: 615px;
      display: flex;
      align-items: center;



      .search {
        max-width: calc(100% - 26px);
        width: 100%;
        //margin-top: 20px;
        --form-field-bg: #fff;

        .icon-search {
          width: 15px;
          height: 22px;
          padding-inline: 17px 13px;
          color: var(--theme-80);
        }
      }

      .btn-info {
        margin-left: 10px;
      }

      .filters-more {
        position: relative;
        display: flex;

        .btn-filter {
          background-color: #fff;
          border: 1.5px solid var(--grey-20);
          border-radius: 10px;
          margin-left: 15px;
          width: fit-content;
          font-size: 12px;
          display: flex;
          gap: 30px;
          --btn-icon-radius: 8px;

          span {
            z-index: 1;

            @media (max-width: 700px) {
              display: none;
            }
          }

          .icon-filter {
            display: flex;
            justify-content: center;
            align-items: center;
            //padding-inline: 17px 13px;
            color: var(--theme-80);
          }
        }
      }

    }
  }

  .content {
    width: max-content;
    max-width: 100%;
    margin-top: 10px;
    padding-inline: 10px;

    @media (max-width: 700px) {
      width: 100%;
      margin-top: 20px;
    }

    .table-wrapper, .mobile-wrapper {
      background-color: #FFFFFF;
      border-radius: 8px;
    }

    .table-wrapper {
      @media (max-width: 700px) {
        display: none;
      }
    }

    .mobile-wrapper {
      display: none;
      --mdc-checkbox-state-layer-size: 20px;

      @media (max-width: 700px) {
        display: block
      }

      .un-card {
        display: grid;
        grid-template-columns: 1fr 36px;
        padding-block: 8px 15px;
        border-bottom: 1px solid #e9e9e9;

        .select {
          mat-checkbox {
            margin-top: 7px;
          }
        }

        .desc {
          .quote-no {
            font-size: 12px;
            width: 100%;

            .cost {
              font-weight: 700;
            }
          }

          .tracking-no {
            font-size: 10px;
            line-height: 1em;
            color: var(--grey-40);
          }
        }

        .actions {
          .btn {
            margin-top: 2px;
          }
        }
      }
    }

  }
}

table {
  tr {
    grid-template-columns: minmax(150px, 200px) minmax(70px, 130px) minmax(200px, 250px) minmax(145px, 160px) minmax(80px, 110px) 164px;
    min-width: 100%;
  }

  .pill {
    width: 99px;
    height: 23px;
    padding: 0px 31px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 25px;
    color: #000;

    &.quote {
      background-color: var(--grey-10);
    }

    &.invoiced {
      background-color: var(--theme-40);
    }
  }

  .actions {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 3px;

    .btn-icon {
      // color: var(--theme-80)
    }

    mat-icon {
        font-size: 16px;
    }
  }

  // .actions {
  //   padding-inline: 0;

  //   .btn {
  //     --mdc-icon-button-state-layer-size: 24px;
  //     --mat-icon-button-state-layer-color: var(--theme-40);
  //     display: flex;
  //     justify-content: center;
  //     padding-inline: 0;

  //     &:has(.file-lines) {
  //       margin-right: 3px;
  //     }

  //     mat-icon {
  //       width: 13.5px;
  //     }

  //     mat-icon.file-lines {
  //       width: 15px;
  //     }
  //   }

  //   .btn-theme {
  //     color: var(--theme-80);

  //     &[disabled] {
  //       color: #E7E8E9;
  //     }
  //   }
  // }
}


:host ::ng-deep .btn-close svg {
  width: 16px;
  height: 16px;
}

:host ::ng-deep .icon-filter svg {
  width: 15px;
  height: 9px;
}



[statusType='1'] {
  color:#55a609;
}
[statusType='2'] {
  color:#e70202;
}

.btn-icon {
  // color: var(--sys-primary);
  color: rgb(106, 117, 135);
  display: flex;
  align-items: center;
}

.btn-delete {
  // color: rgb(84 95 113);
  padding: 0;
  color: var(--red-80);
  mat-icon {
      font-variation-settings:'FILL' 1,'wght' 700,'GRAD' 200,'opsz' 24;
  }
}

.btn-activate {
  padding: 0;
  color: #6DB02D;
  mat-icon {
      font-variation-settings:'FILL' 1,'wght' 700,'GRAD' 200,'opsz' 24;
  }
}