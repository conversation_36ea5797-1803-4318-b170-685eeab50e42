import { NgT<PERSON>plateOutlet, DatePipe, CurrencyPipe } from '@angular/common';
import { Component, computed, effect, inject, linkedSignal, resource, signal, viewChild } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckbox } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInput, MatPrefix } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatOption, MatSelect } from '@angular/material/select';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { CloseFilterOnBlurDirective } from '../../../../directives/close-filter-on-blur.directive';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { RegionService } from '../../../../services/x_apis/masters/region.service';
import { RegionComponent } from './region.component';
import { Regions } from '../../../../models/x_models/masters/region';
import { confirmAndDelete } from '../../../../models/x_models/masters/shared';
import { StatusType } from '../../../../enums/x_enums/dotnet/status-type';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatToolbarModule } from '@angular/material/toolbar';

@Component({
    selector: 'app-regions',
    imports: [MatIcon,
        MatIcon,
        MatInput,
        MatPrefix,
        ReactiveFormsModule,
        MatCheckbox,
        MatPaginatorModule,
        MatTableModule,
        NgTemplateOutlet,
        MatButtonModule,
        MatMenuModule,
        DatePipe,
        MatSortModule,
        MatToolbarModule,
        MatOption,
        MatSelect,
         TranslateModule,
        CloseFilterOnBlurDirective, CurrencyPipe, MatFormFieldModule, RegionComponent],
    templateUrl: './regions.component.html',
    styleUrl: './regions.component.scss'
})
export class RegionsComponent {
    menuService = inject(MenuService);
    access = computed(() => this.menuService.activeMenu()?.controlAccess);
    translate = inject(TranslateService);

    paginator = viewChild<MatPaginator>(MatPaginator);
    sort = viewChild<MatSort>(MatSort);

    regionService = inject(RegionService);
    regionResource = resource({ loader: () => this.regionService.get() });
    dataSource = linkedSignal(() => new MatTableDataSource(this.regionResource.value()));

    loading = signal(false);
    mode = signal<'view' | 'edit' | 'add'>('add');
    search = new FormControl("");
    showGrid = signal(false);
    status = StatusType;

    list = signal<Regions>({
        id: 0,
        regionCode: "",
        regionName: "",
        regionRegionalName: "",
        deltaTypeName: '',
        regionLgdCode: ""
    });

    displayedColumns: string[] = [
        'regionName',
        'regionRegionalName',
        'regionLgdCode',
        'deltaTypeName',
        'actions'
    ];


    constructor() {
        effect(() => {
            if (this.paginator()) {
                this.dataSource().sort = this.sort()!;
                this.dataSource().paginator = this.paginator()!
            }
        });
    }

    ngOnInit(): void {
    }

    onAdd() {
        this.list.set({
            id: 0,
            regionCode: "",
            regionName: "",
            regionRegionalName: "",
            deltaTypeName: '',
            regionLgdCode: ""
        });
        this.mode.set("add")
        this.showGrid.set(true)
    }

    onView(rowData: Regions) {
        this.list.set(rowData);
        this.mode.set('view');
        this.showGrid.set(true)
    }

    onEdit(rowData: Regions) {
        this.list.set(rowData);
        this.mode.set('edit');
        this.showGrid.set(true)
    }

    onClose() {
        this.showGrid.set(false)
        this.regionResource.reload()
        this.search.setValue("");
    }

    onRefresh() {
        this.regionResource.reload()
        this.search.setValue("");
    }

    async onDelete(rowData: Regions) {
        await confirmAndDelete(rowData, rowData.regionName, 'Region', this.regionService, () => this.regionResource.reload());
    }

    onSearch() {
        const filterValue = this.search.value?.trim().toLowerCase() || '';
        this.dataSource().filter = filterValue;
    }

}
