import { NgTemplateOutlet, DatePipe } from '@angular/common';
import { Component, computed, effect, inject, linkedSignal, OnInit, output, resource, signal, viewChild } from '@angular/core';
import { FormBuilder, FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButton, MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon, MatIconModule } from '@angular/material/icon';
import { MatError, MatFormField, MatInput, MatPrefix } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';;
import { MenuService } from '../../../../services/x_apis/menu.service';
import { StatusType } from '../../../../enums/x_enums/dotnet/status-type';
import { AppRoutes } from '../../../../enums/app-routes';
import { confirmAndDelete } from '../../../../models/x_models/masters/shared';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { GunnyformComponent } from './gunnyform.component';
import { GunnyService } from '../../../../services/x_apis/masters/gunny.service';
import { GunnyList } from '../../../../models/x_models/masters/gunny';
import { LookupService } from '../../../../services/x_apis/lookup.service';
import { MatOption, MatSelect } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { provideNativeDateAdapter } from '@angular/material/core';
import { AlertService } from '../../../../services/alert.service';
import { QrCode } from '../../../../models/x_models/gunny/gunny-allocation';
import { InputformatService } from '../../../../services/x_apis/inputformat.service';
import { MatToolbarModule } from '@angular/material/toolbar';

@Component({
  selector: 'app-gunnylist',
  imports: [
    MatIcon,
    MatInput,
    MatPrefix,
    ReactiveFormsModule,
    MatPaginatorModule,
    MatTableModule,
    NgTemplateOutlet,
    MatButtonModule,
    MatMenuModule,
    DatePipe,
    MatSortModule,
    TranslateModule,
    GunnyformComponent,
    MatOption,
    MatDatepickerModule,
    MatSelect,
    MatFormFieldModule,
    FormsModule,
    MatError,
    MatFormField,
    MatButton,
    MatToolbarModule,
    MatIconModule
  ],

  providers: [provideNativeDateAdapter(), DatePipe],
  templateUrl: './gunnylist.component.html',
  styleUrls: ['./gunnylist.component.scss']
})
export class GunnylistComponent implements OnInit {
  protected readonly AppRoutes = AppRoutes;

  menuService = inject(MenuService);
  access = computed(() => this.menuService.activeMenu()?.controlAccess);

  translate = inject(TranslateService);
  alertService = inject(AlertService);
  inputFormatService = inject(InputformatService);
  gunnyService = inject(GunnyService)
  lookupService = inject(LookupService);
  paginator = viewChild<MatPaginator>(MatPaginator);
  sort = viewChild<MatSort>(MatSort);
  dataSource = signal(new MatTableDataSource<GunnyList>([]));

  gunnyType = resource({ loader: () => this.lookupService.getGunnytype() }).value;

  loading = signal(false);
  mode = signal<'view' | 'edit' | 'add'>('add');
  search = new FormControl("");
  showGrid = signal(false);
  status = StatusType;
  closed = output<boolean>();
  qrData = signal<QrCode[]>([]);

  fb = inject(FormBuilder);
  form = this.fb.group({
    id: [0],
    gunnyTypeDetailId: this.fb.control<number>(1, null),
    scannedDate: [new Date()],
  });

  listData = signal<GunnyList>({
    id: 0,
    gunnyQrCode: '',
    gunnyName: '',
    gunnyShortName: '',
    storageLocationName: ''
  });

  displayedColumns: string[] = [
    'gunnyName',
    'gunnyQrCode',
    'actions',
  ];

  constructor() {
  }

  ngOnInit(): void {
    this.getLists();
  }

  async getLists() {
    const scannedDate = this.form.value.scannedDate ?? new Date();
    const scanDateString = scannedDate.toISOString();

    try {
      const res: any = await this.gunnyService.gunnyDetails({ "entryTs": scanDateString, "gunnyTypeDetailId": this.form.value.gunnyTypeDetailId });
      if (res) {
        const dataArray: GunnyList[] = Array.isArray(res) ? res : [res];
        this.dataSource.set(new MatTableDataSource(dataArray));
        setTimeout(() => {
          this.dataSource().sort = this.sort()!;
          this.dataSource().paginator = this.paginator()!;
        }, 100);
      } else {
        console.log('No Data Found in Qr getById');
      }
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  }

  onAdd() {
    this.listData.set({
      id: 0,
      gunnyQrCode: '',
      gunnyName: '',
      gunnyShortName: '',
      storageLocationName: ''
    });
    this.mode.set("add")
    this.showGrid.set(true)
  }

  onView(data: GunnyList) {
    this.listData.set(data);
    this.mode.set('view');
    this.showGrid.set(true)
  }

  onEdit(data: GunnyList) {
    this.listData.set(data);
    this.mode.set('edit');
    this.showGrid.set(true)
  }

  onClose() {
    this.showGrid.set(false)
    this.getLists();
    this.search.setValue("");
  }

  onRefresh() {
    this.getLists();
    this.search.setValue("");
  }

  async onDelete(data: GunnyList) {
    await confirmAndDelete(data, data.gunnyName, 'Gunny', this.gunnyService, () => this.getLists());
  }

  onSearch() {
    const filterValue = this.search.value?.trim().toLowerCase() || '';
    this.dataSource().filter = filterValue;
  }

  filter(value: any) {
    // this.search.set(value.target.value);
  }

  goBack() {
    this.closed.emit(true)
  }

  onInput(event: any, regexPattern: any): void {
    this.inputFormatService.isNumericOrHyphenOrSpace(event, regexPattern);
  }

  onChange(event: any) {
    this.getLists();
  }
}
