export interface PlayBackDetails {
    vehicleId: number;
    transportCategoryId: number;
    avgSpeed: number;
    startTs: string;
    endTs: string;
    duration: {
        // [key:string]:number
        item1: number;
        item2: number;
        item3: number;
    };
    runTime: {
        // [key:string]:number
        item1: number;
        item2: number;
        item3: number;
    };
    idleTime: {
        // [key:string]:number
        item1: number;
        item2: number;
        item3: number;
    };
    stoppedTime: {
        // [key:string]:number
        item1: number;
        item2: number;
        item3: number;
    };
    distance: number;
    trackingData: any[]; // Use `any[]` or a more specific type based on actual data structure
}


export interface playbackPayload {
    vehicleId: number,
    start: Date,
    end: Date
}

export interface Coordinate {
    lat: number;
    lon: number;
  }