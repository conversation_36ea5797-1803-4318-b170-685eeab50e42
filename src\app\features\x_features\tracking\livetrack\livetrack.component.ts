import { NgT<PERSON>plateOutlet, DatePipe, <PERSON><PERSON><PERSON>cyPipe, CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, computed, effect, inject, linkedSignal, resource, signal, viewChild } from '@angular/core';
import { FormBuilder, FormControl, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckbox } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInput, MatPrefix } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatOption, MatSelect } from '@angular/material/select';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { CloseFilterOnBlurDirective } from '../../../../directives/close-filter-on-blur.directive';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { StatusType } from '../../../../enums/x_enums/dotnet/status-type';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { VehicleCard, Vehicles } from '../../../../models/x_models/tracking/livetrack';
import { LivetrackService } from '../../../../services/x_apis/tracking/livetrack.service';
import { LookupService } from '../../../../services/x_apis/lookup.service';
import { MatCardModule } from '@angular/material/card';
import { AlertService } from '../../../../services/alert.service';
import { LivetrackMapComponent } from './livetrack-map.component';

@Component({
  selector: 'app-livetrack',
  imports: [MatIcon,
    MatIcon,
    MatInput,
    MatPrefix,
    ReactiveFormsModule,
    MatCheckbox,
    MatPaginatorModule,
    MatTableModule,
    NgTemplateOutlet,
    MatButtonModule,
    MatMenuModule,
    DatePipe,
    MatSortModule,
    MatOption,
    MatSelect,
    TranslateModule,
    CommonModule,
    MatCardModule,
    CloseFilterOnBlurDirective, CurrencyPipe, MatFormFieldModule,
    LivetrackMapComponent
  ],
  templateUrl: './livetrack.component.html',
  styleUrl: './livetrack.component.scss'
})
export class LivetrackComponent {
  menuService = inject(MenuService);
  access = computed(() => this.menuService.activeMenu()?.controlAccess);
  translate = inject(TranslateService);
  lookupService = inject(LookupService);
  livetrackService = inject(LivetrackService);
  alertService = inject(AlertService);

  regions = resource({ loader: () => this.lookupService.getRegion() }).value;

  paginator = viewChild<MatPaginator>(MatPaginator);
  sort = viewChild<MatSort>(MatSort);
  private static livetrackData: Vehicles[]
  livetrackResource = resource({
    loader: async () => LivetrackComponent.livetrackData,
  })
  dataSource = linkedSignal(() => new MatTableDataSource<Vehicles>(this.livetrackResource.value()));
  displayedColumns: string[] = [
    'actions',
    'regionName',
    'vehicleStatusTypeName',
    'regNo',
    // 'vehicleId',
    'gpsDeviceTs',
    // 'mainPowerStatusType'
  ];

  fb = inject(FormBuilder);
  form = this.fb.group({
    regionId: this.fb.control<number | null>(8)
  });

  loading = signal(false);
  mode = signal<'view' | 'edit' | 'add'>('add');
  search = new FormControl("");
  showGrid = signal(false);
  mapView = signal(false);
  noData= signal<boolean>(false);
  status = StatusType;
  vehicleData = signal<number>(0);
  mapId = signal<number>(0);

  vehicleCards: VehicleCard[] = [
    {
      title: 'Runing',
      total: 0,
      imageUrl: 'images/tracking/running.png'
    },
    {
      title: 'Stoped',
      total: 0,
      imageUrl: 'images/tracking/stoped.png'
    },
    {
      title: 'Unreachable',
      total: 0,
      imageUrl: 'images/tracking/idle.png'
    },
    {
      title: 'Total',
      total: 0,
      imageUrl: 'images/tracking/total.png'
    }
  ];

  vehicleDetails = signal<Vehicles>({
    id: 0,
    vehicleId: 0,
    regNo: "",
    regionName: "",
    gpsDeviceTs: "",
    mainPowerStatusType: 0,
    vehicleStatusTypeName: "",
    vehicleStatusType: 0
  });

  constructor(private cdRef: ChangeDetectorRef) {
    effect(() => {
      if (this.paginator()) {
        this.dataSource().sort = this.sort()!;
        this.dataSource().paginator = this.paginator()!
      }
    });
  }

  ngOnInit(): void {
    this.getVehicleDetails(8);
    this.form.get("regionId")?.valueChanges.subscribe(res => {
      this.getVehicleDetails(Number(res));
      this.showGrid.set(true)
    });
  }

  async getVehicleDetails(regionId: number) {
    const res = await this.livetrackService.getByRegionId(regionId);
    const vehiclesArray: Vehicles[] = Array.isArray(res) ? res : [res];
    this.noData.set(vehiclesArray.length > 0 ? false : true);
    // if (vehiclesArray.length > 0) {
      this.showGrid.set(true);
      this.livetrackResource.set(vehiclesArray);
      this.vehicleCards[0].total = vehiclesArray.filter(item => item.vehicleStatusType === 1 || item.vehicleStatusType === 2).length;
      this.vehicleCards[1].total = vehiclesArray.filter(item => item.vehicleStatusType === 0).length;
      this.vehicleCards[2].total = vehiclesArray.filter(item => item.vehicleStatusType === 3).length;
      this.vehicleCards[3].total = +this.vehicleCards[0].total + +this.vehicleCards[1].total + +this.vehicleCards[2].total;
    // } else {
    //   this.showGrid.set(false);
    //   // this.alertService.error('No Data Found')
    // }
  }

  getVehicleStatusImage(status: number): string {
    switch (status) {
      case 1:
        return 'images/tracking/connected.png';
      case 2:
        return 'images/tracking/disconnected.png';
      default:
        return 'images/tracking/notconnected.png';
    }
  }

  getVehicleStatusAltText(status: number): string {
    switch (status) {
      case 1:
        return 'Connected';
      case 2:
        return 'Disconnected';
      default:
        return 'Not Connected';
    }
  }

  onMapView(rowData: Vehicles) {
    this.mapId.set(2);
    if (this.showGrid()) {
      this.vehicleDetails.set(rowData);
      this.mapView.set(true);
      this.showGrid.set(false);
    } else {
      this.mapView.set(false);
      this.showGrid.set(true);
    }
  }

  onMapFullView(id: number) {
    this.mapId.set(1);
    this.mapView.set(id === 1 ? true : false);
    this.showGrid.set(id === 1 ? false : true);
  }

  onRefresh() {
    this.getVehicleDetails(Number(this.form.value.regionId));
    this.search.setValue("");
  }

  onClose() {
    this.showGrid.set(false)
    this.livetrackResource.reload()
    this.search.setValue("");
  }

  onSearch() {
    const filterValue = this.search.value?.trim().toLowerCase() || '';
    this.dataSource().filter = filterValue;
  }

}
