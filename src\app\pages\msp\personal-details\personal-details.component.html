<div class="container">
  <mat-card class="form-card">
    <mat-card-header class="card-header">
      <mat-card-title>
        <mat-icon class="header-icon">person</mat-icon>
        <h1>Personal Details</h1>
      </mat-card-title>
      <mat-card-subtitle>
        Please fill in your personal information accurately
      </mat-card-subtitle>
    </mat-card-header>

    <mat-progress-bar 
      *ngIf="isLoading()" 
      mode="indeterminate" 
      class="progress-bar">
    </mat-progress-bar>

    <mat-card-content class="card-content">
      <form [formGroup]="personalDetailsForm" (ngSubmit)="onSubmit()" class="form-container">
        
        <!-- Basic Information Section -->
        <div class="section">
          <h3 class="section-title">
            <mat-icon>account_circle</mat-icon>
            Basic Information
          </h3>
          <mat-divider></mat-divider>
          
          <div class="form-grid">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Applicant Name</mat-label>
              <input 
                matInput 
                formControlName="applicantName" 
                placeholder="Enter your full name"
                autocomplete="name">
              <mat-icon matSuffix>person</mat-icon>
              <mat-error *ngIf="personalDetailsForm.get('applicantName')?.invalid && personalDetailsForm.get('applicantName')?.touched">
                {{getErrorMessage('applicantName')}}
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Mobile Number</mat-label>
              <input 
                matInput 
                formControlName="mobileNumber" 
                placeholder="Enter 10-digit mobile number"
                autocomplete="tel">
              <mat-icon matSuffix>phone</mat-icon>
              <mat-error *ngIf="personalDetailsForm.get('mobileNumber')?.invalid && personalDetailsForm.get('mobileNumber')?.touched">
                {{getErrorMessage('mobileNumber')}}
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Gender</mat-label>
              <mat-select formControlName="gender">
                <mat-option *ngFor="let gender of genderList" [value]="gender.value">
                  {{gender.label}}
                </mat-option>
              </mat-select>
              <mat-icon matSuffix>wc</mat-icon>
              <mat-error *ngIf="personalDetailsForm.get('gender')?.invalid && personalDetailsForm.get('gender')?.touched">
                {{getErrorMessage('gender')}}
              </mat-error>
            </mat-form-field>
          </div>
        </div>

        <!-- Family Information Section -->
        <div class="section">
          <h3 class="section-title">
            <mat-icon>family_restroom</mat-icon>
            Family Information
          </h3>
          <mat-divider></mat-divider>
          
          <div class="form-grid">
            <mat-form-field appearance="outline">
              <mat-label>Father's Name</mat-label>
              <input 
                matInput 
                formControlName="fathersName" 
                placeholder="Enter father's full name">
              <mat-icon matSuffix>person</mat-icon>
              <mat-error *ngIf="personalDetailsForm.get('fathersName')?.invalid && personalDetailsForm.get('fathersName')?.touched">
                {{getErrorMessage('fathersName')}}
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Mother's Name</mat-label>
              <input 
                matInput 
                formControlName="mothersName" 
                placeholder="Enter mother's full name">
              <mat-icon matSuffix>person</mat-icon>
              <mat-error *ngIf="personalDetailsForm.get('mothersName')?.invalid && personalDetailsForm.get('mothersName')?.touched">
                {{getErrorMessage('mothersName')}}
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Fathers Birth District</mat-label>
              <mat-select formControlName="birthDistrict">
                <mat-option *ngFor="let district of districtList" [value]="district.name">
                  {{district.name}}
                </mat-option>
              </mat-select>
              <mat-icon matSuffix>location_on</mat-icon>
              <mat-error *ngIf="personalDetailsForm.get('birthDistrict')?.invalid && personalDetailsForm.get('birthDistrict')?.touched">
                {{getErrorMessage('birthDistrict')}}
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Fathers Native District</mat-label>
              <mat-select formControlName="nativeDistrict">
                <mat-option *ngFor="let district of districtList" [value]="district.name">
                  {{district.name}}
                </mat-option>
              </mat-select>
              <mat-icon matSuffix>home</mat-icon>
              <mat-error *ngIf="personalDetailsForm.get('nativeDistrict')?.invalid && personalDetailsForm.get('nativeDistrict')?.touched">
                {{getErrorMessage('nativeDistrict')}}
              </mat-error>
            </mat-form-field>

          </div>
        </div>

        <!-- Personal Details Section -->
        <div class="section">
          <h3 class="section-title">
            <mat-icon>info</mat-icon>
            Personal Details
          </h3>
          <mat-divider></mat-divider>
          
          <div class="form-grid">
            <mat-form-field appearance="outline">
              <mat-label>Date of Birth</mat-label>
              <input 
                matInput 
                [matDatepicker]="picker" 
                formControlName="dateOfBirth"
                [max]="maxDate"
                readonly>
              <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
              <mat-datepicker #picker></mat-datepicker>
              <mat-error *ngIf="personalDetailsForm.get('dateOfBirth')?.invalid && personalDetailsForm.get('dateOfBirth')?.touched">
                {{getErrorMessage('dateOfBirth')}}
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Birth District</mat-label>
              <mat-select formControlName="birthDistrict">
                <mat-option *ngFor="let district of districtList" [value]="district.name">
                  {{district.name}}
                </mat-option>
              </mat-select>
              <mat-icon matSuffix>location_on</mat-icon>
              <mat-error *ngIf="personalDetailsForm.get('birthDistrict')?.invalid && personalDetailsForm.get('birthDistrict')?.touched">
                {{getErrorMessage('birthDistrict')}}
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Native District</mat-label>
              <mat-select formControlName="nativeDistrict">
                <mat-option *ngFor="let district of districtList" [value]="district.name">
                  {{district.name}}
                </mat-option>
              </mat-select>
              <mat-icon matSuffix>home</mat-icon>
              <mat-error *ngIf="personalDetailsForm.get('nativeDistrict')?.invalid && personalDetailsForm.get('nativeDistrict')?.touched">
                {{getErrorMessage('nativeDistrict')}}
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Religion</mat-label>
              <mat-select formControlName="religion">
                <mat-option *ngFor="let religion of religionList" [value]="religion">
                  {{religion}}
                </mat-option>
              </mat-select>
              <mat-icon matSuffix>account_balance</mat-icon>
              <mat-error *ngIf="personalDetailsForm.get('religion')?.invalid && personalDetailsForm.get('religion')?.touched">
                {{getErrorMessage('religion')}}
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Nationality</mat-label>
              <input 
                matInput 
                formControlName="nationality" 
                placeholder="e.g., Indian">
              <mat-icon matSuffix>flag</mat-icon>
              <mat-error *ngIf="personalDetailsForm.get('nationality')?.invalid && personalDetailsForm.get('nationality')?.touched">
                {{getErrorMessage('nationality')}}
              </mat-error>
            </mat-form-field>
          </div>
        </div>

        <!-- Government ID Section -->
        <div class="section">
          <h3 class="section-title">
            <mat-icon>badge</mat-icon>
            Government ID Details
          </h3>
          <mat-divider></mat-divider>
          
          <div class="form-grid">
            <mat-form-field appearance="outline">
              <mat-label>Aadhaar Number</mat-label>
              <input 
                matInput 
                formControlName="aadhaarNumber" 
                placeholder="Enter 12-digit Aadhaar number"
                maxlength="12">
              <mat-icon matSuffix>credit_card</mat-icon>
              <mat-error *ngIf="personalDetailsForm.get('aadhaarNumber')?.invalid && personalDetailsForm.get('aadhaarNumber')?.touched">
                {{getErrorMessage('aadhaarNumber')}}
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>PAN Number</mat-label>
              <input 
                matInput 
                formControlName="panNumber" 
                placeholder="**********"
                maxlength="10"
                style="text-transform: uppercase;">
              <mat-icon matSuffix>assignment_ind</mat-icon>
              <mat-error *ngIf="personalDetailsForm.get('panNumber')?.invalid && personalDetailsForm.get('panNumber')?.touched">
                {{getErrorMessage('panNumber')}}
              </mat-error>
            </mat-form-field>
          </div>
        </div>

        <!-- Document Upload Section -->
        <div class="section">
          <h3 class="section-title">
            <mat-icon>upload_file</mat-icon>
            Document Upload
          </h3>
          <mat-divider></mat-divider>
          
          <div class="file-upload-section">
            <div class="file-upload-container">
              <input 
                #fileInput
                type="file" 
                id="fileInput"
                accept=".pdf,.jpg,.jpeg,.png"
                (change)="onFileSelected($event)"
                hidden>
              
              <button 
                type="button" 
                mat-stroked-button 
                (click)="fileInput.click()"
                class="upload-button">
                <mat-icon>cloud_upload</mat-icon>
                Choose Community Certificate
              </button>
              
              <div *ngIf="selectedFile()" class="file-info">
                <mat-icon class="file-icon">description</mat-icon>
                <span class="file-name">{{selectedFile()?.name}}</span>
                <button 
                  type="button" 
                  mat-icon-button 
                  (click)="removeFile()"
                  class="remove-file-btn">
                  <mat-icon>close</mat-icon>
                </button>
              </div>
            </div>
            
            <div class="file-requirements">
              <small class="requirement-text">
                <mat-icon class="info-icon">info</mat-icon>
                Accepted formats: PDF, JPEG, PNG | Maximum size: 5MB
              </small>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="button-section">
          <button 
            type="button" 
            mat-stroked-button 
            (click)="onReset()"
            [disabled]="isLoading()"
            class="reset-button">
            <mat-icon>refresh</mat-icon>
            Reset Form
          </button>
          
          <button 
            type="submit" 
            mat-raised-button 
            color="primary"
            [disabled]="personalDetailsForm.invalid || isLoading()"
            class="submit-button">
            <mat-icon *ngIf="!isLoading()">send</mat-icon>
            <mat-icon *ngIf="isLoading()" class="loading-icon">hourglass_empty</mat-icon>
            {{isLoading() ? 'Submitting...' : 'Submit Details'}}
          </button>
        </div>
      </form>
    </mat-card-content>
  </mat-card>
</div>