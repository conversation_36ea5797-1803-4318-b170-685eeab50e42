.content {
    height: calc(100% - 180px);
}

.content {
    display: flex;
    margin-top: 12px;

    #mapId {
        width: 100%;
        min-height: 100%;
    }
}

.grid-container {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    gap: 1rem;
}

.row-12 {
    grid-column: span 12;
}

.row-8 {
    grid-column: span 8;
}

.row-4 {
    grid-column: span 4;
}

.row-6 {
    grid-column: span 6;
}

@media (max-width: 768px) {
    .grid-container {
        grid-template-columns: repeat(12, 1fr);
    }

    .row-8 {
        grid-column: span 12;
        text-align: left;
    }

    .row-4 {
        grid-column: span 12;
    }

    .row-6 {
        grid-column: span 12;
    }

    .actions {
        justify-content: flex-start;
    }

    .card-container {
        grid-template-columns: 1fr;
    }
}

.txt-aln {
    color: rgb(9, 98, 182)
}

.value-aln {
    color: rgb(18, 26, 20)
}

.gap {
    padding-top: 15px;
    padding-bottom: 15px;
}

.mdc-checkbox {
    padding: 0px !important;
}
.example-margin {
    margin-top:10px;
    margin-right:10px;
}
