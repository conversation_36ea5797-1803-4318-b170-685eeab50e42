.leave-balance-container {
    padding: 24px;
    background-color: #f5f5f5;
    min-height: 100vh;
  }
  
  .header {
    text-align: center;
    margin-bottom: 32px;
    background-color: #11074e;
    color: white;

    
    .page-title {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12px;
      font-size: 2.5rem;
      font-weight: 300;
      color: #ffffff;
      margin: 0;
      
      mat-icon {
        font-size: 2.5rem;
        width: 2.5rem;
        height: 2.5rem;
        color: #ffffff;
      }
    }
    
    .subtitle {
      color: #666;
      font-size: 1.1rem;
      margin: 8px 0 0 0;
    }
  }
  
  .leave-cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 24px;
    margin-bottom: 32px;
  }
  
  .leave-card {
    padding: 24px;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    background: white;
    
    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    }
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
  }
  
  .leave-type {
    display: flex;
    align-items: center;
    gap: 12px;
    
    mat-icon {
      font-size: 1.5rem;
      width: 1.5rem;
      height: 1.5rem;
    }
    
    .type-text {
      font-size: 1.25rem;
      font-weight: 500;
      color: #333;
    }
  }
  
  .granted-section {
    text-align: right;
    
    .granted-label {
      display: block;
      font-size: 0.875rem;
      color: #666;
      margin-bottom: 4px;
    }
    
    .granted-value {
      font-size: 1.5rem;
      font-weight: 600;
      color: #333;
    }
  }
  
  .balance-section {
    display: flex;
    justify-content: center;
    margin: 24px 0;
  }
  
  .balance-circle {
    width: 120px;
    height: 120px;
    border: 4px solid;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.9);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    .balance-number {
      font-size: 2rem;
      font-weight: 700;
      color: #333;
      line-height: 1;
    }
    
    .balance-label {
      font-size: 0.875rem;
      color: #666;
      margin-top: 4px;
    }
  }
  
  .progress-section {
    margin: 20px 0;
    
    mat-progress-bar {
      height: 8px;
      border-radius: 4px;
    }
    
    .progress-labels {
      display: flex;
      justify-content: space-between;
      margin-top: 8px;
      font-size: 0.875rem;
      color: #666;
    }
  }
  
  .consumed-section {
    margin-top: 20px;
    padding-top: 16px;
    border-top: 1px solid #eee;
  }
  
  .consumed-info {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #666;
    
    mat-icon {
      color: #f44336;
      font-size: 1.25rem;
      width: 1.25rem;
      height: 1.25rem;
    }
    
    strong {
      color: #333;
    }
  }
  
  .summary-card {
    margin-top: 32px;
    padding: 24px;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  .summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 24px;
    margin-top: 16px;
  }
  
  .summary-item {
    text-align: center;
    padding: 16px;
    border-radius: 8px;
    background: #f8f9fa;
    
    .summary-label {
      display: block;
      font-size: 0.875rem;
      color: #666;
      margin-bottom: 8px;
    }
    
    .summary-value {
      font-size: 1.75rem;
      font-weight: 600;
      color: #333;
      
      &.consumed {
        color: #f44336;
      }
      
      &.available {
        color: #4CAF50;
      }
    }
  }
  
  @media (max-width: 768px) {
    .leave-balance-container {
      padding: 16px;
    }
    
    .leave-cards-grid {
      grid-template-columns: 1fr;
      gap: 16px;
    }
    
    .header .page-title {
      font-size: 2rem;
      
      mat-icon {
        font-size: 2rem;
        width: 2rem;
        height: 2rem;
      }
    }
    
    .balance-circle {
      width: 100px;
      height: 100px;
      
      .balance-number {
        font-size: 1.5rem;
      }
    }
  }