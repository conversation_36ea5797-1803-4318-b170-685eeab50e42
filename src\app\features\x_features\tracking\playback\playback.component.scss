.mapcard {
    // height: 500px;
    // width: 100%;
    // border: 1px solid black;
    margin-top: 10px;
    margin-bottom: 10px;
}

.details {
    background-image: linear-gradient(rgb(63, 103, 167), rgb(109, 176, 45));
    margin-bottom: 5px;
    border-radius: 10px;
    height: 50%;
}

.detailheader {
    font-weight: bold;
    text-align: center;
    padding: 5px 5px 0px 5px;
    color: rgb(250, 250, 220);
    font-size: 15px !important;
    width: 100% !important
}



.detailBody {
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 13px;
    padding: 5px;
    color: white;


}

.timefont {
    font-size: 16px;
}

.datetime {
    display: flex;
    align-items: center;
    font-weight: bold;
    padding: 2px 10px 10px 10px;
}

.row>* {
    // padding-right:0px;
}

.alignment {
    display: flex;
    justify-content: space-between;
}

.btnrefresh {
    background-color: #6DB02D !important;
    color: white !important;
    font-weight: bold !important;
    border-radius: 5px;
}

.map_range {
    transform: scale(0.98);
    position: absolute;
    z-index: 999;
    bottom: 0rem;
    left: 0rem;
    width: 100%;
    background-color: #FCFCFC;
    opacity: 0.8;
}

.map_chartsec {
    transform: scale(0.8);
    position: absolute;
    z-index: 999;
    bottom: 13rem;
    left: 1.3rem;
}

ngx-gauge {
    display: flex;
}

@media only screen and (max-width: 500px) and (min-width: 320px) {
    .map_chartsec {
        position: absolute;
        z-index: 999;
        width: 51%;
        bottom: 2%;
        right: 5%;
    }
}

.map_sec {
    height: 100%;
}

.speedometer {
    background: #eceaeaeb;
    border-radius: 30px;
    width: min-content;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
}

.map_icons_sec {
    z-index: 999;
    position: absolute;
    right: 1%;
    top: 12%;
}

.map_icons_sec span {
    display: block;
    font-size: 36px;
    color: #3f67a7;
    width: 37px;
}

.map_icons_sec span.forward a {
    background-color: #3f67a7;
    border-radius: 50%;
    font-size: 14px;
    color: #fff;
    width: 37px;
    height: 37px;
    display: -webkit-box;
    display: flex;
    -webkit-box-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    align-items: center;
    margin: 11px 0px;
}

.map_icons_sec span.forward i {
    font-size: 14px;
}

.map_icons_sec img {
    max-width: 100%;
}


.time_odometer {}

.time_odometer p {
    color: blue;
    font-size: 20px;
    width: 100%;
    text-align: center;
}

.cuurlen {
    font-size: 18px;
}


@media only screen and (max-width: 1400px) {
    .time_odometer p {
        color: blue;
        font-size: 18px;
        width: 100%;
        text-align: center;
    }
}

@media only screen and (max-width: 500px) and (min-width: 320px) {
    .map_chartsec {
        position: absolute;
        z-index: 999;
        width: 51%;
        bottom: 2%;
        right: 5%;
    }
}

.titlecard {
    // display: flex;
    // align-items: center;
    // justify-content: space-between;

    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;

    .vehiclenum {}

    .address {
        display: flex;
        justify-content: center;
    }

    .backactions {
        display: flex;
        gap: 5px;
        justify-content: flex-end;
    }
}

.detailcards{
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 0.5rem;
}


/* Tablet: 2 columns */
@media (max-width: 992px) {
    .detailcards {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Mobile: 1 column */
@media (max-width: 600px) {
    .titlecard {
        grid-template-columns: 1fr;

        .vehiclenum {
            display: flex;
            justify-content: center;
        }

        .address {
            display: flex;
            justify-content: center;
        }

        .backactions {
            display: flex;
            gap: 5px;
            justify-content: center;
        }
    }
    .detailcards {
        grid-template-columns: 1fr;
    }
}


