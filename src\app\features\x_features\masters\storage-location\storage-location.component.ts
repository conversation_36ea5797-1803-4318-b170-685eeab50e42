import { Component, computed, ElementRef, inject, input, output, resource, signal, viewChild } from '@angular/core';
import { Form<PERSON>uilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButton } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormField, MatInput, MatError } from '@angular/material/input';
import { MatOption, MatSelect } from '@angular/material/select';
import { Router } from '@angular/router';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { DateInputDirective } from '../../../../directives/date-input.directive';
import { MatNativeDateModule } from '@angular/material/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { LookupService } from '../../../../services/x_apis/lookup.service';
import { AlertService } from '../../../../services/alert.service';
import { InputformatService } from '../../../../services/x_apis/inputformat.service';
import { StorageLocationService } from '../../../../services/x_apis/masters/storage-location.service';
import { StorageLocation } from '../../../../models/x_models/masters/storage-location';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { MatToolbarModule } from '@angular/material/toolbar';

@Component({
  selector: 'app-storage-location',
  imports: [FormsModule,
    MatFormField,
    MatInput,
    ReactiveFormsModule,
    MatError,
    MatOption,
    MatSelect,
    MatButton,
    MatIconModule,
    MatDatepickerModule,
    DateInputDirective,
    MatNativeDateModule,
    TranslateModule,
    MatToolbarModule,
    MatFormFieldModule],
  templateUrl: './storage-location.component.html',
  styleUrl: './storage-location.component.scss'
})
export class StorageLocationComponent {
  menuService = inject(MenuService);
  lookupService = inject(LookupService);
  storageLocationService = inject(StorageLocationService);
  alertService = inject(AlertService);
  inputFormatService = inject(InputformatService);

  translate = inject(TranslateService);
  regions = resource({ loader: () => this.lookupService.getRegion() }).value;
  storageTypes = resource({ loader: () => this.lookupService.getStorageType() }).value;

  mode = input.required<string>();
  closed = output<boolean>();
  fb = inject(FormBuilder);
  editable = signal<boolean>(true);
  router = inject(Router);

  uniqueId = input.required<any>();
  storagelocation = signal<StorageLocation | null>(null);
  search1 = viewChild.required<ElementRef<HTMLInputElement>>('search1')
  search2 = viewChild.required<ElementRef<HTMLInputElement>>('search2')


  form = this.fb.group({
    id: this.fb.control<number>(0),
    storageLocationName: this.fb.control<string>('', Validators.required),
    storageLocationRegionalName: this.fb.control<string>('', Validators.required),
    storageType: this.fb.control<number | null>(null, Validators.required),
    regionId: this.fb.control<number | null>(null, Validators.required),
    unitId: this.fb.control<number>(0, Validators.required),
    talukId: this.fb.control<number>(0, Validators.required),
    blockId: this.fb.control<number>(0, Validators.required),
    villageId: this.fb.control<number>(0, Validators.required),
    agencyId: this.fb.control<number>(1, Validators.required),
    capacity: this.fb.control<number | null>(null, Validators.required),
    carpetArea: this.fb.control<number | null>(null, Validators.required),
    latitude: this.fb.control<number | null>(null, Validators.required),
    longitude: this.fb.control<number | null>(null, Validators.required)
  });

  ngOnInit() {
    this.translate.onLangChange.subscribe(() => {
      this.fetchRegions();
    });
    if (this.mode() === "edit" || this.mode() === "view") {
      this.getFormData()
    }
  }

  readonly regionSearch = signal('');
  readonly storageSearch = signal('');



  readonly filteredRegions = computed(() =>
    this.regions()?.filter(x =>
      x.value.toLowerCase().includes(this.regionSearch().toLowerCase())
    )
  );

  readonly filteredStorageTypes = computed(() =>
    this.storageTypes()?.filter(x =>
      x.value.toLowerCase().includes(this.storageSearch().toLowerCase())
    )
  );
  


  filter(value: any,type:string) {
    if (type === 'region') {
      this.regionSearch.set(value.target.value);
    } else if (type === 'storage') {
      this.storageSearch.set(value.target.value);
    }
  }

    resetSearch(type: any) {

    if (type === 'region') {
      this.search1().nativeElement.value = '';
      this.regionSearch.set('');
    } else if (type === 'storage') {
      this.storageSearch.set('');
      this.search2().nativeElement.value = '';
    } 

  }

  async fetchRegions() {
    this.regions.set(await this.lookupService.getRegion());
    // this.storageTypes.set(await this.lookupService.getStorageType());
  }

  async getFormData() {
    const res = await this.storageLocationService.getById(this.uniqueId());
    this.storagelocation.set(res)
    this.form.patchValue({ ...res });
    if (this.mode() === "view") {
      this.editable.set(false)
      this.form.disable()
    }
  }

  onEdit() {
    this.form.enable();
    this.editable.set(true)
  }

  async onSubmit() {
    if (this.form.valid) {
      const formValue = { ...this.form.value };
      const res = await this.storageLocationService.create(formValue as StorageLocation)
      if (res.isSuccess) {
        this.closed.emit(true)
        if (formValue.id == 0) {
          this.alertService.success(`Storage Location ${this.form.value.storageLocationName} Created successfully.`)
        }
        else {
          this.alertService.success(`Storage Location ${this.form.value.storageLocationName} Updated successfully.`)
        }
      }
    } else {
      this.form.markAllAsTouched();
    }
  }

  goBack() {
    this.closed.emit(true)
  }

  formReset() {
    let indexId = this.form.value.id;
    this.form.reset();
    this.form.patchValue({
      id: indexId
    });
  }

  onInput(event: any, regexPattern: any): void {
    this.inputFormatService.isNumericOrHyphenOrSpace(event, regexPattern);
  }

}
