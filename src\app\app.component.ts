import {Component, DEFAULT_CURRENCY_CODE, inject} from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { ViewModeService } from './services/view-mode.service';
import { MatIconModule, MatIconRegistry } from '@angular/material/icon';
import { DomSanitizer } from '@angular/platform-browser';
import { MAT_FORM_FIELD_DEFAULT_OPTIONS } from '@angular/material/form-field';
import { LoaderComponent } from './components/loader/loader.component';
import { LoaderService } from './services/loader.service';
import { TranslateService } from '@ngx-translate/core';
import * as en from '../../public/i18n/en.json';  
import * as ta from '../../public/i18n/ta.json';  
@Component({
  selector: 'app-root',
  imports: [RouterOutlet, MatIconModule, LoaderComponent],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
  host: {
    '(window:resize)': 'onWindowResize()',
  },
  providers: [
    { provide: MAT_FORM_FIELD_DEFAULT_OPTIONS, useValue: {appearance: 'outline'} },
    { provide: DEFAULT_CURRENCY_CODE, useValue: '₹' }
  ]
})
export class AppComponent {
  viewModeService = inject(ViewModeService);
  domSanitizer = inject(DomSanitizer);
  loader = inject(LoaderService);
  translate = inject(TranslateService);

  constructor() {
    const language = localStorage.getItem('language') || 'en'; // Default to 'en
     this.translate.setDefaultLang('en');
     this.translate.setTranslation('en', en);
     this.translate.setTranslation('ta', ta);
     this.translate.use(language);
  }

  onWindowResize() {
    this.viewModeService.handleResize();
  }
}
