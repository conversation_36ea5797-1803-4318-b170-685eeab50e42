import { Component, OnInit, ViewChild } from '@angular/core';

import { Router, RouterOutlet } from '@angular/router';

import { ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { MatTable, MatTableModule } from '@angular/material/table';
import { MatTabsModule } from '@angular/material/tabs';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatNativeDateModule, MatOptionModule } from '@angular/material/core';
import { MatInputModule } from '@angular/material/input';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatCardModule } from '@angular/material/card';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { PayrollService } from '../../../services/m_apis/service/payroll.service';

@Component({
  selector: 'app-payslipapprovedlist',
  imports: [
  
    ReactiveFormsModule,
    CommonModule,
    RouterOutlet, 
    MatTableModule,
    MatTabsModule,
    MatIconModule, 
    MatCardModule,
    MatButtonModule,
    MatPaginatorModule,
    MatSortModule,
    MatTooltipModule,
    MatFormFieldModule,
    MatSelectModule,
    MatOptionModule,
    MatInputModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatChipsModule,
    MatProgressSpinnerModule
  ],
  templateUrl: './payslipapprovedlist.component.html',
  styleUrls: ['./payslipapprovedlist.component.scss']
})
export class PayslipapprovedlistComponent implements OnInit {
  
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  dataSource: any[] = [];
  
  // Updated displayedColumns to match the payroll data structure
  displayedColumns: string[] = [
    'sno',
    'empId',
    'employeeName',
    'payMonth',
    'pfNumber',
    'basicPay',
    'houseRentAllowance',
    'dearnessAllowance',
    'cashAllowance',
    'cityCompensatoryAllowance',
    'conveyanceAllowance',
    'medicalAllowance',
    'specialPay',
    'washAllowance',
    'totalEarnings',
    'epf',
    'fbf',
    'his',
    'spf',
    'loanDeduction',
    'totalDeductions',
    'netSalary',
    'actions'
  ];

  mainList: any[] = [];
  count: number = 0;
  pageSize: number = 10;
  isLoading: boolean = false;

  constructor(
    private webService: PayrollService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.getListapproved();
  }

  getListapproved() {
    this.isLoading = true;
    
    this.webService.getApprovedPayslips().subscribe({
      next: (data) => {
        this.dataSource = (data as any).responseData || [];
        this.count = this.dataSource.length;
        this.mainList = [...this.dataSource];
        this.isLoading = false;
        
        console.log('Approved payslips data:', this.dataSource);
      },
      error: (err) => {
        console.error('Error fetching approved payslips:', err);
        this.isLoading = false;
        this.dataSource = [];
        this.count = 0;
      }
    });
  }

  goPageChange(empId: any) {
    this.router.navigate([`m/approvedpayslip/${empId}`]);
  }

  viewDetails(row: { empId: any; }) {
    // Navigate to payslip details page
    this.router.navigate(['/payslip-details', row.empId]);
  }

  refreshData() {
    this.getListapproved();
  }

  // Optional: Add filtering method
  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    // Implement filtering logic here if needed
  }

  // Optional: Export functionality
  exportToExcel() {
    // Implement export functionality if needed
    console.log('Exporting to Excel...');
  }
}