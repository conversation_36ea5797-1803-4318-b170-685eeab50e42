import { computed, inject, Injectable, signal } from '@angular/core';
import { environment } from '../../environments/environment';
import * as jwtDecode from "jwt-decode"
import { SessionData } from '../models/x_models/session';
import { CryptoService } from './crypto.service';
import { DataService } from './data.service';
import { TokenReponse } from '../models/x_models/auth';
import { UserResponse } from '../models/x_models/user';
// import {AccessToken} from '../models/auth';


@Injectable({
  providedIn: 'root'
})
export class SessionService {
  cryptoService = inject(CryptoService);
  dataService = inject(DataService)

  localStorageSessionKey = environment.apiBaseUrl + '-AuthData';

  #sessionSignal = signal<SessionData | null>(null);
  session = this.#sessionSignal.asReadonly()

  isLoggedIn = computed(() => !!this.session());


  constructor() {
    this.loadFromLocalStorage();
  }

  async create(accessToken: string) {
    debugger
    let decodedToken = jwtDecode.jwtDecode<any>(accessToken);
    debugger
    // const userDetails = this.decryptToken(decodedToken);
    const sessionData: SessionData = {
      ...decodedToken,
      accessToken: accessToken
    }

    localStorage.setItem(this.localStorageSessionKey, JSON.stringify(sessionData))
    // const decryptedSessionData = this.updateSessionDataWithUserDetails(sessionData)
    this.#sessionSignal.set(sessionData)
  }


  loadFromLocalStorage() {
    const jsonData: any = localStorage.getItem(this.localStorageSessionKey);

    if (jsonData) {
      const sessionData = JSON.parse(jsonData)
      // const userDetails = this.decryptToken(sessionData)

      // const decryptedSessionData = this.updateSessionDataWithUserDetails(sessionData)
      this.#sessionSignal.set(sessionData)
    }
  }

  accessToken() {
    return this.session()?.responseData;
  }

  updateSessionDataWithUserDetails(sessionData: any) {
    debugger
    const jsonData: any = localStorage.getItem(this.localStorageSessionKey);
      const Data = JSON.parse(jsonData)

    const session = {
      ...Data,
      blockId: sessionData.blockId,
      dpcId: sessionData.dpcId,
      firstname: sessionData.firstname,
      id: sessionData.id,
      lastname: sessionData.lastname,
      regionId: sessionData.regionId,
      roleId: sessionData.roleId,
      talukId: sessionData.talukId,
      villageId: sessionData.villageId
    }
    localStorage.setItem(this.localStorageSessionKey, JSON.stringify(session))

    this.#sessionSignal.set(session)
  }


  decryptToken(decodedToken: any) {
    return JSON.parse(this.cryptoService.decodeDecrypt(decodedToken.code));
  }

  clear() {
    localStorage.removeItem(this.localStorageSessionKey);
    this.#sessionSignal.set(null)
  }

  isTokenExpired() {
    const session = this.session();
    const currentDate = new Date().getTime();
    return session && session.exp >= currentDate;
  }
}
