export interface Hulling {
    id: number,
    hullingCode: string,
    hullingName: string,
    hullingRegionalName: string,
    address: string,
    presentAddress: string,
    pincode: string,
    fortifiedType: number,
    paddyType: number,
    mrmType: number,
    latitude?: number,
    longitude?: number,
    landmark?: string,
    millerName: string,
    millType: number,
    factoryLicenseNo: string,
    factoryLicenseExpiryTs: string,
    boilerLicenseNo: string,
    boilerLicenseExpiryTs: string,
    fromDate?: string,
    toDate?: string,
    validFrom: string,
    validTo: string,
    modelNo: string,
    gstNo: string,
    panNo: string,
    isGeneratorAvailable: boolean,
    generatorCapacity: number,
    isSeparateEbMeter: boolean,
    uuId?: number,
    isAadhaarVerified?: boolean,
    regionId: number,
    talukId: number,
    agencyId: number,
    storageLocationId?: number
}

export interface Hullings {
    id: number | null,
    hullingCode: string,
    hullingName: string,
    hullingRegionalName: string,
    address: string,
    fortifiedTypeName: string,
    mrmTypeName: string,
    millerName: string,
    millTypeName: string,
    validFrom: string,
    validTo: string,
    regionName: string,
    agencyName: string,
}

export type HullingResponse = Hullings[];