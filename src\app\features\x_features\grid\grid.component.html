<div class="component card">
  <div class="page-header">
    <h1>{{"Users"}}</h1>
  </div>
  <div class="header">
    <div class="filters-wrapper">
      <mat-form-field class="search hide-subscript" appearance="outline">
        <mat-icon class="icon-search material-symbols-outlined" matPrefix>search</mat-icon>
        <input id="search" [formControl]="search" (keydown.enter)="onSearch()" autocomplete="off" matInput placeholder="Search">
      </mat-form-field>
      <div class="filters-more">
        <button (click)="showFilters.set(true)" class="btn btn-filter" mat-icon-button>
          <mat-icon class="icon-filter material-symbols-outlined" matPrefix >filter_list</mat-icon>
        </button>
        <button class="btn btn-filter" mat-icon-button>
          <mat-icon class="icon-filter material-symbols-outlined" matPrefix >add</mat-icon>
        </button>
        <button class="btn btn-filter" mat-icon-button>
          <mat-icon class="icon-filter material-symbols-outlined" matPrefix >sync</mat-icon>
        </button>
        @if (showFilters()) {
          <div appCloseFilterOnBlur [(showFilters)]="showFilters" class="filters">
            <div class="filters-header">
              <h3>Filters</h3>
              <button (click)="showFilters.set(false)" class="btn btn-close" mat-icon-button aria-label="Close generate invoice">
                <!-- <mat-icon svgIcon="close"></mat-icon> -->
              </button>
            </div>
            <div class="filters-body">
              <label for="transaction-type">Invoice number</label>
              <mat-form-field class="hide-subscript">
                <mat-select value="" id="transaction-type" >
                  <mat-option value="" disabled>Select</mat-option>
                </mat-select>
              </mat-form-field>
            </div>
            <div class="filters-actions">
              <button class="btn btn-theme" mat-stroked-button>Reset</button>
              <button class="btn btn-theme" mat-flat-button>Apply</button>
            </div>
          </div>
        }
      </div>
    </div>

  </div>
  <div class="content">
    <div class="table-wrapper">
      <table mat-table [dataSource]="dataSource()" matSort class="flex-table">

        <ng-container matColumnDef="selectAll">
          <th class="select-all" mat-header-cell *matHeaderCellDef>
            <mat-checkbox [checked]="allSelected()" [indeterminate]="partiallySelected()" (change)="onSelectAllChange($event)" >
              <div class="visually-hidden">Check box</div>
            </mat-checkbox>
          </th>
          <td class="select-item" mat-cell *matCellDef="let element">
            @if (!loading()) {
              <mat-checkbox [checked]="element.selected" (change)="onSelectItemChange($event, element)" >
                <div class="visually-hidden">Check box</div>
              </mat-checkbox>
            }
          </td>
        </ng-container>

        <ng-container matColumnDef="quoteNo">
          <th class="" mat-header-cell *matHeaderCellDef mat-sort-header> Quote number  </th>
          <td class="" mat-cell *matCellDef="let element"> {{element.quoteNo}}  </td>
        </ng-container>

        <ng-container matColumnDef="referenceNo">
          <th class="" mat-header-cell *matHeaderCellDef mat-sort-header> Reference number </th>
          <td class="" mat-cell *matCellDef="let element">{{element.referenceNo}}</td>
        </ng-container>

        <ng-container matColumnDef="date">
          <th class="" mat-header-cell *matHeaderCellDef mat-sort-header> Date </th>
          <td class="" mat-cell *matCellDef="let element">{{element.date | date:'dd-MM-YYYY'}}</td>
        </ng-container>

        <ng-container matColumnDef="cost">
          <th class="" mat-header-cell *matHeaderCellDef mat-sort-header> Estimated cost </th>
          <td class="" mat-cell *matCellDef="let element">{{element.cost | currency }}</td>
        </ng-container>

        <ng-container matColumnDef="status">
          <th class="" mat-header-cell *matHeaderCellDef mat-sort-header> Status </th>
          <td class="" mat-cell *matCellDef="let element">
            <div class="status" [attr.data-status]="element.status">{{element.status}}</div>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

        <tr *matNoDataRow>
          <ng-container *ngTemplateOutlet="shimmer"></ng-container>
        </tr>
      </table>
      <mat-paginator pageSize="8" [length]="pageLength()" [pageIndex]="pageIndex()" (page)="onPageChange($event)" [pageSizeOptions]="[5, 10, 15]"
                     aria-label="Select page of periodic elements">
      </mat-paginator>
    </div>

    <div class="mobile-wrapper">
      <div class="mobile-header">
        <div class="col-left">
          <mat-checkbox [indeterminate]="partiallySelected()" (change)="onSelectAllChange($event)" >
            <div class="checkbox-label">Select all</div>
          </mat-checkbox>
        </div>
        <div class="col-right">

        </div>
      </div>
      <div class="un-cards">
        @for (item of dataSource().data; track item.referenceNo) {
          <div class="un-card">
            <div class="select">
              <mat-checkbox [checked]="item.selected" (change)="onSelectItemChange($event, item)" >
                <div class="visually-hidden">Check box</div>
              </mat-checkbox>
            </div>
            <div class="desc">
              <div class="quote-no">{{item.quoteNo}} <div class="cost">{{item.cost | currency}}</div></div>
              <div class="tracking-no">{{item.date | date: "dd-MM-YYYY"}}</div>
              <div class="status" [attr.data-status]="item.status">{{item.status}}</div>
            </div>
            <div class="actions">
              <button mat-icon-button class="btn btn-actions" [matMenuTriggerFor]="menu" aria-label="Card actions">
                <mat-icon>more_vert</mat-icon>
              </button>
              <mat-menu #menu="matMenu">

              </mat-menu>
            </div>
          </div>
        }
      </div>

      <mat-paginator pageSize="8" [length]="pageLength()" [pageIndex]="pageIndex()" (page)="onPageChange($event)" [pageSizeOptions]="[5, 10, 15]"
                     aria-label="Select page of periodic elements">
      </mat-paginator>
    </div>
  </div>
</div>

<ng-template #shimmer>
  <div class="grid-shimmer">
    @for (num of [1, 2, 3, 4, 5, 6, 7, 8]; track $index) {
      <div class="row">
        @for (item of [1, 2, 3, 4, 5, 6, 7, ]; track $index) {
          <div class="cell">
            <div class="shimmer-container">
              <div class="shimmer" [attr.col]="item"></div>
            </div>
          </div>
        }
      </div>
    }
  </div>
</ng-template>

<ng-template #searchInfo>
  <div style="font-size: 12px;line-height: 16px;">
    <p style="font-weight: 700;">Search using any of the following details:</p>
    <ul style="padding-left: 20px;">
      <li>Quote number</li>
      <li>Reference number</li>
    </ul>
  </div>
</ng-template>
