<div class=" card d-flex justify-content-between align-items-center flex-wrap grid-margin mb-2 header">
    <div  style="width: 100%;display: flex;">
        <div class="col-6 tripdetails" style="width: 50%;">
            {{TripSheetDetails | json}}

            <div class="tripstatus" *ngIf="TripSheetDetails()?.tripstatus ==2">
                <!-- <span class="tsts">{{ this.getTripStatus(TripSheetDetails()?.tripstatus)}}</span> &nbsp;&nbsp; -->
                <span>Distance : <span class="bld" style="font-size: 20px;">{{TripSheetDetails()?.km}}&nbsp;Kms</span>&nbsp;&nbsp;</span>
                <span>|</span>&nbsp;&nbsp;
                <span>Travelled Time : <span class="bld" style="font-size: 20px;">{{time}}</span> </span>
            </div>
            <div class="memotitle row">
                <div class="col-6 bld"><span class="smltxt">Source</span>
                    <h3>{{TripSheetDetails()?.sourcefrommemo}}</h3>
                    <span style="display: flex;">
                    <h6 *ngIf="TripSheetDetails()?.sourceFromVillage">{{TripSheetDetails()?.sourceFromVillage}}, </h6>
                    <h6 *ngIf="TripSheetDetails()?.sourceFromTaluk">{{TripSheetDetails()?.sourceFromTaluk}}, </h6>
                    <h6 *ngIf="TripSheetDetails()?.sourceFromRegion">{{TripSheetDetails()?.sourceFromRegion}}.</h6>
                </span>
                </div>
                <div class="col-6 bld"><span class="smltxt">Destination</span>
                    <h3>{{TripSheetDetails()?.sourcetomemo}}</h3>
                    <span style="display: flex;">
                    <h6 *ngIf="TripSheetDetails()?.sourceToVillage">{{TripSheetDetails()?.sourceToVillage}}, </h6>
                    <h6 *ngIf="TripSheetDetails()?.sourceToTaluk">{{TripSheetDetails()?.sourceToTaluk}}, </h6>
                    <h6 *ngIf="TripSheetDetails()?.sourceToRegion">{{TripSheetDetails()?.sourceToRegion}}.</h6>
                </span>

                </div>
            </div>
            <div class="memotitle row">
                <div class="col-6 bld"><span class="smltxt">Vehicle Reg. No</span>
                    <h4 style="display: flex;align-items: center;">{{TripSheetDetails()?.vehicle_number}}&nbsp;
                    <img *ngIf="TripSheetDetails()?.verifyStatus" src="images/tracking/Frame 1000003836.svg" style="width:25px;height:25px;"  title="Vahan Verified" />
                    <img *ngIf="!TripSheetDetails()?.verifyStatus" src="images/tracking/Frame 1000003837.svg" style="width:25px;height:25px;" title="Vahan Not Verified" />
                    </h4>
                </div>
                <div class="col-6 bld"><span class="smltxt">Driver Details</span>
                    <h4  *ngIf="TripSheetDetails()?.drivername || TripSheetDetails()?.drivermobileno" style="display: flex;align-items: center;">{{TripSheetDetails()?.drivername}} / {{TripSheetDetails()?.drivermobileno}} &nbsp;
                    <img *ngIf="TripSheetDetails()?.dVerifyStatus" src="images/tracking/Frame 1000003836.svg" style="width:25px;height:25px;"  title="Sarathi Verified" />
                    <img *ngIf="!TripSheetDetails()?.dVerifyStatus" src="images/tracking/Frame 1000003837.svg" style="width:25px;height:25px;" title="Sarathi Not Verified" />
                    </h4>
                </div>
            </div>
            <div class="memotitle row">
                <div class="col-6 bld"><span class="smltxt">Memo Number</span>
                    <h4>{{TripSheetDetails()?.txn_id}}</h4>
                </div>
                <div class="col-6 bld d-flex" style="align-items: center;">
                    <img src="images/tracking/eye.png" style="cursor:pointer;width:30px;height:30px;" title="View Live Track" (click)="navigateToLivetrack(TripSheetDetails())" />&nbsp;&nbsp;
                    <img *ngIf="TripSheetDetails()?.tripstatus ==2" src="images/tracking/play-back.svg" style="cursor:pointer;width:25px;height:25px;" (click)="navigateToPlayback(TripSheetDetails())" title="View Play Back" /> 
                </div>
            </div>

            <div class="row mt-3">
                <!-- <div class="row d-flex">
                    <div class="col-4 bld center"><span>Commodity</span></div>
                    <div class="col-4 bld center">Quantity</div>
                    <div class="col-2"></div>
                </div>

                <div class="row d-flex" *ngFor="let item of TripSheetDetails()?.setOfTripSheetViewModels">
                    <div class="col-4 center p-2">{{ item?.commodity }}</div>
                    <div class="col-4 center p-2">{{ item?.total_qty }}</div>
                    <div class="col-2"></div>
                </div> -->
                <table class="table table-bordered">
                    <thead>
                      <tr>
                        <th>Commodity</th>
                        <th>Total Quantity</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr *ngFor="let item of TripSheetDetails()?.setOfTripSheetViewModels">
                        <td>{{ item?.commodity }}</td>
                        <td>{{ item?.total_qty }}</td>

                      </tr>
                    </tbody>
                  </table>
                
            </div>

           
            
            <div class="row mt-5">
                <div style="width: 500px;margin-left: auto;margin-right: auto;">
                    <div class="triplineStart row">
                        <div class="starticon col-1"></div>
                        <div class="col-11">
                            <span class="mcts">Memo Created</span>
                            {{memoTime}}<br>
                        </div>
                        <br>
                        <br>
                        <br>
                        
                    </div>
                    <div class="triplineStart row " *ngIf="StartDatetime !=''">
                        <div class="starticon col-1"></div>
                        <div class="col-11">
                            <span class="tsts">Trip Started</span>
                            {{StartDatetime}}<br>
                        </div>
                        <br>
                        <br>
                        <br>
                    </div>
                    <div class="triplineEnd row" *ngIf="EndDatetime !=''">
                        <div class="starticon col-1"></div>
                        <div class="col-11">
                            <span class="tets">Trip Completed</span>
                            {{EndDatetime}}
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <div class="col-6" style="width: 50%;">
            
            <!-- <app-livetrack-map [vehicleId]="1"></app-livetrack-map> -->
            <!-- <div id="mapId" #mapId style="display:block; position: relative;height: 800px !important;">
                <div class="map_icons_sec sidebtns pointer">
                   
                </div>
            </div> -->
        </div>
    </div>

     <!-- <span class="resender_sec" matTooltip="{{'Recenter'|translate}}" matTooltipClass="tooltip-edit"
                        matTooltipPosition="right" >
                        <a>
                            <img src="../../../../assets/images/icon/resender.png" alt="stop">
                        </a>
                    </span> -->

</div>
