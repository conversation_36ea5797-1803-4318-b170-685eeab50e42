import { NgT<PERSON>plateOutlet, DatePipe, CurrencyPipe } from '@angular/common';
import { Component, computed, effect, inject, linkedSignal, resource, signal, viewChild } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckbox } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInput, MatPrefix } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatOption, MatSelect } from '@angular/material/select';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { CloseFilterOnBlurDirective } from '../../../../directives/close-filter-on-blur.directive';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { confirmAndDelete } from '../../../../models/x_models/masters/shared';
import { StatusType } from '../../../../enums/x_enums/dotnet/status-type';
import { HullingComponent } from './hulling.component';
import { HullingService } from '../../../../services/x_apis/masters/hulling.service';
import { Hullings } from '../../../../models/x_models/masters/hulling';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatToolbarModule } from '@angular/material/toolbar';

@Component({
    selector: 'app-hullings',
    imports: [MatIcon,
        MatIcon,
        MatInput,
        MatPrefix,
        ReactiveFormsModule,
        MatCheckbox,
        MatPaginatorModule,
        MatTableModule,
        NgTemplateOutlet,
        MatButtonModule,
        MatMenuModule,
        DatePipe,
        MatSortModule,
        MatOption,
        MatSelect,
        MatToolbarModule,
        TranslateModule,
        CloseFilterOnBlurDirective, CurrencyPipe, MatFormFieldModule, HullingComponent],
    templateUrl: './hullings.component.html',
    styleUrl: './hullings.component.scss'
})
export class HullingsComponent {
    menuService = inject(MenuService);
    access = computed(() => this.menuService.activeMenu()?.controlAccess);
    translate = inject(TranslateService);

    paginator = viewChild<MatPaginator>(MatPaginator);
    sort = viewChild<MatSort>(MatSort);

    hullingService = inject(HullingService);
    hullingResource = resource({ loader: () => this.hullingService.get() });
    dataSource = linkedSignal(() => new MatTableDataSource(this.hullingResource.value()));

    loading = signal(false);
    mode = signal<'view' | 'edit' | 'add'>('add');
    search = new FormControl("");
    showGrid = signal(false);
    status = StatusType;

    list = signal<Hullings>({
        id: 0,
        hullingCode: "",
        hullingName: "",
        hullingRegionalName: "",
        address: "",
        fortifiedTypeName: "",
        mrmTypeName: "",
        millerName: "",
        millTypeName: "",
        validFrom: "",
        validTo: "",
        regionName: "",
        agencyName: ""
    });

    displayedColumns: string[] = [
        'agencyName',
        'regionName',
        'hullingCode',
        'hullingName',
        'millTypeName',
        'validFrom',
        'validTo',
        'actions'
    ];


    constructor() {
        effect(() => {
            if (this.paginator()) {
                this.dataSource().sort = this.sort()!;
                this.dataSource().paginator = this.paginator()!
            }
        });
    }

    ngOnInit(): void {
    }

    onAdd() {
        this.list.set({
            id: 0,
            hullingCode: "",
            hullingName: "",
            hullingRegionalName: "",
            address: "",
            fortifiedTypeName: "",
            mrmTypeName: "",
            millerName: "",
            millTypeName: "",
            validFrom: "",
            validTo: "",
            regionName: "",
            agencyName: ""
        });
        this.mode.set("add")
        this.showGrid.set(true)
    }

    onView(rowData: Hullings) {
        this.list.set(rowData);
        this.mode.set('view');
        this.showGrid.set(true)
    }

    onEdit(rowData: Hullings) {
        debugger
        this.list.set(rowData);
        this.mode.set('edit');
        this.showGrid.set(true)
    }

    onClose() {
        this.showGrid.set(false)
        this.hullingResource.reload()
        this.search.setValue("");
    }

    onRefresh() {
        this.hullingResource.reload()
        this.search.setValue("");
    }

    async onDelete(rowData: Hullings) {
        await confirmAndDelete(rowData, rowData.regionName, 'Hulling', this.hullingService, () => this.hullingResource.reload());
    }

    onSearch() {
        const filterValue = this.search.value?.trim().toLowerCase() || '';
        this.dataSource().filter = filterValue;
      }

}
