// .leave-apply-container {
//     padding: 24px;
//     background-color: #f5f5f5;
//     min-height: 100vh;
  
//     .header {
//       text-align: center;
//       margin-bottom: 32px;
  
//       .page-title {
//         display: flex;
//         align-items: center;
//         justify-content: center;
//         gap: 12px;
//         font-size: 2.5rem;
//         font-weight: 300;
//         color: #333;
//         margin: 0;
  
//         mat-icon {
//           font-size: 2.5rem;
//           width: 2.5rem;
//           height: 2.5rem;
//           color: #11074e;
//         }
//       }
  
//       .subtitle {
//         color: #666;
//         font-size: 1.1rem;
//         margin: 8px 0 0 0;
//       }
//     }
  
//     .apply-card {
//       max-width: 100%;
//       margin: 0 auto;
//       padding: 24px;
//       border-radius: 12px;
//       box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
//       background: white;
//       border-left: 4px solid #11074e;
  
//       &:hover {
//         transform: translateY(-4px);
//         box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
//       }
//     }
  
//     .form-section {
//       margin-bottom: 24px;
  
//       &.date-session-grid {
//         display: grid;
//         grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
//         gap: 24px;
//       }
  
//       &.submit-section {
//         text-align: right;
//       }
  
//       &.file-upload-section {
//         border-top: 1px solid #eee;
//         padding-top: 16px;
//       }
  
//       .form-field {
//         width: 100%;
//       }
  
//       .file-upload {
//         display: flex;
//         align-items: center;
//         gap: 12px;
  
//         input[type="file"] {
//           display: none;
//         }
  
//         .file-name {
//           color: #666;
//           font-size: 0.875rem;
//         }
  
//         mat-icon {
//           font-size: 1.25rem;
//           width: 1.25rem;
//           height: 1.25rem;
//         }
//       }
  
//       button[mat-raised-button] {
//         padding: 0 24px;
//         font-weight: 500;
  
//         mat-icon {
//           margin-right: 8px;
//           font-size: 1.25rem;
//           width: 1.25rem;
//           height: 1.25rem;
//         }
//       }
//     }
  
//     @media (max-width: 768px) {
//       padding: 16px;
  
//       .header .page-title {
//         font-size: 2rem;
  
//         mat-icon {
//           font-size: 2rem;
//           width: 2rem;
//           height: 2rem;
//         }
//       }
  
//       .apply-card {
//         padding: 16px;
//       }
  
//       .form-section.date-session-grid {
//         grid-template-columns: 1fr;
//         gap: 16px;
//       }
//     }
//   }

.leave-management-container {
    padding: 24px;
    background-color: #f5f5f5;
    min-height: 100vh;
  
    .nav-bar {
      display: flex;
      gap: 16px;
      justify-content: center;
      margin-bottom: 32px;
  
      .nav-button {
        display: flex;
        align-items: center;
        gap: 8px;
        background-color: #11074e;
        color: white;
        padding: 8px 16px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transition: background-color 0.2s ease, box-shadow 0.2s ease;
  
        &:hover {
          background-color: #1a0c7a;
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }
  
        &.active {
          background-color: #1a0c7a;
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }
  
        mat-icon {
          font-size: 1rem;
          width: 1rem;
          height: 1rem;
        }
      }
    }
  
    .leave-apply-container {
      .header {
        // text-align: center;
        margin-bottom: 32px;
       
        .page-title {
        //   display: flex;
        //   align-items: center;
        //   justify-content: center;
          gap: 12px;
          font-size: 2.5rem;
          font-weight: 300;
          color: #333;
          margin: 0;
          padding-left: 24px;
  
          mat-icon {
            font-size: 2.5rem;
            width: 2.5rem;
            height: 2.5rem;
            color: #ffffff;
          }
        }
  
        .subtitle {
            background-color: #f4e7e6; /* light green */
            color: #a36416;            /* darker green text for contrast */
            font-size: 1.1rem;
            margin: 8px 0 0 0;
            padding: 12px 24px;
            border-radius: 6px;
          }
          
      }
  
      .apply-card {
        max-width: 100%;
        margin: 0 auto;
        padding: 12px;
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        border-left: 4px solid #11074e;
  
        .form-section {
          margin-bottom: 24px;
  
          // .form-field {
          //   width: 100%;

          // }

          .textarea-field {
            width: 100%;
            display: block; /* Ensure it behaves as a block-level element */
          }
        
        
          
  
          // &.date-session-grid {
          //   display: grid;
          //   grid-template-columns: repeat(2, minmax(200px, 1fr)); /* Fixed 2 columns with min width */
          //   gap: 16px;
          //   align-items: start;
          //   margin-bottom: 20px; /* Spacing between date-session blocks */
          // }

         &.date-session-grid {
            display: grid;
            grid-template-columns: repeat(2, minmax(100px, 0.5fr)); /* Fixed 2 columns with min width */
            gap: 16px;
            align-items: start;
            // margin-bottom: 20px; 
          }

       
  
          &.file-upload-section {
            .file-upload {
              display: flex;
              align-items: center;
              gap: 10px;
  
              input[type="file"] {
                display: none;
              }
  
              .file-name {
                color: #666;
                font-size: 0.875rem;
              }
              .file-upload-wrapper {
                display: flex;
                flex-direction: column;
                gap: 8px;
                cursor: pointer;
                
                .upload-icon {
                  display: flex;
                  align-items: center;
                  color: #666;
                  font-size: 0.875rem;
                  cursor: pointer;
                }
                
                .file-info {
                  display: flex;
                  align-items: center;
                  gap: 8px;
                  color: #666;
                  font-size: 0.875rem;
                }
              }
            }
          }
  
          &.submit-section {
            text-align: center;
  
            button {
              background-color: #11074e;
              color: white;
              padding: 8px 24px;
              border-radius: 8px;
            }
            .reset-btn {
                background-color: #e8f0fe;      // light blue or choose any
                color: #1a237e;                 // dark blue text
                border: 1px solid #90caf9;      // light border to mimic 'stroked'
                margin-left: 12px;
              }
          }
        }
      }
    }

    .leave-balances {
      display: flex;
      gap: 1rem;
      margin-bottom: 2rem;
      flex-wrap: wrap;
    
      .balance-card {
        display: flex;
        align-items: center;
        padding: 1rem;
        background: #f5f5f5;
        border-radius: 8px;
        flex: 1;
        min-width: 200px;
    
        mat-icon {
          margin-right: 0.5rem;
          color: #3f51b5;
        }
    
        .balance-info {
          display: flex;
          flex-direction: column;
    
          .balance-label {
            font-size: 0.9rem;
            color: #666;
          }
    
          .balance-value {
            font-size: 1.1rem;
            font-weight: 500;
            color: #333;
          }
        }
      }
    }
    
    .selected-leave-balance {
      margin-bottom: 1.5rem;
    
      .leave-type-balance {
        display: flex;
        align-items: center;
        padding: 1rem;
        background: #e8eaf6;
    
        mat-icon {
          margin-right: 0.5rem;
          color: #3f51b5;
        }
    
        .leave-type-info {
          display: flex;
          flex-direction: column;
    
          .leave-type-label {
            font-size: 0.9rem;
            color: #333;
            font-weight: 500;
          }
    
          .leave-type-value {
            font-size: 0.85rem;
            color: #666;
          }
        }
      }
    }
    .days-calculation {
      margin-bottom: 20px;
  
      .days-card {
        display: flex;
        align-items: center;
        padding: 5px;
        background: #eff6ff;
        border-radius: 6px;
        border: 2px solid #11074e;
  
        mat-icon {
          margin-right: 10px;
          color: #11074e;
        }
  
        .days-info {
          display: flex;
          flex-direction: column;
  
          .days-label {
            font-size: 0.9rem;
            color: #1e293b;
            font-weight: 500;
          }
  
          .days-value {
            font-size: 1rem;
            color: #11074e;
            font-weight: 600;
            display: flex;
            justify-content: center;
          }
        }
      }
    }
    
    .form-container {
      display: flex;
      flex-direction: column;
      
    }
  
    .leave-table-container {
      .header {
        // text-align: center;
        margin-bottom: 32px;
  
        .page-title {
        //   display: flex;
        //   align-items: center;
        //   justify-content: center;
          gap: 12px;
          font-size: 2.5rem;
          font-weight: 300;
          color: #333;
          margin: 0;
          padding-left: 24px;
  
          mat-icon {
            font-size: 2.5rem;
            width: 2.5rem;
            height: 2.5rem;
            color: #11074e;
          }
        }
  
        .subtitle {
            background-color: #f4e7e6; /* light green */
            color: #a36416;            /* darker green text for contrast */
            font-size: 1.1rem;
            margin: 8px 0 0 0;
            padding: 12px 24px;
            border-radius: 6px;
          }
          
      }
  
      // .table-card {
      //   max-width: 100%;
      //   margin: 0 auto;
      //   padding: 24px;
      //   border-radius: 12px;
      //   box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      //   border-left: 4px solid #11074e;
  
      //   .leave-table {
      //     width: 100%;
      //     font-size: 0.875rem;
      //     color: #333;
  
      //     th {
      //       font-weight: 500;
      //       color: #666;
      //       padding: 8px;
      //     }
  
      //     td {
      //       padding: 8px;
      //       border-top: 1px solid #eee;
      //     }
      //   }
  
      //   .no-data {
      //     text-align: center;
      //     font-size: 0.875rem;
      //     color: #666;
      //     padding: 16px 0;
      //   }
      // }
    }
  
    @media (max-width: 768px) {
      padding: 16px;
  
      .nav-bar {
        flex-direction: column;
        gap: 8px;
      }
  
      .leave-apply-container {
        .header .page-title {
          font-size: 2rem;
  
          mat-icon {
            font-size: 2rem;
            width: 2rem;
            height: 2rem;
          }
        }
  
        .apply-card {
          padding: 16px;
  
          .form-section.date-session-grid {
            grid-template-columns: 1fr;
            gap: 8px;
          }
        }
      }
  
      .leave-table-container {
        .table-card {
          padding: 16px;
  
          .leave-table {
            font-size: 0.75rem;
  
            th, td {
              padding: 6px;
            }
          }
        }
      }
    }


  }


// .leave-management-container {
//   padding: 24px;
//   background-color: #f5f5f5;
//   min-height: 100vh;

//   .nav-bar {
//     display: flex;
//     gap: 16px;
//     justify-content: center;
//     margin-bottom: 32px;

//     .nav-button {
//       display: flex;
//       align-items: center;
//       gap: 8px;
//       background-color: #11074e;
//       color: white;
//       padding: 8px 16px;
//       border-radius: 8px;
//       box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
//       transition: background-color 0.2s ease, box-shadow 0.2s ease;

//       &:hover {
//         background-color: #1a0c7a;
//         box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
//       }

//       &.active {
//         background-color: #1a0c7a;
//         box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
//       }

//       mat-icon {
//         font-size: 1rem;
//         width: 1rem;
//         height: 1rem;
//       }
//     }
//   }

//   .leave-apply-container {
//     .header {
//       margin-bottom: 32px;
//       text-align: left;

//       .page-title {
//         display: flex;
//         align-items: center;
//         gap: 12px;
//         font-size: 2.5rem;
//         font-weight: 300;
//         color: #333;
//         margin: 0;
//         padding-left: 24px;

//         mat-icon {
//           font-size: 2.5rem;
//           width: 2.5rem;
//           height: 2.5rem;
//           color: #11074e;
//         }
//       }

//       .subtitle {
//         background-color: #f4e7e6;
//         color: #a36416;
//         font-size: 1.1rem;
//         margin: 8px 0 0 24px;
//         padding: 12px 24px;
//         border-radius: 6px;
//       }
//     }

//     .apply-card {
//       max-width: 1200px;
//       margin: 0 auto;
//       padding: 24px;
//       border-radius: 12px;
//       box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
//       border-left: 4px solid #11074e;

//       .form-section {
//         margin-bottom: 24px;

//         .form-field {
//           width: 100%;
//           border-radius: 6px;

//           &.highlight-leave-type {
//             background-color: #e3f2fd; /* Light blue */
//             border: 1px solid #2196f3;
//           }

//           &.highlight-date {
//             background-color: #e8f5e9; /* Light green */
//             border: 1px solid #4caf50;
//           }

//           &.highlight-session {
//             background-color: #fff3e0; /* Light orange */
//             border: 1px solid #ff9800;
//           }

//           &.highlight-approver {
//             background-color: #f3e5f5; /* Light purple */
//             border: 1px solid #9c27b0;
//           }

//           &.highlight-reason {
//             background-color: #fce4ec; /* Light pink */
//             border: 1px solid #e91e63;
//           }
//         }

//         &.date-session-grid {
//           display: grid;
//           grid-template-columns: repeat(2, 1fr);
//           gap: 16px;
//           align-items: start;
//         }

//         &.file-upload-section {
//           .file-upload {
//             display: flex;
//             align-items: center;
//             gap: 16px;

//             &.highlight-file {
//               background-color: #e0f7fa; /* Light cyan */
//               padding: 12px;
//               border: 1px solid #00bcd4;
//               border-radius: 6px;
//             }

//             .file-name {
//               color: #666;
//               font-size: 0.875rem;
//             }
//           }
//         }

//         &.submit-section {
//           display: flex;
//           justify-content: center;
//           gap: 16px;

//           button {
//             background-color: #11074e;
//             color: white;
//             padding: 12px 32px;
//             border-radius: 8px;
//             font-size: 1rem;
//           }

//           .reset-btn {
//             background-color: #e8f0fe;
//             color: #1a237e;
//             border: 1px solid #90caf9;
//           }
//         }
//       }
//     }
//   }

//   .leave-balances {
//     display: grid;
//     grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
//     gap: 16px;
//     margin-bottom: 32px;

//     .balance-card {
//       display: flex;
//       align-items: center;
//       padding: 16px;
//       background: #ffffff;
//       border-radius: 8px;
//       box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

//       mat-icon {
//         margin-right: 12px;
//         color: #3f51b5;
//       }

//       .balance-info {
//         display: flex;
//         flex-direction: column;

//         .balance-label {
//           font-size: 0.9rem;
//           color: #666;
//           margin-bottom: 4px;
//         }

//         .balance-value {
//           font-size: 1.1rem;
//           font-weight: 500;
//           color: #333;
//         }
//       }
//     }
//   }

//   .selected-leave-balance {
//     margin-bottom: 24px;

//     .leave-type-balance {
//       display: flex;
//       align-items: center;
//       padding: 16px;
//       background: #e8eaf6;
//       border-radius: 6px;
//       border: 1px solid #3f51b5;

//       mat-icon {
//         margin-right: 12px;
//         color: #3f51b5;
//       }

//       .leave-type-info {
//         display: flex;
//         flex-direction: column;

//         .leave-type-label {
//           font-size: 0.9rem;
//           color: #333;
//           font-weight: 500;
//         }

//         .leave-type-value {
//           font-size: 0.85rem;
//           color: #666;
//         }
//       }
//     }
//   }

//   .form-container {
//     display: flex;
//     flex-direction: column;
//     gap: 24px;
//     max-width: 800px;
//     margin: 0 auto;
//   }

//   .leave-table-container {
//     .header {
//       margin-bottom: 32px;
//       text-align: left;

//       .page-title {
//         display: flex;
//         align-items: center;
//         gap: 12px;
//         font-size: 2.5rem;
//         font-weight: 300;
//         color: #333;
//         margin: 0;
//         padding-left: 24px;

//         mat-icon {
//           font-size: 2.5rem;
//           width: 2.5rem;
//           height: 2.5rem;
//           color: #11074e;
//         }
//       }

//       .subtitle {
//         background-color: #f4e7e6;
//         color: #a36416;
//         font-size: 1.1rem;
//         margin: 8px 0 0 24px;
//         padding: 12px 24px;
//         border-radius: 6px;
//       }
//     }

//     .table-card {
//       max-width: 1200px;
//       margin: 0 auto;
//       padding: 24px;
//       border-radius: 12px;
//       box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
//       border-left: 4px solid #11074e;

//       .leave-table {
//         width: 100%;
//         font-size: 0.875rem;
//         color: #333;

//         th {
//           font-weight: 500;
//           color: #666;
//           padding: 12px;
//           background-color: #f5f5f5;
//         }

//         td {
//           padding: 12px;
//           border-top: 1px solid #eee;
//         }
//       }

//       .no-data {
//         text-align: center;
//         font-size: 0.875rem;
//         color: #666;
//         padding: 24px 0;
//       }
//     }
//   }

//   @media (max-width: 768px) {
//     padding: 16px;

//     .nav-bar {
//       flex-direction: column;
//       gap: 8px;
//     }

//     .leave-apply-container {
//       .header .page-title {
//         font-size: 2rem;

//         mat-icon {
//           font-size: 2rem;
//           width: 2rem;
//           height: 2rem;
//         }
//       }

//       .apply-card {
//         padding: 16px;

//         .form-section.date-session-grid {
//           grid-template-columns: 1fr;
//           gap: 12px;
//         }
//       }
//     }

//     .leave-table-container {
//       .table-card {
//         padding: 16px;

//         .leave-table {
//           font-size: 0.75rem;

//           th, td {
//             padding: 8px;
//           }
//         }
//       }
//     }
//   }
// }


  