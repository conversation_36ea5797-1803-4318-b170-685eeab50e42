import { NgTemplateOutlet, DatePipe } from '@angular/common';
import { Component, computed, effect, inject, linkedSignal, OnInit, resource, signal, viewChild, WritableSignal } from '@angular/core';
import { FormBuilder, FormControl, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButton, MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon, MatIconModule } from '@angular/material/icon';
import { MatError, MatFormField, MatInput, MatPrefix } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { StatusType } from '../../../../enums/x_enums/dotnet/status-type';
import { AppRoutes } from '../../../../enums/app-routes';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { GunnyMovementComponent } from './gunny-movement.component';
import { GunnyMovementService } from '../../../../services/x_apis/gunny/gunny-movement.service';
import { GunnyMovements } from '../../../../models/x_models/gunny/gunny-movement';
import { Router } from '@angular/router';
import { GunnyformComponent } from '../../masters/gunny/gunnyform.component';
import { MatOption, MatSelect } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { provideNativeDateAdapter } from '@angular/material/core';
import { confirmAndDelete } from '../../../../models/x_models/masters/shared';
import { MatToolbarModule } from '@angular/material/toolbar';
@Component({
  selector: 'app-gunny-movements',
  imports: [MatIcon,
    MatInput,
    MatPrefix,
    ReactiveFormsModule,
    MatPaginatorModule,
    MatTableModule,
    NgTemplateOutlet,
    MatButtonModule,
    MatMenuModule,
    DatePipe,
    MatSortModule,
    MatFormFieldModule,
    TranslateModule,
    GunnyMovementComponent,
    GunnyformComponent,
    MatOption,
    MatDatepickerModule,
    MatSelect,
    FormsModule,
    MatError,
    MatFormField,
    MatButton,
    MatIconModule,
    MatToolbarModule
  ],
  providers: [provideNativeDateAdapter(), DatePipe],
  templateUrl: './gunny-movements.component.html',
  styleUrls: ['./gunny-movements.component.scss']
})
export class GunnyMovementsComponent implements OnInit {
  menuService = inject(MenuService);
  access = computed(() => this.menuService.activeMenu()?.controlAccess);
  translate = inject(TranslateService);

  paginator = viewChild<MatPaginator>(MatPaginator);
  sort = viewChild<MatSort>(MatSort);

  gunnyMovementService = inject(GunnyMovementService)
  // tableResource = resource({ loader: () => this.gunnyMovementService.get() });
  // dataSource = linkedSignal(() => new MatTableDataSource(this.tableResource.value()));
  dataSource = signal(new MatTableDataSource<GunnyMovements>([]));

  loading = signal(false);
  mode = signal<'view' | 'edit' | 'add'>('add');
  search = new FormControl("");
  showGrid = signal(false);
  status = StatusType;
  router = inject(Router);

  gunnyMovement = signal<GunnyMovements>({
    id: 0,
    fromStorageLocationName: '',
    toStorageLocationName: '',
    truckMemoId: 0,
    noOfBags: 0,
    acknowledgementStatusType: 0,
    acknowledgementStatusTypeName: ''
  });

  displayedColumns: string[] = [
    'fromStorageLocationName',
    'toStorageLocationName',
    'noOfBags',
    'acknowledgementStatusTypeName',
    'actions',
  ];

  gunnyMovements = signal<GunnyMovements[]>([]);
  protected readonly AppRoutes = AppRoutes;



  fb = inject(FormBuilder);

  form = this.fb.group({
    id: [0],
    fromDate: [new Date()],
    toDate: [new Date()],
  });

  constructor(private datePipe: DatePipe) {
    effect(() => {
      if (this.paginator()) {
        this.dataSource().sort = this.sort()!;
        this.dataSource().paginator = this.paginator()!
      }
    });
  }

  ngOnInit(): void {
    this.getLists();
  }

  async getLists() {

    const fromDate = this.form.value.fromDate ?? new Date();
    const toDate = this.form.value.toDate ?? new Date();

    // Convert Date objects to ISO 8601 strings
    const fromDateString = fromDate.toISOString();
    const toDateString = toDate.toISOString();

    try {
      const res: any = await this.gunnyMovementService.getGunnyList({ "fromDate": fromDateString, "toDate": toDateString });
      if (res) {
        const dataArray: GunnyMovements[] = Array.isArray(res) ? res : [res];
        this.dataSource.set(new MatTableDataSource(dataArray));
        setTimeout(() => {
          this.dataSource().sort = this.sort()!;
          this.dataSource().paginator = this.paginator()!;
        }, 100);
      } else {
        console.log('No Data Found in Qr getById');
      }
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  }

  onAdd() {
    this.gunnyMovement.set({
      id: 0,
      fromStorageLocationName: '',
      toStorageLocationName: '',
      truckMemoId: 0,
      noOfBags: 0,
      acknowledgementStatusType: 0,
      acknowledgementStatusTypeName: ''
    });
    this.mode.set("add")
    this.showGrid.set(true)
  }

  onView(data: GunnyMovements) {
    debugger
    this.gunnyMovement.set(data);
    this.mode.set('view');
    this.showGrid.set(true)
  }

  onEdit(data: GunnyMovements) {
    this.gunnyMovement.set(data);
    this.mode.set('edit');
    this.showGrid.set(true);
  }

  onClose() {
    this.showGrid.set(false)
    this.getLists();
    this.search.setValue("");
  }

  onRefresh() {
    this.getLists();
    this.search.setValue("");
  }

  onScanAdd(data: GunnyMovements) {
    this.router.navigate(['/x/gunnyqrscanner']);
  }

  async onDelete(data: GunnyMovements) {
    await confirmAndDelete(data, data.id, 'Gunny Movement', this.gunnyMovementService, () => this.getLists());
  }

  onSearch() {
    const filterValue = this.search.value?.trim().toLowerCase() || '';
    this.dataSource().filter = filterValue;
  }

  onDateChange(event: any) {
    this.getLists();
  }

  sentDpc(data: any) {
    this.gunnyMovementService.sendtoDpc(data.id);
  }

  getStatusColor(statusType: number): string {
    switch (statusType) {
      case 1: return '#b5e1b5'; // Green
      case 2: return '#eeefc0'; // Yellow
      case 3: return '#c0e6ef'; // Blue
      case 4: return '#ccc9e3'; // Purple
      default: return '#efcfcf'; // Red
    }
  }

}
