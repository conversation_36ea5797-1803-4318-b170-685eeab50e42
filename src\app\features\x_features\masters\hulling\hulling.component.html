<mat-toolbar color="primary" class="navbar">
  <nav aria-label="Breadcrumb navigation" style="display: flex; align-items: center; flex-wrap: wrap;">
    <a routerLink="/" class="crumb">{{'Master' | translate}}</a>
    <mat-icon>chevron_right</mat-icon>
    <a class="crumb" (click)="goBack()">{{menuService.activeMenu()?.title}}</a>
    <mat-icon>chevron_right</mat-icon>
    <span aria-current="page" class="nav-active">
      @if(uniqueId() == 0) {
      {{'New' | translate}} {{menuService.activeMenu()?.title}}
      } @else {
        {{hulling()?.hullingName}}
      }
    </span>
    @if(mode() === "view") {
    <button (click)="onEdit()" title="edit" class="btn-edit btn-icon-unstyled">
      <mat-icon class="material-symbols-rounded">edit_square</mat-icon>
    </button>
    }
  </nav>
  <button mat-flat-button color="primary" (click)="goBack()" aria-label="Go back" class="back-btn">
    <mat-icon>arrow_back_ios</mat-icon>
    Back
  </button>
</mat-toolbar>

  <div class="card component">

    <form [formGroup]="form" (ngSubmit)="onSubmit()">
      <div class="form">

        <div class="field">
          <label for="agencyId" class="required-label">{{'Agency' | translate }}</label>
          <mat-form-field appearance="outline">
            <mat-select id="agencyId" formControlName="agencyId" (openedChange)="resetSearch('agency')">
             <mat-form-field class="select-search hide-subscript" appearance="outline">
                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                <input #search3 autocomplete="off" matInput placeholder="Search" (input)="filter($event,'agency')">
              </mat-form-field>
              @for (role of filteredAgency(); track role) {
              <mat-option [value]="role.key">{{role.value}}</mat-option>
              }
            </mat-select>
            <mat-error>
              @if(form.controls.agencyId.errors?.['required']) {
              <!-- Agency is required -->
              {{'Agency' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>


        <div class="field">
          <label for="regionId" class="required-label">{{'Region' | translate }}</label>
          <mat-form-field appearance="outline">
            <mat-select id="regionId" formControlName="regionId" (openedChange)="resetSearch('region')">
              <mat-form-field class="select-search hide-subscript" appearance="outline">
                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                <input #search1 autocomplete="off" matInput placeholder="Search" (input)="filter($event,'region')">
              </mat-form-field>
              @for (role of filteredRegions(); track role) {
              <mat-option [value]="role.key">{{role.value}}</mat-option>
              }
            </mat-select>
            <mat-error>
              @if(form.controls.regionId.errors?.['required']) {
              <!-- Region is required -->
              {{'Region' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>


        <div class="field">
          <label for="talukId" class="required-label">{{'Taluk' | translate }}</label>
          <mat-form-field appearance="outline">
            <mat-select id="talukId" formControlName="talukId" (openedChange)="resetSearch('taluk')">
              <mat-form-field class="select-search hide-subscript" appearance="outline">
                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                <input #search2 autocomplete="off" matInput placeholder="Search" (input)="filter($event,'taluk')">
              </mat-form-field>
              @for (role of filteredTaluks(); track role) {
              <mat-option [value]="role.key">{{role.value}}</mat-option>
              }
            </mat-select>
            <mat-error>
              @if(form.controls.talukId.errors?.['required']) {
              <!-- Taluk is required -->
              {{'Taluk' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>


        @if(mode() === "view" || mode() === "edit") {
        <div class="field">
          <label for="hullingCode" class="required-label">{{'Hulling' | translate }} {{'Code' | translate }}</label>
          <mat-form-field>
            <input id="hullingCode" formControlName="hullingCode" matInput maxlength="100" readonly>
          </mat-form-field>
        </div>
        }



        <div class="field">
          <label for="hullingName" class="required-label">{{'MillName' | translate }}</label>
          <mat-form-field>
            <input id="hullingName" formControlName="hullingName" matInput (input)="onInput($event,'/^[0-9A-Za-z ]*$/')"
              maxlength="100">
            <mat-error>
              @if(form.controls.hullingName.errors?.['required']) {
              <!-- Mill name is required -->
              {{'MillName' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="hullingRegionalName" class="required-label">{{'MillName' | translate }} {{'InTamil' | translate
            }}</label>
          <mat-form-field>
            <input id="hullingRegionalName" formControlName="hullingRegionalName" matInput
              (input)="onInput($event,'/^[a-zA-Z0-9 ]*$/')" maxlength="100">
            <mat-error>
              @if(form.controls.hullingRegionalName.errors?.['required']) {
              <!-- Mill name in tamil is required -->
              {{'MillName' | translate }} {{'InTamil' | translate }} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="address" class="required-label">{{'MillAddress' | translate }}</label>
          <mat-form-field>
            <textarea matInput formControlName="address" maxlength="250" rows="2"></textarea>
            <mat-error>
              @if(form.controls.address.errors?.['required']) {
              <!-- Mill address is required -->
              {{'MillAddress' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="pincode" class="required-label">{{'MillPincode' | translate }}</label>
          <mat-form-field>
            <input id="pincode" formControlName="pincode" matInput (input)="onInput($event,'/^[0-9]*$/')" maxlength="6"
              minlength="6">
            <mat-error>
              @if(form.controls.pincode.errors?.['required']) {
              <!-- Mill pincode is required -->
              {{'MillPincode' | translate}} {{'IsRequired' | translate}}
              }
              @else if (form.controls.pincode.errors?.['minlength']) {
              {{'InvalidFormat' | translate}}
              }
              @else if (form.controls.pincode.errors?.['maxlength']) {
              {{'InvalidFormat' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>


        <div class="field">
          <label for="latitude" class="required-label">{{'MillLatitude' | translate }}</label>
          <mat-form-field>
            <input id="latitude" formControlName="latitude" matInput (input)="onInput($event,'/^[0-9.]*$/')">
            <mat-error>
              @if(form.controls.latitude.errors?.['required']) {
              <!-- Mill latitude is required -->
              {{'MillLatitude' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>


        <div class="field">
          <label for="longitude" class="required-label">{{'MillLongitude' | translate }}</label>
          <mat-form-field>
            <input id="longitude" formControlName="longitude" matInput (input)="onInput($event,'/^[0-9.]*$/')">
            <mat-error>
              @if(form.controls.longitude.errors?.['required']) {
              <!-- Mill longitude is required -->
              {{'MillLongitude' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="landmark" class="required-label">{{'MillLandmark' | translate }}</label>
          <mat-form-field>
            <input id="landmark" formControlName="landmark" matInput (input)="onInput($event,'/^[0-9A-Za-z ]*$/')"
              maxlength="100">
            <mat-error>
              @if(form.controls.landmark.errors?.['required']) {
              <!-- Mill landmark is required -->
              {{'MillLandmark' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="millerName" class="required-label">{{'MillerName' | translate }}</label>
          <mat-form-field>
            <input id="millerName" formControlName="millerName" matInput (input)="onInput($event,'/^[0-9A-Za-z ]*$/')"
              maxlength="100">
            <mat-error>
              @if(form.controls.millerName.errors?.['required']) {
              <!-- Miller name is required -->
              {{'MillerName' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>


        <div class="field">
          <label for="presentAddress" class="required-label">{{'MillerPresentAddress' | translate }}</label>
          <mat-form-field>
            <textarea matInput formControlName="presentAddress" maxlength="250" rows="2"></textarea>
            <mat-error>
              @if(form.controls.presentAddress.errors?.['required']) {
              <!-- Miller present address is required -->
              {{'MillerPresentAddress' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="millType" class="required-label">{{'MillType' | translate }}</label>
          <mat-form-field appearance="outline">
            <mat-select id="millType" formControlName="millType" (openedChange)="resetSearch('mill')">
              <mat-form-field class="select-search hide-subscript" appearance="outline">
                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                <input #search4 autocomplete="off" matInput placeholder="Search" (input)="filter($event,'mill')">
              </mat-form-field>
              @for (role of filteredMill(); track role) {
              <mat-option [value]="role.key">{{role.value}}</mat-option>
              }
            </mat-select>
            <mat-error>
              @if(form.controls.millType.errors?.['required']) {
              <!-- Mill type is required -->
              {{'MillType' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="factoryLicenseNo" class="required-label">{{'FactoryLicenseNo' | translate }}</label>
          <mat-form-field>
            <input id="factoryLicenseNo" formControlName="factoryLicenseNo" matInput
              (input)="onInput($event,'/^[0-9A-Za-z ]*$/')" maxlength="20">
            <mat-error>
              @if(form.controls.factoryLicenseNo.errors?.['required']) {
              <!-- Factory license no is required -->
              {{'FactoryLicenseNo' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>


        <div class="field">
          <label for="factoryLicenseExpiryTs" class="required-label">{{'FactoryLicenseUpto' | translate }}</label>
          <mat-form-field>
            <input matInput appDateInput [matDatepicker]="picker1" formControlName="factoryLicenseExpiryTs">
            <mat-datepicker-toggle matIconSuffix [for]="picker1"></mat-datepicker-toggle>
            <mat-datepicker #picker1></mat-datepicker>
            <mat-error>
              @if(form.controls.factoryLicenseExpiryTs.errors?.['required']) {
              <!-- Factory license upto is required -->
              {{'FactoryLicenseUpto' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>


        <div class="field">
          <label for="boilerLicenseNo" class="required-label">{{'BoilerLicenseNo' | translate }}</label>
          <mat-form-field>
            <input id="boilerLicenseNo" formControlName="boilerLicenseNo" matInput
              (input)="onInput($event,'/^[0-9A-Za-z ]*$/')" maxlength="20">
            <mat-error>
              @if(form.controls.boilerLicenseNo.errors?.['required']) {
              <!-- Boiler license no is required -->
              {{'BoilerLicenseNo' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>


        <div class="field">
          <label for="boilerLicenseExpiryTs" class="required-label">{{'BoilerLicenseUpto' | translate }}</label>
          <mat-form-field>
            <input matInput appDateInput [matDatepicker]="picker2" formControlName="boilerLicenseExpiryTs">
            <mat-datepicker-toggle matIconSuffix [for]="picker2"></mat-datepicker-toggle>
            <mat-datepicker #picker2></mat-datepicker>
            <mat-error>
              @if(form.controls.boilerLicenseExpiryTs.errors?.['required']) {
              <!-- Boiler license upto is required -->
              {{'BoilerLicenseUpto' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="boilerLicenseExpiryTs" class="required-label">{{'DateofAppointment' | translate }}</label>
          <mat-form-field>
            <input matInput appDateInput [matDatepicker]="picker3" formControlName="fromDate">
            <mat-datepicker-toggle matIconSuffix [for]="picker3"></mat-datepicker-toggle>
            <mat-datepicker #picker3></mat-datepicker>
            <mat-error>
              @if(form.controls.fromDate.errors?.['required']) {
              <!-- Date of appointment is required -->
              {{'DateofAppointment' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="toDate" class="required-label">{{'ToDate' | translate }}</label>
          <mat-form-field>
            <input matInput appDateInput [matDatepicker]="picker4" formControlName="toDate" [min]="form.value.fromDate">
            <mat-datepicker-toggle matIconSuffix [for]="picker4"></mat-datepicker-toggle>
            <mat-datepicker #picker4></mat-datepicker>
            <mat-error>
              @if(form.controls.toDate.errors?.['required']) {
              <!-- To date is required -->
              {{'ToDate' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="validFrom" class="required-label">{{'AgreementValidFrom' | translate }}</label>
          <mat-form-field>
            <input matInput appDateInput [matDatepicker]="picker5" formControlName="validFrom">
            <mat-datepicker-toggle matIconSuffix [for]="picker5"></mat-datepicker-toggle>
            <mat-datepicker #picker5></mat-datepicker>
            <mat-error>
              @if(form.controls.validFrom.errors?.['required']) {
              <!-- Application valid from is required -->
              {{'AgreementValidFrom' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="validTo" class="required-label">{{'AgreementValidTo' | translate }}</label>
          <mat-form-field>
            <input matInput appDateInput [matDatepicker]="picker6" formControlName="validTo"
              [min]="form.value.validFrom">
            <mat-datepicker-toggle matIconSuffix [for]="picker6"></mat-datepicker-toggle>
            <mat-datepicker #picker6></mat-datepicker>
            <mat-error>
              @if(form.controls.validTo.errors?.['required']) {
              <!-- Application valid to is required -->
              {{'AgreementValidTo' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="mrmType" class="required-label">{{'MRMType' | translate }}</label>
          <mat-form-field appearance="outline">
            <mat-select id="mrmType" formControlName="mrmType" (openedChange)="resetSearch('mrm')">
              <mat-form-field class="select-search hide-subscript" appearance="outline">
                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                <input #search5 autocomplete="off" matInput placeholder="Search" (input)="filter($event,'mrm')">
              </mat-form-field>
              @for (role of filteredMRM(); track role) {
              <mat-option [value]="role.key">{{role.value}}</mat-option>
              }
            </mat-select>
            <mat-error>
              @if(form.controls.mrmType.errors?.['required']) {
              <!-- MRM type is required -->
              {{'MRMType' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="gstNo" class="required-label">{{'GSTNo' | translate }}</label>
          <mat-form-field>
            <input id="gstNo" formControlName="gstNo" matInput
              (input)="onInput($event,'/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/')" maxlength="15">
            <mat-error>
              @if(form.controls.gstNo.errors?.['required']) {
              <!-- GST no is required
              {{'GunnyShortCode' | translate}} {{'IsRequired' | translate}} -->
              {{'GSTNo' | translate}} {{'IsRequired' | translate}}
              }
              @else if (form.controls.gstNo.errors?.['invalidGst']) {
              Invalid GST Format
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="panNo" class="required-label">{{'PANNo' | translate }}</label>
          <mat-form-field>
            <input id="panNo" formControlName="panNo" matInput (input)="onInput($event,'/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/')"
              maxlength="10">
            <mat-error>
              @if(form.controls.panNo.errors?.['required']) {
              <!-- PAN no is required -->
              {{'PANNo' | translate}} {{'IsRequired' | translate}}
              }
              @else if (form.get('panNo')?.hasError('invalidPan')) {
              Invalid PAN Format
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="paddyType" class="required-label">{{'PaddyType' | translate }}</label>
          <mat-form-field appearance="outline">
            <mat-select id="paddyType" formControlName="paddyType" (openedChange)="resetSearch('paddy')">
               <mat-form-field class="select-search hide-subscript" appearance="outline">
                <mat-icon class="material-symbols-outlined icon-search" matPrefix>search</mat-icon>
                <input #search6 autocomplete="off" matInput placeholder="Search" (input)="filter($event,'paddy')">
              </mat-form-field>
              @for (role of filteredPaddy(); track role) {
              <mat-option [value]="role.key">{{role.value}}</mat-option>
              }
            </mat-select>
            <mat-error>
              @if(form.controls.paddyType.errors?.['required']) {
              <!-- Paddy type is required -->
              {{'PaddyType' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="fortifiedType" class="required-label">{{'FortifiedType' | translate }}</label>
          <mat-radio-group aria-labelledby="example-radio-group-label" class="example-radio-group"
            formControlName="fortifiedType">
            <mat-radio-button class="example-radio-button" [value]="1" checked>FRK</mat-radio-button>
            <mat-radio-button class="example-radio-button" [value]="2">NON-FRK</mat-radio-button>
          </mat-radio-group>
          <!-- <mat-error>
            @if(form.controls.fortifiedType.errors?.['required']) {
            Fortified type is required
            }
          </mat-error> -->
        </div>

        <div class="field">
          <label for="isGeneratorAvailable" class="required-label">{{'IsGeneratorAvailable?' | translate }}</label>
          <mat-radio-group aria-labelledby="example-radio-group-label" class="example-radio-group"
            formControlName="isGeneratorAvailable">
            <mat-radio-button class="example-radio-button" [value]="true" checked>Yes</mat-radio-button>
            <mat-radio-button class="example-radio-button" [value]="false">No</mat-radio-button>
          </mat-radio-group>
          <!-- <mat-error>
            @if(form.controls.fortifiedType.errors?.['required']) {
            Fortified type is required
            }
          </mat-error> -->
        </div>

        <div class="field">
          <label for="generatorCapacity" class="required-label">{{'GeneratorCapacity' | translate }}</label>
          <mat-form-field>
            <input id="generatorCapacity" formControlName="generatorCapacity" matInput maxlength="20" type="number">
            <mat-error>
              @if(form.controls.generatorCapacity.errors?.['required']) {
              <!-- Generator capacity is required -->
              {{'GeneratorCapacity' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="modelNo" class="required-label">{{'GeneratorModelNo' | translate }}</label>
          <mat-form-field>
            <input id="modelNo" formControlName="modelNo" matInput (input)="onInput($event,'/^[0-9A-Za-z ]*$/')"
              maxlength="30">
            <mat-error>
              @if(form.controls.modelNo.errors?.['required']) {
              <!-- Generator model no is required -->
              {{'GeneratorModelNo' | translate}} {{'IsRequired' | translate}}
              }
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field">
          <label for="isSeparateEbMeter" class="required-label">{{'IsSeparateEBMeter?' | translate }}</label>
          <mat-radio-group aria-labelledby="example-radio-group-label" class="example-radio-group"
            formControlName="isSeparateEbMeter">
            <mat-radio-button class="example-radio-button" [value]="true" checked>Yes</mat-radio-button>
            <mat-radio-button class="example-radio-button" [value]="false">No</mat-radio-button>
          </mat-radio-group>
          <!-- <mat-error>
            @if(form.controls.fortifiedType.errors?.['required']) {
            Fortified type is required
            }
          </mat-error> -->
        </div>


      </div>

      <div class="actions">
       
        @if(editable() === true) {
        @if(mode() === 'add') {
        <button type="button" mat-flat-button (click)="formReset()" class="btn btn-danger">{{'Reset' |
          translate}}</button>
        }
        <button [disabled]="editable() === false" mat-flat-button class="btn btn-theme" type="submit">{{uniqueId() == 0
          ? ('Create' | translate) : ('Update' | translate)}}</button>
        }
      </div>


    </form>
  </div>
