export interface Procuretoken {
    id:number
    requestedType: number
    entryTs:string
    tokenDate: string
    tokenNo: string
    dpcId: number
    farmerId: number
    landId: [],
    productId:number
    varietyId: number
    seasonId: number
    farmerName:string
    productName: string
    varietyName: string
    seasonName: string
    dpcName: string
    landViewModel: [
      {
        id: number
        farmerName: null,
        subDivsion: string
        landTypeName: string
        expectedYield:number
        surveyNo: number
        regionName: string
        unitName: string
        talukName: string
        blockName:string
        villageName: string
      }
    ],
    noofBags: number
    noofProcuredBags:number
    procurementStatusType: number
    procurementStatusTypeName: string
    requestedTypeName: string
  }

export interface Procuretokens {
    id: number
    tokenNo: string
    tokenDate: string
    dpcNameCode: string
    farmerNameCode: string
    productName: string
    noofBags: number
    requestedTypeName: string
    noofProcuredBags: number
    procurementStatusTypeName: string
}

export type ProcuretokenResponse = Procuretokens[]