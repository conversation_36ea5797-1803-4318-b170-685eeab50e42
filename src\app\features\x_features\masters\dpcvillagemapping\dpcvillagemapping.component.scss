.border {
  border: 1px solid #80808073;
  box-shadow: 5px 5px 5px 5px #9e9e9e47;
  min-height: 300px;
  border-radius: 3px;
  cursor: pointer;
  max-height: 100px;
  overflow-y: scroll;
}

.sty {
      color: gray;
    font-size: 56px;
    cursor: pointer;
    padding: 25px
}

.sty:hover {
  color: green;
   font-size: 56px;
    cursor: pointer;
    padding: 25px
}
.sty1 {
      color: gray;
    font-size: 56px;
    cursor: pointer;
    padding: 25px;
    transform: rotate(180deg);
}

.sty1:hover {
  color: green;
   font-size: 56px;
    cursor: pointer;
    padding: 25px
}
.container
{
    display: flex;
    // grid-template-columns: auto auto auto auto auto;
    padding:20px 20px 0px 0px;
    gap: 20px;
}
.wid-25
{
    width: 25%;
}

mat-form-field {
  max-width: 100%;
}

.back-btn {
  padding-bottom: 10px;
  padding-top: 10px;
}

.navbar {
  padding-left: 30px;
  padding-right: 30px;
}