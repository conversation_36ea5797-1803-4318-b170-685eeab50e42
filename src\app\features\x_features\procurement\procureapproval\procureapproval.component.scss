.mat-checkbox {
  color: white;
  vertical-align: middle;
}

.mat-input-element {
  width: 100%;
}

.error-msg {
   color:red;
}


.grid-container {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  gap: 1rem;
}

.gauge-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

ngx-gauge {
  width: 300px;
  height: 200px;
  margin: 20px;
}

.row-12 {
  grid-column: span 12;
}

.row-8 {
  grid-column: span 8;
}

.row-4 {
  grid-column: span 4;
}

.row-6 {
  grid-column: span 6;
}

.row-3 {
  grid-column: span 3;
}

@media (max-width: 768px) {
  .grid-container {
    grid-template-columns: repeat(12, 1fr);
  }

  .row-8 {
    grid-column: span 12;
    text-align: left;
  }

  .row-4 {
    grid-column: span 12;
  }
  .row-3 {
      grid-column: span 12;
    }

  .row-6 {
    grid-column: span 12;
  }

  .actions {
    justify-content: flex-start;
  }

  .card-container {
    grid-template-columns: 1fr;
  }
}
.header {
  margin-top: 40px;
}

.vertical-tabs .mat-tab-labels {
  flex-direction: column;
}


// ::ng-deep .mat-tab-label.mat-tab-label-active {
//   background-color: #1976d2; /* Active tab background */
//   color: white;             /* Active tab text color */
//   font-weight: bold;        /* Active tab font weight */
//   opacity: 1 !important;    /* Ensure full opacity */
// }

// ::ng-deep .mat-ink-bar {
//   background-color: #1976d2; /* Ink bar color */
// }

// ::ng-deep .mat-tab-label.mat-tab-label-active:focus,
// ::ng-deep .mat-tab-label.mat-tab-label-active:hover {
//   background-color: #1976d2; /* Keep active tab background */
//   color: white;              /* Keep active tab text color */
// }

