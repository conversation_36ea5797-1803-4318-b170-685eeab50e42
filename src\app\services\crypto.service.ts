import { Injectable } from '@angular/core';
import { pki, util } from 'node-forge';

@Injectable({
  providedIn: 'root'
})
export class CryptoService {
  publicKey = pki.publicKeyFromPem(`-----BEGIN PUBLIC KEY-----
MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAiw8uLPR/pHF28rfG1+Iy
QQ/oB0jZRwOhEAlC5DZjqS721OfkD7Zn3ZatE71rO5/P0Qs8bHVyCAGvUr+CckDl
OM6Kk46z6zNgqplMB9+wY7Y1F0EJ+tklmggFblFa9AXS5hLXoNuB4OgvrdBvyPd9
Zm3TUlii8PIq6Xjt4GN6HYF9gALsK90hh+sKyI3rLXXrbF76sdJtEXRqmlb6h5ft
y9pIjrkQr5/cSfDjysyyszQkZEOLQuHpFcyaT6fETaEISl5WxMQZBO9lvMYogMcH
BrDj/jFZ8PJQLImAZUnU+nbSL503tK6K73sst87OG4ikmFUmGy22fz7dlqgtPj39
bzcayFmRJwPOTcItRGK3XtddhrZXC1CnEurXouHhKugbqXCrpfWDnh/493I59Mt2
chtL0BrYOyzs8stYGlgjB1/H0J/uC+5iURA7z7Jt4mhbNX+JmS/84zha5B15uIfF
QV88/nSoGB6wqnfpSyo466uL3sk56xomAyPiqbKKR/WI+j4JuCXHg4zJlMXq6cxk
fpdgyw7P7AWvmY5pIOjpGCGkEXdwJB7j1B49knefvIjMK5aV6XU+hfaPGZ8vwJCC
PzNxr8akxv73CvVncZ9kCsMWXi+BiNXwLIklpWzc4L3BUL5bMhi1hKlMQl4dUXlp
d+24iz7AHpUyuR2gFQya4ScCAwEAAQ==
-----END PUBLIC KEY-----`);

privateKey = pki.privateKeyFromPem(`**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`)

  encrypt(value: string) {
    return this.publicKey.encrypt(value)
  }

  decrypt(value: string) {
    return this.privateKey.decrypt(value)
  }

  encode64(value: string) {
    return util.encode64(value)
  }

  decode64(value: string) {
    return util.decode64(value)
  }

  encryptEncode(value: string) {
    return this.encode64(this.encrypt(value))
  }

  decodeDecrypt(value: string) {
    return this.decrypt(this.decode64(value))
  }
}
