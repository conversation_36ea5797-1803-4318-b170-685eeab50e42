form {
    padding-left: 10px;
    padding-right: 10px;
    margin-bottom: 20px;
}

.page-header1 {
    padding: 10px 10px !important;
}

.grid-container {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    gap: 1rem;
    margin-bottom:20px
}


.row-12 {
    grid-column: span 12;
}

.row-8 {
    grid-column: span 8;
}

.row-6 {
    grid-column: span 6;
}
.row-9 {
    grid-column: span 9;
}

.row-3 {
    grid-column: span 3;
}

.row-4 {
    grid-column: span 4;
}

.row-6 {
    grid-column: span 6;
}

@media (max-width: 768px) {
    .grid-container {
        grid-template-columns: repeat(12, 1fr);
    }

    .row-8 {
        grid-column: span 12;
        text-align: left;
    }

    .row-9 {
        grid-column: span 12;
        text-align: left;
    }

    .row-4 {
        grid-column: span 12;
    }

    .row-3 {
        grid-column: span 12;
    }

    .row-6 {
        grid-column: span 12;
    }

    .actions {
        justify-content: flex-start;
    }

    .card-container {
        grid-template-columns: 1fr;
    }
}

mat-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left:30px;
    padding-right: 20px;
    box-shadow: 0 0 20px rgba(6, 10, 135, 0.2);
  }

  .nav-aln{
    display: flex; 
    align-items: center; 
    flex-wrap: wrap;
  }
  
  button {
    display: inline-flex;
    align-items: center;
    padding: 8px 16px;
  }
  
  mat-icon {
    margin-right: 10px;
    margin-left: 10px;
  }
  
  .navbar {
    position: sticky;
    top: 0;
    background-color: #fff;
    z-index: 1000;
  }
  
  .crumb {
    font-size: 15px;
  }
  
  .nav-active {
    color: rgb(17, 17, 214);
  }
  
  @media (max-width: 600px) {
    .navbar {
      flex-direction: column;
      align-items: flex-start;
    }
  
    nav {
      width: 100%;
      margin-bottom: 10px;
    }
  
    button {
      width: 100%;
      justify-content: flex-start;
    }
  }

  mat-form-field {
    max-width:100%;
  }

// test
.summary-card {
    width: 100%;
    max-width: 800px;
    margin: 20px auto;
    padding: 16px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    background-color: #fff;
  }
  
  .summary-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
  
  .summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-radius: 8px;
    background-color: #d9f1ff;
    box-shadow: 5px 2px 4px 0px rgb(33 57 225 / 10%);
  }
  
  .subject-name {
    font-size: 1rem;
    color: #333;
  }
  
  .badge {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #2196f3;
    color: white;
    font-weight: bold;
    font-size: 1rem;
  }
  