export interface ControlAccess {
  canAdd: boolean
  canEdit: boolean
  canView: boolean
  canDelete: boolean
  canDeActivate: boolean
  canActivate: boolean
  canReset: boolean
  canHistory: boolean
  canDownload: boolean
  canApprove:boolean,
  canReject:boolean,
  canSendBack:boolean
}

export interface Menu {
    id: number
    parentId: any
    path: string
    isTitle?: boolean
    title: string
    icon: string
    active?: boolean
    show?: boolean
    class: string
    badge: string
    badgeClass: string
    isExternalLink: boolean
    sequenceNo: number
    isVisible: boolean
    controlAccess: ControlAccess
    submenu: Menu[]
}
