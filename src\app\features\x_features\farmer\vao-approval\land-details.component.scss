table {
  tr {
    grid-template-columns: minmax(150px, 200px) minmax(200px, 250px) minmax(200px, 250px) minmax(145px, 160px) minmax(80px, 110px) 164px;
    min-width: 100%;
  }
}

mat-form-field {
  max-width: 100%;
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  gap: 1rem;
}

.row-8 {
  grid-column: span 9;
}

.row-4 {
  grid-column: span 3;
}


.mat-mdc-form-field {
  width: 100%;
}

.actions {
  flex: 8;
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.card-container {
  background-color: white;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 25px;

}

.card-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.text-content {
  flex: 1;
  padding-right: 16px;
}

@media (max-width: 768px) {
  .grid-container {
    grid-template-columns: repeat(12, 1fr);
  }

  .row-8 {
    grid-column: span 12;
    text-align: left;
  }

  .row-4 {
    grid-column: span 12;
  }

  .actions {
    justify-content: flex-start;
  }

  .card-container {
    grid-template-columns: 1fr;
  }
}



.field mat-form-field {
  width: 100%;
  max-width: 100%;
}


mat-card {
  display: flex;
  flex-direction: column;
}



.card-image {
  width: 100px;
  height: auto;
  object-fit: cover;
}

.form-container1 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.field1 {
  flex: 5;
}



.status-container {
  display: inline-flex;
  align-items: center;
}

.status-icon {
  width: 30px;
  height: 30px;
  margin-right: 8px;
}

.tracking {
  width: 30px;
  height: 30px;
}

// .mat-mdc-card {
//   background-color:white !important ;
//   box-shadow: 0 5px 0 0 #0f8217, 0 0px 27px 0 rgba(0, 0, 0, 0.08) !important;
// };

.card1 {
  background-color: white;
  border-radius: 8px;
  padding: 10px;
  margin-bottom: 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease-in-out;
  cursor: pointer;
}

.card1:hover{
  transform:scale(1.05);
}

.shadow-primary {
     box-shadow: 0 5px 0px 0 rgb(13 50 134 / 75%), 3px 2px 8px 0 rgba(2, 14, 85, 0.4) !important;
}

.shadow-success {
  box-shadow:0 5px 0px 0 rgba(76 175 80 / 75%), 3px 2px 8px 0 rgba(2, 14, 85, 0.4) !important;
}

.shadow-warning {
  box-shadow:0 5px 0px 0 rgba(255 152 0 / 75%), 3px 2px 8px 0 rgba(2, 14, 85, 0.4)  !important; 
}

.shadow-danger {
  box-shadow:0 5px 0px 0 rgba(244 67 54 / 75%), 3px 2px 8px 0 rgba(2, 14, 85, 0.4) !important; 
}

.selected-pending {
  background-color: rgb(218 227 248 / 53%); 
}

.selected-approved {
  background-color: rgb(227 254 228 / 53%); 
}

.selected-rejected {
  background-color: rgb(247 222 220 / 56%);
}

.selected-partial {
  background-color: rgb(251 235 211 / 50%); 
}