import { inject, Injectable } from '@angular/core';
import { DataService } from '../../data.service';
import { Response } from '../../../models/x_models/api-response';
import { Provider, ProviderResponse } from '../../../models/x_models/masters/provider';

@Injectable({
  providedIn: 'root'
})
export class ProviderService {

  dataService = inject(DataService)

  create(data: Provider) {
    return this.dataService.post<Response>("/provider", data)
  }

  get() {
    return this.dataService.get<ProviderResponse>("/provider")
  }

  getById(id: number) {
    return this.dataService.get<Provider>(`/provider/${id}`)
  }

  delete(id: number) {
    return this.dataService.delete<Response>(`/provider/${id}`)
  }

}
