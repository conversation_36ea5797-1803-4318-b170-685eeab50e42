.visually-hidden {
    position: absolute;
    overflow: hidden;
    clip: rect(0 0 0 0);
    height: 1px;
    width: 1px;
    margin: -1px;
    padding: 0;
    border: 0;
}

.phone {
  display: flex;
  flex-wrap: wrap;
  column-gap: 5px;

  .code {
      width: 60px;
  }

  .number {
      max-width: calc(100% - 65px);
  }
}

.centerize {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
}

.hide {
  display: none !important;
}

.card {
  background-color: #fff;
  box-shadow: 0 0 10px 0 rgba(183, 192, 206, .2);
  border-radius: var(--4px);
  // border-width: 0;
  padding: 20px;
}
