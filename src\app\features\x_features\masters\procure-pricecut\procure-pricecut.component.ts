import { Component, computed, ElementRef, inject, input, output, resource, signal, viewChild } from '@angular/core';
import { Form<PERSON>uilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButton } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormField, MatInput, MatError } from '@angular/material/input';
import { MatOption, MatSelect } from '@angular/material/select';
import { Router } from '@angular/router';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { DateInputDirective } from '../../../../directives/date-input.directive';
import { MatNativeDateModule } from '@angular/material/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { LookupService } from '../../../../services/x_apis/lookup.service';
import { AlertService } from '../../../../services/alert.service';
import { InputformatService } from '../../../../services/x_apis/inputformat.service';
import { ProcurePricecut } from '../../../../models/x_models/masters/procure-pricecut';
import { ProcurePricecutService } from '../../../../services/x_apis/masters/procure-pricecut.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MenuService } from '../../../../services/x_apis/menu.service';
import { MatToolbarModule } from '@angular/material/toolbar';

@Component({
  selector: 'app-procure-pricecut',
  imports: [FormsModule,
    MatFormField,
    MatInput,
    ReactiveFormsModule,
    MatError,
    MatOption,
    MatSelect,
    MatButton,
    MatIconModule,
    MatDatepickerModule,
    DateInputDirective,
    MatNativeDateModule,
    TranslateModule,
    MatToolbarModule,
    MatFormFieldModule],
  templateUrl: './procure-pricecut.component.html',
  styleUrl: './procure-pricecut.component.scss'
})
export class ProcurePricecutComponent {
  menuService = inject(MenuService);
  lookupService = inject(LookupService);
  procurePricecutService = inject(ProcurePricecutService);
  alertService = inject(AlertService);
  inputFormatService = inject(InputformatService);

  translate = inject(TranslateService);
  regions = resource({ loader: () => this.lookupService.getRegion() }).value;
  seasons = resource({ loader: () => this.lookupService.getSeason() }).value;

  products = resource({ loader: () => this.lookupService.getProduct() }).value; // for temp
  pricetypes = resource({ loader: () => this.lookupService.getprocurementPriceCut() }).value; // for temp


  mode = input.required<string>();
  closed = output<boolean>();
  fb = inject(FormBuilder);
  editable = signal<boolean>(true);
  router = inject(Router);

  uniqueId = input.required<any>();
  procurementPriceCutTypeName = input.required<any>();

  procurepricecut = signal<ProcurePricecut | null>(null);
  search1 = viewChild.required<ElementRef<HTMLInputElement>>('search1')
  search2 = viewChild.required<ElementRef<HTMLInputElement>>('search2')
  search3 = viewChild.required<ElementRef<HTMLInputElement>>('search3')
  search4 = viewChild.required<ElementRef<HTMLInputElement>>('search4')


  form = this.fb.group({
    id: this.fb.control<number>(0),
    procurementPriceCutType: this.fb.control<number | null>(null, Validators.required),
    productId: this.fb.control<number | null>(null, Validators.required),
    seasonId: this.fb.control<number | null>(null, Validators.required),
    regionId: this.fb.control<number | null>(null),
    minRange: this.fb.control<number | null>(null, Validators.required),
    maxRange: this.fb.control<number | null>(null, Validators.required),
    priceCutAmount: this.fb.control<number | null>(null, Validators.required)
  });

  ngOnInit() {
    this.translate.onLangChange.subscribe(() => {
      this.fetchRegions();
    });
    if (this.mode() === "edit" || this.mode() === "view") {
      this.getFormData()
    }
  }

  readonly regionSearch = signal('');
  readonly seasonSearch = signal('');
  readonly productSearch = signal('');
  readonly pricetypeSearch = signal('');


  readonly filteredRegions = computed(() =>
    this.regions()?.filter(x =>
      x.value.toLowerCase().includes(this.regionSearch().toLowerCase())
    )
  );

  readonly filteredSeasons = computed(() =>
    this.seasons()?.filter(x =>
      x.value.toLowerCase().includes(this.seasonSearch().toLowerCase())
    )
  );

  readonly filteredProducts = computed(() =>
    this.products()?.filter(x =>
      x.value.toLowerCase().includes(this.productSearch().toLowerCase())
    )
  );
  
  readonly filteredPricetypes = computed(() =>
    this.pricetypes()?.filter(x =>
      x.value.toLowerCase().includes(this.pricetypeSearch().toLowerCase())
    )
  );

 
  filter(value: any,type:string) {
     if (type === 'region') {
      this.productSearch.set(value.target.value);
      this.search4().nativeElement.value = '';
    } 
    else if (type === 'season') {
      this.seasonSearch.set(value.target.value);
      this.search3().nativeElement.value = '';
    } 
    else if(type=='product') {
      this.productSearch.set(value.target.value);
      this.search2().nativeElement.value = '';
    }
    else{
      this.pricetypeSearch.set(value.target.value);
      this.search1().nativeElement.value = '';
    }
  }

    resetSearch(type: any) {

    if (type === 'product') {
      this.search2().nativeElement.value = '';
      this.productSearch.set('');
    } else if (type === 'region') {
      this.regionSearch.set('');
      this.search4().nativeElement.value = '';
    } 
     else if(type=='season')  {
      this.seasonSearch.set('');
      this.search3().nativeElement.value = '';
    } 
    else{
      this.pricetypeSearch.set('');
      this.search1().nativeElement.value = '';
    }

  }


  async fetchRegions() {
    this.regions.set(await this.lookupService.getRegion());
    this.products.set(await this.lookupService.getProduct());
  }

  async getFormData() {
    const res = await this.procurePricecutService.getById(this.uniqueId());
    this.procurepricecut.set(res)
    this.form.patchValue({ ...res });
    if (this.mode() === "view") {
      this.editable.set(false)
      this.form.disable()
    }
  }

  onEdit() {
    this.form.enable();
    this.editable.set(true)
  }

  async onSubmit() {
    if (this.form.valid) {
      const formValue = { ...this.form.value };
      const res = await this.procurePricecutService.create(formValue as ProcurePricecut)
      if (res.isSuccess) {
        this.closed.emit(true)
        if (formValue.id == 0) {
          this.alertService.success(`Procurement Price Cut Created successfully.`)
        }
        else {
          this.alertService.success(`Procurement Price Cut Updated successfully.`)
        }
      }
    } else {
      this.form.markAllAsTouched();
    }
  }

  goBack() {
    this.closed.emit(true)
  }

  formReset() {
    let indexId = this.form.value.id;
    this.form.reset();
    this.form.patchValue({
      id: indexId
    });
  }

  onInput(event: any, regexPattern: any): void {
    this.inputFormatService.isNumericOrHyphenOrSpace(event, regexPattern);
  }

}
