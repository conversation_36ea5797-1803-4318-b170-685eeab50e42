
<div class="leave-balance-container">
  <mat-card class="header-card">
    <mat-card-header class="form-header">
      <div class="header-content">
        <div class="logo-section">
          <mat-icon class="tncsc-logo">account_balance_wallet</mat-icon>
        </div>
        <div class="title-section">
          <h2 class="form-subtitle">Leave Balance Entry</h2>
          <p class="subtitle">Set the initial balance for each leave type</p>
        </div>
      </div>
    </mat-card-header>
  </mat-card>
  <mat-card class="apply-card">
<form [formGroup]="leaveBalanceForm" (ngSubmit)="onSubmit()" class="max-w-lg mx-auto p-6 bg-white shadow-md rounded-lg">
    <h2 class="text-2xl font-bold mb-6 text-center">Create Leave Balance</h2>

    <mat-form-field appearance="fill" class="w-full mb-4">
      <mat-label>Employee ID</mat-label>
      <input matInput formControlName="employeeId" placeholder="e.g., EMP001">
      <mat-error *ngIf="leaveBalanceForm.get('employeeId')?.hasError('required')">
        Employee ID is required
      </mat-error>
    </mat-form-field>

    <mat-form-field appearance="fill" class="w-full mb-4">
      <mat-label>Leave Type</mat-label>
      <mat-select formControlName="leaveTypeId">
        <mat-option *ngFor="let leaveType of leaveTypes" [value]="leaveType.leaveTypeId">
          {{ leaveType.leaveTypeName }}
        </mat-option>
      </mat-select>
      <mat-error *ngIf="leaveBalanceForm.get('leaveTypeId')?.hasError('required')">
        Leave Type is required
      </mat-error>
    </mat-form-field>

    <mat-form-field appearance="fill" class="w-full mb-4">
      <mat-label>Year</mat-label>
      <input matInput formControlName="year" type="number" placeholder="e.g., 2025">
      <mat-error *ngIf="leaveBalanceForm.get('year')?.hasError('required')">
        Year is required
      </mat-error>
      <mat-error *ngIf="leaveBalanceForm.get('year')?.hasError('min')">
        Year must be 2020 or later
      </mat-error>
    </mat-form-field>

    <mat-form-field appearance="fill" class="w-full mb-4">
      <mat-label>Initial Balance</mat-label>
      <input matInput formControlName="initialBalance" type="number" step="0.5" placeholder="e.g., 15.0">
      <mat-error *ngIf="leaveBalanceForm.get('initialBalance')?.hasError('required')">
        Initial Balance is required
      </mat-error>
      <mat-error *ngIf="leaveBalanceForm.get('initialBalance')?.hasError('min')">
        Initial Balance must be non-negative
      </mat-error>
    </mat-form-field>

    <div class="flex justify-center">
      <button mat-raised-button color="primary" type="submit" [disabled]="leaveBalanceForm.invalid" class="px-6 py-2">
        Create Balance
      </button>
    </div>

    <div *ngIf="errorMessage" class="mt-4 text-red-600 text-center">
      {{ errorMessage }}
    </div>
    <div *ngIf="successMessage" class="mt-4 text-green-600 text-center">
      {{ successMessage }}
    </div>
</form>
</mat-card> 
</div>