:host {
  padding: 10px 30px;
  position: relative;
  height: 60px;
  display: flex;
  align-items: center;
  width: 100%;

  form {
    width: 100%;
  }
}


header {
  display: flex;
  align-items: center;
  gap: 10px;
  justify-content: space-between;

  .btn-menu mat-icon {
    font-size: 24px;
    font-variation-settings: 'FILL' 1, 'wght' 500, 'GRAD' 0, 'opsz' 48;
    transform: scaleY(1.1);
  }

  .btn-avatar {
    border: none;
    background-color: var(--theme-80);
    width: 30.57px;
    height: 30.57px;
    font-size: var(--12px);
    font-weight: 600;
    display: flex;
    align-items: center;
    line-height: 1em;
    justify-content: center;
    color: #fff;
    border-radius: 50%;
    cursor: pointer;
    margin-left: auto;
  }
}



:host ::ng-deep {
  header {
    .mat-mdc-form-field-subscript-wrapper {
      display: none;
    }

    .mat-badge-content {
      translate: -2px 4px;
      display: flex !important;
      align-items: center;
    }
  }
}

::ng-deep .accounts .mdc-list-item__primary-text {
  width: 100%;
  gap: 10px;
}

.shimmer {
  &.group-name {
    height: 25px;
    width: 220px;
  }
}

.hide {
  display: none !important;
}

.filters {
  position: absolute;
  background: #fff;
  max-width: 400px;
  width: 100%;
  border-radius: 0 0 8px 0px;
  top: 63px;
  z-index: 2;
  box-shadow: 20px 40px 40px -40px rgba(0, 0, 0, 0.1);
  left: 9px;
  padding: 20px 20px 15px 20px;
  height: fit-content;
  will-change: height;
  animation: expand .2s ease-in-out, fade-in .2s ease-in;

  mat-form-field {
    min-width: 100%;
  }
}


@keyframes expand {
  0% {
    height: 45px;
    overflow: hidden;
  }

  100% {
    height: 150px;
    overflow: hidden;
  }
}

@keyframes fade-in {
  0% {
    opacity: 0.1;
  }

  100% {
    opacity: 1;
  }
}

.name {
  // text-align: center;
  font-weight: 600;
}

// .lang-toggle {
//   // margin-left: 1rem;

//   ::ng-deep .mat-button-toggle {
//     border: 1px solid #ccc;
//     // padding: 0 1rem;
//     font-weight: 500;
//     background-color: white;
//   }

//   ::ng-deep .mat-button-toggle-checked {
//     background-color: #3f51b5;
//     color: white;
//     border-color: #3f51b5;
//   }
// }


.custom-toggle-group {
  display: flex;
  // gap: 8px;
  font-size: small;
}

.custom-toggle-group button {
  // padding: 6px 16px;
  color: var(--theme-90);
  border: 1px solid #ccc;
  background-color: white;
  cursor: pointer;
  // border-radius: 4px;
  font-weight: bold;
}

.custom-toggle-group button.selected {
  background-color: var(--theme-90);
  color: white;
  border-color: var(--theme-90);
}

.leftbtn {
  border-top-left-radius: 50px;
  border-bottom-left-radius: 50px;
}

.rightbtn {
  border-top-right-radius: 50px;
  border-bottom-right-radius: 50px;
}

.profile ,.hederlogo {
  display: flex;
  align-items: center;
  gap: 10px;
}

.profileName {
  font-size: .875rem;
  font-family: Overpass, sans-serif;
  font-weight: bold;
  -webkit-font-smoothing: antialiased;
  color: var(--theme-90);

}