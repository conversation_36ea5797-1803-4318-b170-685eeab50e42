export interface GunnyAllocations {
    id:number
    requestedType: number
    entryTs:string
    tokenDate: string
    tokenNo: string
    dpcId: number
    farmerId: number
    landId: [],
    productId:number
    varietyId: number
    seasonId: number
    farmerName:string
    productName: string
    varietyName: string
    seasonName: string
    dpcName: string
    landViewModel: [
      {
        id: number
        farmerName: null,
        subDivsion: string
        landTypeName: string
        expectedYield:number
        surveyNo: number
        regionName: string
        unitName: string
        talukName: string
        blockName:string
        villageName: string
      }
    ],
    noofBags: number
    noofProcuredBags:number
    procurementStatusType: number
    procurementStatusTypeName: string
    requestedTypeName: string
  }

  
  export interface QrCode {
    id: number
    gunnyQrCode: string
    gunnyName :  number
  }

export interface GunnyAllocation {
    id: number
    From: number
    To: number
    GunnyType: number
    TotalCount: number
}


export type GunnyAllocationResponse = GunnyAllocation[]