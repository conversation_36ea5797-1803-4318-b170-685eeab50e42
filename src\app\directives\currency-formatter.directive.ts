import {Directive, ElementRef, HostListener} from '@angular/core';
import {NgControl} from '@angular/forms';

@Directive({
  selector: '[appCurrencyFormatter]'
})
export class CurrencyFormatterDirective {
  regexStr = '^[0-9]*\\.?[0-9]*$';

  private regex: RegExp = new RegExp(/^[0-9]+(\.[0-9]*){0,1}$/g);

  ngOnInit() {
    this.formatInput(this.el.nativeElement.value); // Format initial value if any
  }

  @HostListener('input', ['$event.target.value'])
  onInput(value: string) {
    this.formatInput(value);
  }

  @HostListener('blur', ['$event.target.value'])
  onBlur(value: string) {
    this.formatInput(value); // Ensure formatting is applied on blur as well.
  }


  constructor(private el: ElementRef, private control: NgControl) { }
  @HostListener('keypress', ['$event']) onKeyPress(event: KeyboardEvent) {
    const decimal = (event.target as any).value.includes(".");
    const isDot = event.key === '.'

    if (isDot && decimal) {
      return false;
    }
    return new RegExp(this.regexStr).test(event.key);
  }

  @HostListener('paste', ['$event']) blockPaste(event: KeyboardEvent) {
    this.validateFields(event);
  }

  validateFields(event: KeyboardEvent) {
    setTimeout(() => {
      const cleanedValue = this.el.nativeElement.value.replace(/[^0-9.]/g,'');
      const decimalIndex = cleanedValue.indexOf('.')
      console.log(decimalIndex)
      let partOne;
      let partTwo;
      if (decimalIndex !== -1) {
        partOne = cleanedValue.slice(0, decimalIndex)
        partTwo = cleanedValue.slice(decimalIndex + 1).replace(/[^0-9]/g, '')
      } else {
        partOne = cleanedValue.slice(0, -1)
        partTwo = ""
      }
      
      let integerPart = partOne;

      let lastThree = integerPart.slice(-3);
      let otherNumbers = integerPart.slice(0, -3);

      if (otherNumbers !== '') {
        lastThree = ',' + lastThree;
        otherNumbers = otherNumbers.replace(/\B(?=(\d{2})+(?!\d))/g, ",");
      }

      partOne = otherNumbers + lastThree;

      if (partTwo) {
        this.el.nativeElement.value = partOne + '.' + partTwo;
      } else {
        this.el.nativeElement.value = partOne
      }
      event.preventDefault();
    }, 100);
  }

  private formatInput(value: string) {
    if (!value) {
      this.control.control?.setValue('');
      return;
    }

    // Remove existing commas and validate basic decimal format
    const cleanedValue = value.replace(/,/g, '');

    if (!String(cleanedValue).match(this.regex)) {
      if (this.control.control) {

        //revert to previous value if invalid
        this.control.control.setValue(this.el.nativeElement.value);
      }

      // Stop if the basic decimal validation fails
      return;
    }

    // Format with commas
    const parts = cleanedValue.split('.');
    let integerPart = parts[0];

    // Extract last 3 digits
    let lastThree = integerPart.slice(-3);
    let otherNumbers = integerPart.slice(0, -3);

    if (otherNumbers !== '') {
      lastThree = ',' + lastThree;
      otherNumbers = otherNumbers.replace(/\B(?=(\d{2})+(?!\d))/g, ",");
    }

    parts[0] = otherNumbers + lastThree;
    const formattedValue = parts.join('.');

    if (this.control.control) {
      this.control.control.setValue(formattedValue, { emitEvent: false });
    }

    this.el.nativeElement.value = formattedValue;
  }
}
