import { inject, Injectable } from '@angular/core';
import { DataService } from '../../data.service';
import { Response } from '../../../models/x_models/api-response';
import { Taluk, TalukResponse } from '../../../models/x_models/masters/taluk';

@Injectable({
    providedIn: 'root'
})
export class TalukService {
    dataService = inject(DataService)

    create(data: Taluk) {
        return this.dataService.post<Response>("/taluk", data)
    }

    get() {
        return this.dataService.get<TalukResponse>("/taluk")
    }

    getById(id: number) {
        return this.dataService.get<Taluk>(`/taluk/${id}`)
    }

    delete(id: number) {
        return this.dataService.delete<Response>(`/taluk/${id}`)
    }
}
