{"name": "tncsc", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^19.0.0", "@angular/cdk": "^19.0.4", "@angular/common": "^19.0.0", "@angular/compiler": "^19.0.0", "@angular/core": "^19.0.0", "@angular/forms": "^19.0.0", "@angular/google-maps": "^19.0.5", "@angular/material": "^19.0.4", "@angular/platform-browser": "^19.0.0", "@angular/platform-browser-dynamic": "^19.0.0", "@angular/router": "^19.0.0", "@ngx-translate/core": "^16.0.4", "@ngx-translate/http-loader": "^16.0.1", "@types/intl-tel-input": "^18.1.4", "intl-tel-input": "^18.1.1", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "moment": "^2.30.1", "ngx-filesaver": "^19.0.0", "ngx-gauge": "^11.0.0", "ngx-toastr": "^19.0.0", "rxjs": "~7.8.0", "sweetalert2": "^11.15.10", "tslib": "^2.3.0", "xlsx": "^0.18.5", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.0.1", "@angular/cli": "^19.0.1", "@angular/compiler-cli": "^19.0.0", "@types/jasmine": "~5.1.0", "axios": "^1.8.4", "jasmine-core": "~5.4.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.6.2", "@types/lodash": "^4.17.13"}}